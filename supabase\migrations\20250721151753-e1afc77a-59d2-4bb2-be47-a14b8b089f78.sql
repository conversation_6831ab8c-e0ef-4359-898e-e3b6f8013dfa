-- Migration: Add deal sub-categorization system
-- Creates deal_categories and deals_deal_categories tables with enforced business rules

-- Create deal_categories table for sub-categories within macro-categories
CREATE TABLE public.deal_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category_id UUID NOT NULL REFERENCES public.categories(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    -- Ensure unique sub-category names within each macro-category
    UNIQUE(category_id, name)
);

-- Create many-to-many join table between deals and deal_categories
CREATE TABLE public.deals_deal_categories (
    deal_id UUID NOT NULL REFERENCES public.deals(id) ON DELETE CASCADE,
    deal_category_id UUID NOT NULL REFERENCES public.deal_categories(id) ON DELETE CASCADE,
    -- Composite primary key prevents duplicate associations
    PRIMARY KEY (deal_id, deal_category_id)
);

-- Add performance indexes
CREATE INDEX idx_deal_categories_category_id ON public.deal_categories(category_id);
CREATE INDEX idx_deals_deal_categories_deal_category_id ON public.deals_deal_categories(deal_category_id);

-- Create trigger function to enforce business rule:
-- deals can only link to deal_categories whose category_id matches business.category_id
CREATE OR REPLACE FUNCTION public.validate_deal_category_assignment()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if deal_category.category_id matches business.category_id for this deal
    IF NOT EXISTS (
        SELECT 1 
        FROM public.deals d
        JOIN public.businesses b ON d.business_id = b.id
        JOIN public.deal_categories dc ON dc.id = NEW.deal_category_id
        WHERE d.id = NEW.deal_id 
        AND b.category_id = dc.category_id
    ) THEN
        RAISE EXCEPTION 'Deal category assignment violates business rule: deal_category.category_id must match business.category_id';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to enforce validation on insert/update
CREATE TRIGGER validate_deal_category_assignment_trigger
    BEFORE INSERT OR UPDATE ON public.deals_deal_categories
    FOR EACH ROW
    EXECUTE FUNCTION public.validate_deal_category_assignment();

-- Enable RLS on both new tables
ALTER TABLE public.deal_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.deals_deal_categories ENABLE ROW LEVEL SECURITY;

-- RLS policies for deal_categories (mirror deals table policies)
CREATE POLICY "Deal categories can be read by everyone" 
ON public.deal_categories FOR SELECT 
USING (true);

CREATE POLICY "Deal categories can be created by business owners" 
ON public.deal_categories FOR INSERT 
WITH CHECK (
    category_id IN (
        SELECT b.category_id 
        FROM public.businesses b 
        WHERE b.owner_id = auth.uid()
    )
);

CREATE POLICY "Deal categories can be updated by business owners" 
ON public.deal_categories FOR UPDATE 
USING (
    category_id IN (
        SELECT b.category_id 
        FROM public.businesses b 
        WHERE b.owner_id = auth.uid()
    )
);

CREATE POLICY "Deal categories can be deleted by business owners" 
ON public.deal_categories FOR DELETE 
USING (
    category_id IN (
        SELECT b.category_id 
        FROM public.businesses b 
        WHERE b.owner_id = auth.uid()
    )
);

-- RLS policies for deals_deal_categories (mirror deals table policies via business ownership)
CREATE POLICY "Deal category associations can be read by everyone" 
ON public.deals_deal_categories FOR SELECT 
USING (true);

CREATE POLICY "Deal category associations can be created by business owners" 
ON public.deals_deal_categories FOR INSERT 
WITH CHECK (
    deal_id IN (
        SELECT d.id 
        FROM public.deals d
        JOIN public.businesses b ON d.business_id = b.id
        WHERE b.owner_id = auth.uid()
    )
);

CREATE POLICY "Deal category associations can be updated by business owners" 
ON public.deals_deal_categories FOR UPDATE 
USING (
    deal_id IN (
        SELECT d.id 
        FROM public.deals d
        JOIN public.businesses b ON d.business_id = b.id
        WHERE b.owner_id = auth.uid()
    )
);

CREATE POLICY "Deal category associations can be deleted by business owners" 
ON public.deals_deal_categories FOR DELETE 
USING (
    deal_id IN (
        SELECT d.id 
        FROM public.deals d
        JOIN public.businesses b ON d.business_id = b.id
        WHERE b.owner_id = auth.uid()
    )
);