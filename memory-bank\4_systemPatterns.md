# System Patterns

## Architecture Overview

```mermaid
flowchart TD
    Client[Client Application] --> API[Supabase API]
    API --> Auth[Authentication]
    API --> DB[Database]
    API --> Storage[File Storage]
    
    subgraph FrontEnd
        React[React Application]
        Router[React Router]
        Forms[React Hook Form]
        Query[React Query]
        Zustand[Zustand State]
        UI[shadcn/ui Components]
        
        React --> Router
        React --> Forms
        React --> Query
        React --> Zustand
        React --> UI
    end
    
    subgraph BackEnd
        RLS[Row Level Security]
        Functions[Supabase Functions]
        Triggers[Database Triggers]
        
        RLS --> Functions
        RLS --> Triggers
    end
    
    Client --> FrontEnd
    API --> BackEnd
```

## Key Technical Decisions

### Frontend Architecture
- **Component Structure**: The application follows a modular component architecture with clear separation of concerns.
- **Routing System**: React Router is used for navigation with protected routes for authenticated content.
- **Layout Pattern**: Consistent layout components (MainLayout) provide uniform structure across pages.
- **State Management**: 
  - React Query for server state
  - Zustand for persistent application state (with localStorage persistence)
  - Context API for transient application-wide state
  - Local state for component-specific concerns

### Backend Architecture
- **Database Design**: Relational database with proper foreign key relationships and constraints.
- **Multi-tenancy Approach**: Row-level security policies in Supabase for data isolation.
- **Authentication Flow**: JWT-based authentication through Supabase Auth.
- **API Integration**: Direct database access through Supabase client with RLS protection.

## Component Relationships

### UI Component Hierarchy

```mermaid
flowchart TD
    App --> MainLayout
    MainLayout --> Sidebar
    MainLayout --> Header
    MainLayout --> Content[Page Content]
    
    Sidebar --> Navigation
    Sidebar --> BusinessSelector
    Sidebar --> UserInfo
    
    BusinessSelector --> BusinessStore[Zustand Store]
    Header --> BusinessStore
    
    subgraph Forms
        ProfileForm
        DealForm
        BookingForm
    end
    
    subgraph Components
        Card
        Avatar
        Button
        Input
        TimeSlots
        Calendar
    end
    
    Content --> Forms
    Content --> Components
    Content --> BusinessStore
```

### Data Flow

```mermaid
flowchart LR
    User[User Action] --> Component[UI Component]
    Component --> Form[Form Validation]
    Component --> ZustandAction[Zustand Action]
    
    Form --> Query[React Query]
    ZustandAction --> ZustandStore[Zustand Store]
    ZustandStore --> PersistMiddleware[Persist Middleware]
    Query --> API[Supabase API]
    API --> Database[Database]
    
    Database --> API
    API --> Query
    Query --> Component
    ZustandStore --> Component
    Component --> User[UI Update]
    
    PersistMiddleware --> LocalStorage[LocalStorage]
    LocalStorage --> PersistMiddleware
```

## Design Patterns

### Form Handling Pattern
All forms in the application follow a consistent pattern:
1. Schema definition with Zod
2. Form initialization with react-hook-form and zodResolver
3. Field components with proper validation and error handling
4. Submission handler with loading state management
5. Success/error feedback through toast notifications

### Authentication Pattern
The authentication flow follows these steps:
1. User submits credentials
2. Supabase Auth validates and returns session token
3. Token is stored and managed by Supabase client
4. Protected routes check authentication status
5. Authorized API calls include authentication token

### Multi-tenant Data Access Pattern
Data access follows strict tenant isolation:
1. All database tables include a tenant_id column
2. RLS policies verify tenant_id matches current user's organization
3. All queries automatically include tenant filters through RLS
4. Cross-tenant operations are only available to authorized roles

### State Management Pattern
The application uses a hybrid state management approach:
1. **Zustand Stores**: For persistent application state with complex interactions
   - Created with clear interfaces and typed state
   - Organized by domain (e.g., businessStore, userStore)
   - Uses persist middleware to maintain state across sessions
   - Provides actions for state modification and side effects
2. **React Query**: For server state management
   - Handles data fetching, caching, and synchronization
   - Provides loading, error, and success states
   - Uses query keys for proper cache invalidation
3. **Context API**: For non-persistent shared state
   - Used for theme, notifications, and other UI concerns
   - Provided at appropriate component levels to minimize re-renders
4. **Local State**: For component-specific concerns
   - useState for simple component state
   - useReducer for more complex local state logic

## Common Code Structures

### Page Component Structure
```typescript
const PageName = () => {
  // Zustand store access
  const selectedBusiness = useBusinessStore(state => state.selectedBusiness);
  
  // Data fetching with React Query
  const { data, isLoading, error } = useQuery({
    queryKey: ['data', selectedBusiness?.id],
    queryFn: async () => {...},
    enabled: !!selectedBusiness
  })
  
  // Component state
  const [localState, setLocalState] = useState(...)
  
  // Event handlers
  const handleAction = () => {...}
  
  // Effects
  useEffect(() => {...}, [dependencies])
  
  // Render with loading/error states
  if (isLoading) return <LoadingComponent />
  if (error) return <ErrorComponent />
  
  return (
    <MainLayout>
      <PageContent />
    </MainLayout>
  )
}
```

### Form Component Structure
```typescript
// Schema definition
const formSchema = z.object({...})

// Component
const FormComponent = () => {
  // Initialize form
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {...}
  })
  
  // Submit handler
  const onSubmit = async (data) => {
    try {
      // Process form submission
      // Show success toast
    } catch (error) {
      // Show error toast
    }
  }
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        {/* Form fields */}
      </form>
    </Form>
  )
}
```

### Zustand Store Structure
```typescript
// Define types
interface BusinessState {
  businesses: Business[];
  selectedBusiness: Business | null;
  isLoading: boolean;
  fetchBusinesses: (userId: string) => Promise<void>;
  selectBusiness: (business: Business) => void;
}

// Create store
export const useBusinessStore = create<BusinessState>()(
  persist(
    (set, get) => ({
      businesses: [],
      selectedBusiness: null,
      isLoading: false,
      
      fetchBusinesses: async (userId) => {
        set({ isLoading: true });
        try {
          // Fetch data
          set({ businesses: data, isLoading: false });
        } catch (error) {
          console.error(error);
          set({ businesses: [], isLoading: false });
        }
      },
      
      selectBusiness: (business) => {
        set({ selectedBusiness: business });
      }
    }),
    {
      name: 'store-name',
      partialize: (state) => ({
        // Only persist specific properties
        selectedBusiness: state.selectedBusiness
      })
    }
  )
)
```
