import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Save, Mic, User, MessageSquare, Settings } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import MainLayout from "@/layouts/MainLayout";
import { useBusinessStore } from "@/store/businessStore";
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";
import { useToast } from "@/hooks/use-toast";
import { useQueryClient } from "@tanstack/react-query";

type AIAgent = Database['public']['Tables']['ai_business_agents']['Row'];

const AgentConfig = () => {
  const { agentId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const selectedBusiness = useBusinessStore(state => state.selectedBusiness);
  const queryClient = useQueryClient();
  
  const [agent, setAgent] = useState<AIAgent | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (agentId && selectedBusiness) {
      fetchAgent();
    }
  }, [agentId, selectedBusiness]);

  const fetchAgent = async () => {
    if (!agentId || !selectedBusiness) return;

    try {
      const { data, error } = await supabase
        .from('ai_business_agents')
        .select('*')
        .eq('id', agentId)
        .eq('business_id', selectedBusiness.id)
        .single();

      if (error) throw error;
      setAgent(data);
    } catch (error) {
      console.error('Error fetching agent:', error);
      toast({
        title: "Errore",
        description: "Impossibile caricare l'agente",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!agent) return;

    setSaving(true);
    try {
      const { error } = await supabase
        .from('ai_business_agents')
        .update({
          name: agent.name,
          description: agent.description,
          instructions: agent.instructions,
          personality_style: agent.personality_style,
          voice_id: agent.voice_id,
          is_active: agent.is_active,
          updated_at: new Date().toISOString()
        })
        .eq('id', agent.id);

      if (error) throw error;

      // Invalidate agent limits cache to reflect changes immediately
      queryClient.invalidateQueries({ 
        queryKey: ["agentLimits", selectedBusiness?.id] 
      });

      toast({
        title: "Successo",
        description: "Configurazione agente salvata",
      });
    } catch (error) {
      console.error('Error saving agent:', error);
      toast({
        title: "Errore",
        description: "Impossibile salvare la configurazione",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const getAgentIcon = (type: string) => {
    switch (type) {
      case 'booking':
        return MessageSquare;
      case 'customer_support':
        return MessageSquare;
      case 'sales':
        return User;
      default:
        return Bot;
    }
  };

  const getAgentTypeLabel = (type: string) => {
    switch (type) {
      case 'booking':
        return 'Prenotazioni';
      case 'customer_support':
        return 'Supporto Clienti';
      case 'sales':
        return 'Vendite';
      default:
        return type;
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-6 max-w-4xl">
          <div className="animate-pulse space-y-6">
            <div className="h-8 w-64 bg-gray-300 rounded"></div>
            <div className="h-64 bg-gray-300 rounded"></div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!agent) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-6 max-w-4xl">
          <div className="text-center py-16">
            <Bot className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Agente non trovato</h3>
            <Button onClick={() => navigate('/ai-agents')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Torna agli Agenti
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  const IconComponent = getAgentIcon(agent.agent_type);

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-6 max-w-4xl">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="ghost" onClick={() => navigate('/ai-agents')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Torna agli Agenti
          </Button>
          <div className="flex items-center gap-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={agent.avatar_url || undefined} />
              <AvatarFallback>
                <IconComponent className="h-6 w-6" />
              </AvatarFallback>
            </Avatar>
            <div>
              <h1 className="text-2xl font-bold">Configura {agent.name}</h1>
              <p className="text-gray-600">{getAgentTypeLabel(agent.agent_type)}</p>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          {/* Informazioni Generali */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Informazioni Generali
              </CardTitle>
              <CardDescription>
                Configura le informazioni di base dell'agente
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nome Agente</Label>
                  <Input
                    id="name"
                    value={agent.name}
                    onChange={(e) => setAgent({ ...agent, name: e.target.value })}
                    placeholder="Nome dell'agente"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">Stato</Label>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={agent.is_active}
                      onCheckedChange={(checked) => setAgent({ ...agent, is_active: checked })}
                    />
                    <span className="text-sm">
                      {agent.is_active ? 'Attivo' : 'Inattivo'}
                    </span>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Descrizione</Label>
                <Textarea
                  id="description"
                  value={agent.description || ''}
                  onChange={(e) => setAgent({ ...agent, description: e.target.value })}
                  placeholder="Descrizione dell'agente..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Configurazione Voce */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mic className="h-5 w-5" />
                Configurazione Voce
              </CardTitle>
              <CardDescription>
                Imposta la voce e lo stile di personalità dell'agente
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="voice">ID Voce</Label>
                  <Input
                    id="voice"
                    value={agent.voice_id || ''}
                    onChange={(e) => setAgent({ ...agent, voice_id: e.target.value })}
                    placeholder="ID della voce (es. alloy, echo, fable)"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="personality">Stile Personalità</Label>
                  <Select
                    value={agent.personality_style || ''}
                    onValueChange={(value) => setAgent({ ...agent, personality_style: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleziona stile" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="professionale">Professionale</SelectItem>
                      <SelectItem value="amichevole">Amichevole</SelectItem>
                      <SelectItem value="formale">Formale</SelectItem>
                      <SelectItem value="casual">Casual</SelectItem>
                      <SelectItem value="entusiasta">Entusiasta</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Istruzioni */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Istruzioni e Comportamento
              </CardTitle>
              <CardDescription>
                Definisci come l'agente deve comportarsi e rispondere
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="instructions">Istruzioni</Label>
                <Textarea
                  id="instructions"
                  value={agent.instructions || ''}
                  onChange={(e) => setAgent({ ...agent, instructions: e.target.value })}
                  placeholder="Inserisci le istruzioni dettagliate per l'agente..."
                  rows={8}
                  className="font-mono text-sm"
                />
                <p className="text-sm text-gray-500">
                  Sii specifico sulle responsabilità, il tono di voce e i processi che l'agente deve seguire.
                </p>
              </div>
            </CardContent>
          </Card>

          <Separator />

          {/* Azioni */}
          <div className="flex justify-end gap-4">
            <Button variant="outline" onClick={() => navigate('/ai-agents')}>
              Annulla
            </Button>
            <Button onClick={handleSave} disabled={saving}>
              <Save className="h-4 w-4 mr-2" />
              {saving ? 'Salvando...' : 'Salva Configurazione'}
            </Button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default AgentConfig;