import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/integrations/supabase/types';

type PricingTier = Database['public']['Tables']['pricing_tiers']['Row'];
type BusinessSubscription = Database['public']['Tables']['business_subscriptions']['Row'];
type AgentDefinition = Database['public']['Tables']['ai_business_agents_profile']['Row'];

export interface PricingTierWithSubscription extends PricingTier {
  current?: boolean;
  agentDefinitions?: AgentDefinition[];
}

/**
 * Hook per ottenere tutte le definizioni degli agenti
 */
export const useAgentDefinitions = () => {
  return useQuery({
    queryKey: ['agentDefinitions'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('ai_business_agents_profile')
        .select('*')
        .order('agent_type', { ascending: true });

      if (error) throw error;
      return data || [];
    },
    staleTime: 10 * 60 * 1000, // 10 minuti - le definizioni cambiano molto lentamente
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook per ottenere tutti i piani tariffari disponibili
 */
export const usePricingTiers = () => {
  return useQuery({
    queryKey: ['pricingTiers'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pricing_tiers')
        .select('*')
        .eq('is_active', true)
        .order('price_monthly', { ascending: true });

      if (error) throw error;
      return data || [];
    },
    staleTime: 10 * 60 * 1000, // 10 minuti - i prezzi cambiano molto lentamente
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook per ottenere la sottoscrizione corrente di un utente
 */
export const useBusinessSubscription = (userId: string | undefined) => {
  return useQuery({
    queryKey: ['businessSubscription', userId],
    queryFn: async () => {
      if (!userId) {
        throw new Error('User ID is required');
      }

      const { data, error } = await supabase
        .from('business_subscriptions')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .maybeSingle();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minuti
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook combinato per ottenere i piani tariffari con indicazione del piano corrente e definizioni degli agenti
 */
export const usePricingTiersWithCurrent = (userId: string | undefined) => {
  const { data: tiers, isLoading: tiersLoading, error: tiersError } = usePricingTiers();
  const { data: subscription, isLoading: subscriptionLoading, error: subscriptionError } = useBusinessSubscription(userId);
  const { data: agentDefinitions, isLoading: agentDefinitionsLoading, error: agentDefinitionsError } = useAgentDefinitions();

  const tiersWithCurrent = React.useMemo(() => {
    if (!tiers) return [];
    
    return tiers.map(tier => ({
      ...tier,
      current: subscription?.tier_type === tier.tier_type,
      agentDefinitions: agentDefinitions?.filter(agent => {
        const allowedAgents = tier.allowed_agents as string[] | null;
        return allowedAgents?.includes(agent.id) || false;
      }) || []
    }));
  }, [tiers, subscription, agentDefinitions]);

  return {
    data: tiersWithCurrent,
    isLoading: tiersLoading || subscriptionLoading || agentDefinitionsLoading,
    error: tiersError || subscriptionError || agentDefinitionsError,
    subscription
  };
};

/**
 * Funzione helper per mappare i tipi di agenti dal database alle etichette italiane
 */
export const getAgentTypeLabel = (agentType: string): string => {
  const agentTypeLabels: Record<string, string> = {
    'booking': 'Prenotazioni',
    'customer_support': 'Supporto Clienti',
    'sales': 'Vendite',
    'marketing': 'Marketing',
    'data_analysis': 'Analisi Dati'
  };

  return agentTypeLabels[agentType] || agentType;
};

/**
 * Funzione helper per formattare il prezzo
 */
export const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('it-IT', {
    style: 'currency',
    currency: 'EUR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(price);
}; 