
import { useState } from "react";
import { Check, ChevronsUpDown, Edit, X, Save } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useRoleManagement, type UserRole } from "@/hooks/useRoleManagement";

const roleLabels: Record<UserRole, string> = {
  user: "Utente",
  admin: "Amministratore",
  business_owner: "Proprietario Business"
};

interface RoleSelectorProps {
  currentRole: UserRole;
  userId: string;
  onRoleChange?: () => void;
}

export const RoleSelector = ({ currentRole, userId, onRoleChange }: RoleSelectorProps) => {
  const [open, setOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<UserRole>(currentRole);
  const [isEditing, setIsEditing] = useState(false);
  const { updateUserRole, isUpdating } = useRoleManagement();

  const handleRoleChange = async (newRole: UserRole) => {
    if (newRole === selectedRole) {
      setOpen(false);
      return;
    }

    const success = await updateUserRole(userId, newRole);
    if (success) {
      setSelectedRole(newRole);
      onRoleChange?.();
      setIsEditing(false);
    }
    setOpen(false);
  };

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setOpen(false);
    setSelectedRole(currentRole);
  };

  if (!isEditing) {
    return (
      <div className="flex items-center gap-2">
        <span className="text-sm">{roleLabels[selectedRole]}</span>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleEditClick}
          className="h-6 w-6 p-0"
          disabled={isUpdating}
        >
          <Edit className="h-3 w-3" />
        </Button>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-[180px] justify-between h-8 text-xs"
            disabled={isUpdating}
          >
            {roleLabels[selectedRole]}
            <ChevronsUpDown className="ml-2 h-3 w-3 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[180px] p-0">
          <Command>
            <CommandInput placeholder="Cerca ruolo..." className="h-8" />
            <CommandList>
              <CommandEmpty>Nessun ruolo trovato.</CommandEmpty>
              <CommandGroup>
                {Object.entries(roleLabels).map(([role, label]) => (
                  <CommandItem
                    key={role}
                    value={role}
                    onSelect={() => handleRoleChange(role as UserRole)}
                    className="text-xs"
                  >
                    <Check
                      className={cn(
                        "mr-2 h-3 w-3",
                        selectedRole === role ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {label}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      <Button
        variant="ghost"
        size="sm"
        onClick={handleCancelEdit}
        className="h-6 w-6 p-0"
        disabled={isUpdating}
      >
        <X className="h-3 w-3" />
      </Button>
    </div>
  );
};
