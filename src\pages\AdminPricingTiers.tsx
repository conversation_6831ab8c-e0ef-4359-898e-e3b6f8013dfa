import { useState, useEffect } from "react";
import { Plus, Edit, Trash2, Eye } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import MainLayout from "@/layouts/MainLayout";
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";
import { useToast } from "@/hooks/use-toast";

type PricingTier = Database['public']['Tables']['pricing_tiers']['Row'];
type PricingTierInsert = Database['public']['Tables']['pricing_tiers']['Insert'];
type PricingTierType = Database['public']['Enums']['pricing_tier_type'];
type AgentDefinition = Database['public']['Tables']['ai_business_agents_profile']['Row'];

const AdminPricingTiers = () => {
  const [tiers, setTiers] = useState<PricingTier[]>([]);
  const [agentDefinitions, setAgentDefinitions] = useState<AgentDefinition[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedTier, setSelectedTier] = useState<PricingTier | null>(null);
  const [formData, setFormData] = useState<Partial<PricingTierInsert>>({
    name: '',
    tier_type: 'basic',
    price_monthly: 0,
    price_yearly: 0,
    allowed_agents: [],
    features: [],
    is_active: true
  });
  const [featuresText, setFeaturesText] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    fetchTiers();
    fetchAgentDefinitions();
  }, []);

  const fetchAgentDefinitions = async () => {
    try {
      const { data, error } = await supabase
        .from('ai_business_agents_profile')
        .select('*')
        .order('name', { ascending: true });

      if (error) throw error;
      setAgentDefinitions(data || []);
    } catch (error) {
      console.error('Error fetching agent definitions:', error);
      toast({
        title: "Errore",
        description: "Impossibile caricare le definizioni degli agenti",
        variant: "destructive",
      });
    }
  };

  const fetchTiers = async () => {
    try {
      const { data, error } = await supabase
        .from('pricing_tiers')
        .select('*')
        .order('tier_type', { ascending: true });

      if (error) throw error;
      setTiers(data || []);
    } catch (error) {
      console.error('Error fetching pricing tiers:', error);
      toast({
        title: "Errore",
        description: "Impossibile caricare i piani tariffari",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const parseFeatures = (featuresText: string): string[] => {
    return featuresText
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);
  };

  const handleCreate = async () => {
    if (!formData.name || !formData.tier_type) {
      toast({
        title: "Errore",
        description: "Nome e tipo piano sono obbligatori",
        variant: "destructive",
      });
      return;
    }

    try {
      const features = parseFeatures(featuresText);
      const { error } = await supabase
        .from('pricing_tiers')
        .insert([{
          ...formData,
          features
        } as PricingTierInsert]);

      if (error) throw error;

      toast({
        title: "Successo",
        description: "Piano tariffario creato con successo",
      });

      setIsCreateDialogOpen(false);
      resetForm();
      fetchTiers();
    } catch (error) {
      console.error('Error creating pricing tier:', error);
      toast({
        title: "Errore",
        description: "Impossibile creare il piano tariffario",
        variant: "destructive",
      });
    }
  };

  const handleUpdate = async () => {
    if (!selectedTier || !formData.name || !formData.tier_type) {
      toast({
        title: "Errore",
        description: "Nome e tipo piano sono obbligatori",
        variant: "destructive",
      });
      return;
    }

    try {
      const features = parseFeatures(featuresText);
      const { error } = await supabase
        .from('pricing_tiers')
        .update({
          ...formData,
          features,
          allowed_agents: formData.allowed_agents || [],
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedTier.id);

      if (error) throw error;

      toast({
        title: "Successo",
        description: "Piano tariffario aggiornato con successo",
      });

      setIsEditDialogOpen(false);
      setSelectedTier(null);
      resetForm();
      fetchTiers();
    } catch (error) {
      console.error('Error updating pricing tier:', error);
      toast({
        title: "Errore",
        description: "Impossibile aggiornare il piano tariffario",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (tier: PricingTier) => {
    if (!confirm(`Sei sicuro di voler eliminare il piano "${tier.name}"?`)) {
      return;
    }

    try {
      const { error } = await supabase
        .from('pricing_tiers')
        .delete()
        .eq('id', tier.id);

      if (error) throw error;

      toast({
        title: "Successo",
        description: "Piano tariffario eliminato con successo",
      });

      fetchTiers();
    } catch (error) {
      console.error('Error deleting pricing tier:', error);
      toast({
        title: "Errore",
        description: "Impossibile eliminare il piano tariffario",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      tier_type: 'basic',
      price_monthly: 0,
      price_yearly: 0,
      allowed_agents: [],
      features: [],
      is_active: true
    });
    setFeaturesText('');
  };

  const openEditDialog = (tier: PricingTier) => {
    setSelectedTier(tier);
    setFormData({
      name: tier.name,
      tier_type: tier.tier_type,
      price_monthly: tier.price_monthly,
      price_yearly: tier.price_yearly,
      allowed_agents: tier.allowed_agents,
      features: tier.features,
      is_active: tier.is_active
    });
    
    // Convert features array to text
    if (Array.isArray(tier.features)) {
      setFeaturesText(tier.features.join('\n'));
    } else {
      setFeaturesText('');
    }
    
    setIsEditDialogOpen(true);
  };

  const openViewDialog = (tier: PricingTier) => {
    setSelectedTier(tier);
    setIsViewDialogOpen(true);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR'
    }).format(price);
  };

  const getTierBadgeVariant = (tierType: PricingTierType) => {
    switch (tierType) {
      case 'basic':
        return 'secondary';
      case 'professional':
        return 'default';
      case 'enterprise':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const handleAllowedAgentsChange = (agentId: string, checked: boolean) => {
    const currentAllowedAgents = formData.allowed_agents || [];
    if (checked) {
      // Add agent if not already present
      if (!currentAllowedAgents.includes(agentId)) {
        setFormData({
          ...formData,
          allowed_agents: [...currentAllowedAgents, agentId]
        });
      }
    } else {
      // Remove agent
      setFormData({
        ...formData,
        allowed_agents: currentAllowedAgents.filter(id => id !== agentId)
      });
    }
  };

  const getSelectedAgentNames = (allowedAgents: string[] | null) => {
    if (!allowedAgents || allowedAgents.length === 0) return [];
    return agentDefinitions
      .filter(agent => allowedAgents.includes(agent.id))
      .map(agent => {
        const typeLabel = agent.agent_type === 'booking' ? 'Prenotazioni' :
                         agent.agent_type === 'customer_support' ? 'Supporto Clienti' :
                         agent.agent_type === 'sales' ? 'Vendite' :
                         agent.agent_type === 'marketing' ? 'Marketing' :
                         agent.agent_type === 'data_analysis' ? 'Analisi Dati' : agent.agent_type;
        return `${agent.name} - ${typeLabel}`;
      });
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          <div className="animate-pulse">
            <div className="h-8 w-64 bg-gray-300 rounded mb-2"></div>
            <div className="h-4 w-96 bg-gray-300 rounded mb-8"></div>
            <div className="h-64 bg-gray-300 rounded"></div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Piani Tariffari</h1>
            <p className="text-gray-600">Gestisci i piani tariffari del sistema</p>
          </div>
          
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Nuovo Piano
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Crea Nuovo Piano Tariffario</DialogTitle>
                <DialogDescription>
                  Aggiungi un nuovo piano tariffario al sistema
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="create-name">Nome *</Label>
                  <Input
                    id="create-name"
                    value={formData.name || ''}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="Nome del piano"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="create-tier-type">Tipo Piano *</Label>
                  <Select
                    value={formData.tier_type}
                    onValueChange={(value: PricingTierType) => setFormData({ ...formData, tier_type: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleziona tipo piano" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="basic">Basic</SelectItem>
                      <SelectItem value="professional">Professional</SelectItem>
                      <SelectItem value="enterprise">Enterprise</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="create-price-monthly">Prezzo Mensile *</Label>
                    <Input
                      id="create-price-monthly"
                      type="number"
                      step="0.01"
                      value={formData.price_monthly || 0}
                      onChange={(e) => setFormData({ ...formData, price_monthly: parseFloat(e.target.value) || 0 })}
                      placeholder="0.00"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="create-price-yearly">Prezzo Annuale *</Label>
                    <Input
                      id="create-price-yearly"
                      type="number"
                      step="0.01"
                      value={formData.price_yearly || 0}
                      onChange={(e) => setFormData({ ...formData, price_yearly: parseFloat(e.target.value) || 0 })}
                      placeholder="0.00"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Agenti Consentiti</Label>
                  <div className="border rounded-md p-3 max-h-40 overflow-y-auto">
                    {agentDefinitions.length === 0 ? (
                      <p className="text-sm text-gray-500">Nessun agente disponibile</p>
                    ) : (
                      <div className="space-y-2">
                        {agentDefinitions.map((agent) => (
                          <div key={agent.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`create-agent-${agent.id}`}
                              checked={(formData.allowed_agents || []).includes(agent.id)}
                              onCheckedChange={(checked) => handleAllowedAgentsChange(agent.id, checked as boolean)}
                            />
                            <Label htmlFor={`create-agent-${agent.id}`} className="text-sm">
                              {agent.name} - {agent.agent_type === 'booking' && 'Prenotazioni'}
                        {agent.agent_type === 'customer_support' && 'Supporto Clienti'}
                        {agent.agent_type === 'sales' && 'Vendite'}
                        {agent.agent_type === 'marketing' && 'Marketing'}
                        {agent.agent_type === 'data_analysis' && 'Analisi Dati'}
                            </Label>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="create-features">Caratteristiche (una per riga)</Label>
                  <Textarea
                    id="create-features"
                    value={featuresText}
                    onChange={(e) => setFeaturesText(e.target.value)}
                    placeholder="Caratteristica 1&#10;Caratteristica 2&#10;Caratteristica 3"
                    rows={5}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="create-is-active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                  />
                  <Label htmlFor="create-is-active">Piano attivo</Label>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => { setIsCreateDialogOpen(false); resetForm(); }}>
                  Annulla
                </Button>
                <Button onClick={handleCreate}>
                  Crea Piano
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Piani Tariffari</CardTitle>
            <CardDescription>
              Lista di tutti i piani tariffari disponibili
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Piano</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Prezzo Mensile</TableHead>
                  <TableHead>Prezzo Annuale</TableHead>
                  <TableHead>Agenti Consentiti</TableHead>
                  <TableHead>Stato</TableHead>
                  <TableHead className="text-right">Azioni</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tiers.map((tier) => (
                  <TableRow key={tier.id}>
                    <TableCell>
                      <div className="font-medium">{tier.name}</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getTierBadgeVariant(tier.tier_type)}>
                        {tier.tier_type}
                      </Badge>
                    </TableCell>
                    <TableCell>{formatPrice(tier.price_monthly)}</TableCell>
                    <TableCell>{formatPrice(tier.price_yearly)}</TableCell>
                    <TableCell>
                      <div>
                        {getSelectedAgentNames(tier.allowed_agents).length > 0 ? (
                          <div className="flex flex-wrap gap-1">
                            {getSelectedAgentNames(tier.allowed_agents).map((name, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {name}
                              </Badge>
                            ))}
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">Nessuno</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={tier.is_active ? "default" : "secondary"}>
                        {tier.is_active ? "Attivo" : "Inattivo"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex gap-2 justify-end">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openViewDialog(tier)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openEditDialog(tier)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(tier)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {tiers.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <p>Nessun piano tariffario trovato</p>
                <p className="text-sm">Clicca "Nuovo Piano" per aggiungerne uno</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Edit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Modifica Piano Tariffario</DialogTitle>
              <DialogDescription>
                Modifica i dettagli del piano tariffario
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Nome *</Label>
                <Input
                  id="edit-name"
                  value={formData.name || ''}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Nome del piano"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-tier-type">Tipo Piano *</Label>
                <Select
                  value={formData.tier_type}
                  onValueChange={(value: PricingTierType) => setFormData({ ...formData, tier_type: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Seleziona tipo piano" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="basic">Basic</SelectItem>
                    <SelectItem value="professional">Professional</SelectItem>
                    <SelectItem value="enterprise">Enterprise</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-price-monthly">Prezzo Mensile *</Label>
                  <Input
                    id="edit-price-monthly"
                    type="number"
                    step="0.01"
                    value={formData.price_monthly || 0}
                    onChange={(e) => setFormData({ ...formData, price_monthly: parseFloat(e.target.value) || 0 })}
                    placeholder="0.00"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-price-yearly">Prezzo Annuale *</Label>
                  <Input
                    id="edit-price-yearly"
                    type="number"
                    step="0.01"
                    value={formData.price_yearly || 0}
                    onChange={(e) => setFormData({ ...formData, price_yearly: parseFloat(e.target.value) || 0 })}
                    placeholder="0.00"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label>Agenti Consentiti</Label>
                  <div className="border rounded-md p-3 max-h-40 overflow-y-auto">
                    {agentDefinitions.length === 0 ? (
                      <p className="text-sm text-gray-500">Nessun agente disponibile</p>
                    ) : (
                      <div className="space-y-2">
                        {agentDefinitions.map((agent) => (
                          <div key={agent.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`edit-agent-${agent.id}`}
                              checked={(formData.allowed_agents || []).includes(agent.id)}
                              onCheckedChange={(checked) => handleAllowedAgentsChange(agent.id, checked as boolean)}
                            />
                            <Label htmlFor={`edit-agent-${agent.id}`} className="text-sm">
                              {agent.name} - {agent.agent_type === 'booking' && 'Prenotazioni'}
                        {agent.agent_type === 'customer_support' && 'Supporto Clienti'}
                        {agent.agent_type === 'sales' && 'Vendite'}
                        {agent.agent_type === 'marketing' && 'Marketing'}
                        {agent.agent_type === 'data_analysis' && 'Analisi Dati'}
                            </Label>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-features">Caratteristiche (una per riga)</Label>
                  <Textarea
                    id="edit-features"
                    value={featuresText}
                    onChange={(e) => setFeaturesText(e.target.value)}
                    placeholder="Caratteristica 1&#10;Caratteristica 2&#10;Caratteristica 3"
                    rows={5}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="edit-is-active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                  />
                  <Label htmlFor="edit-is-active">Piano attivo</Label>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => { setIsEditDialogOpen(false); resetForm(); }}>
                Annulla
              </Button>
              <Button onClick={handleUpdate}>
                Salva Modifiche
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* View Dialog */}
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Dettagli Piano Tariffario</DialogTitle>
            </DialogHeader>
            {selectedTier && (
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold">{selectedTier.name}</h3>
                  <Badge variant={getTierBadgeVariant(selectedTier.tier_type)} className="mt-1">
                    {selectedTier.tier_type}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Prezzo Mensile</Label>
                    <p className="text-lg font-semibold">{formatPrice(selectedTier.price_monthly)}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Prezzo Annuale</Label>
                    <p className="text-lg font-semibold">{formatPrice(selectedTier.price_yearly)}</p>
                  </div>
                </div>
                
                {Array.isArray(selectedTier.allowed_agents) && selectedTier.allowed_agents.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium">Agenti Consentiti</Label>
                    <div className="mt-2 space-y-1">
                      {getSelectedAgentNames(selectedTier.allowed_agents).map((name, index) => (
                        <Badge key={index} variant="outline" className="mr-1 mb-1">
                          {name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                
                <div>
                  <Label className="text-sm font-medium">Stato</Label>
                  <div className="mt-1">
                    <Badge variant={selectedTier.is_active ? "default" : "secondary"}>
                      {selectedTier.is_active ? "Attivo" : "Inattivo"}
                    </Badge>
                  </div>
                </div>
                
                {Array.isArray(selectedTier.features) && selectedTier.features.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium">Caratteristiche</Label>
                    <ul className="mt-2 space-y-1">
                      {selectedTier.features.map((feature, index) => (
                        <li key={index} className="text-sm text-gray-600 flex items-start">
                          <span className="mr-2">•</span>
                          <span>{String(feature)}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                
                <div className="text-sm text-gray-500">
                  <p>Creato: {new Date(selectedTier.created_at).toLocaleDateString('it-IT')}</p>
                  <p>Aggiornato: {new Date(selectedTier.updated_at).toLocaleDateString('it-IT')}</p>
                </div>
              </div>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
                Chiudi
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  );
};

export default AdminPricingTiers;