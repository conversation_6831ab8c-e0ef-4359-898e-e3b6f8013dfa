import { useState, useEffect } from "react";
import { ChevronUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export interface ScrollToTopProps {
  /** Background color class for the button */
  bgColor?: string;
  /** Hover background color class for the button */
  hoverBgColor?: string;
  /** Icon color class */
  iconColor?: string;
  /** Border color class */
  borderColor?: string;
  /** Shadow class for the button */
  shadowClass?: string;
  /** Z-index class for the button */
  zIndexClass?: string;
  /** Position classes (bottom, right) */
  positionClasses?: string;
  /** Scroll threshold to show the button (in pixels) */
  scrollThreshold?: number;
  /** Custom icon (defaults to ChevronUp) */
  icon?: React.ReactNode;
  /** Additional class names */
  className?: string;
}

const ScrollToTop = ({
  bgColor = "bg-pangea-blue/90",
  hoverBgColor = "hover:bg-pangea-blue",
  iconColor = "text-white",
  borderColor = "border border-white/20",
  shadowClass = "shadow-lg",
  zIndexClass = "z-40",
  positionClasses = "bottom-4 sm:bottom-6 right-4 sm:right-6",
  scrollThreshold = 300,
  icon,
  className,
}: ScrollToTopProps = {}) => {
  const [isVisible, setIsVisible] = useState(false);

  // Show button when page is scrolled down
  const toggleVisibility = () => {
    if (window.scrollY > scrollThreshold) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  };

  // Set up scroll event listener
  useEffect(() => {
    window.addEventListener("scroll", toggleVisibility);
    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <div className={cn(
      "fixed transition-all duration-300",
      positionClasses,
      zIndexClass,
      isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4 pointer-events-none',
      className
    )}>
      <Button
        onClick={scrollToTop}
        size="icon"
        className={cn(
          "h-10 w-10 sm:h-12 sm:w-12 rounded-full backdrop-blur-sm transition-all hover:scale-110",
          bgColor,
          hoverBgColor,
          shadowClass,
          borderColor
        )}
        aria-label="Scroll to top"
      >
        <div className="relative">
          {icon || <ChevronUp className={cn("h-5 w-5 sm:h-6 sm:w-6 animate-bounce-subtle", iconColor)} />}
        </div>
      </Button>
    </div>
  );
};

export default ScrollToTop;
