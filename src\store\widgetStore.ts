
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  Widget,
  WidgetType,
  WidgetSize,
  DashboardConfig
} from '@/types/widget';

// Definizione dello stato dello store
interface WidgetState {
  dashboardConfig: DashboardConfig;
  addWidget: (widget: Widget) => void;
  removeWidget: (widgetId: string) => void;
  updateWidget: (widgetId: string, updates: Partial<Widget>) => void;
  moveWidget: (widgetId: string, newPosition: number) => void;
  resetDashboard: (config?: DashboardConfig) => void;
  setWidgetLoading: (widgetId: string, isLoading: boolean) => void;
  updateWidgetData: (widgetId: string, data: any) => void;
}

// Configurazione predefinita della dashboard
const defaultDashboardConfig: DashboardConfig = {
  widgets: [],
  lastUpdated: new Date().toISOString(),
  layout: 'grid'
};

// Creazione dello store
export const useWidgetStore = create<WidgetState>()(
  persist(
    (set) => ({
      dashboardConfig: defaultDashboardConfig,

      // Aggiunge un nuovo widget alla dashboard
      addWidget: (widget: Widget) => set((state) => {
        const newWidgets = [...state.dashboardConfig.widgets, widget];
        return {
          dashboardConfig: {
            ...state.dashboardConfig,
            widgets: newWidgets,
            lastUpdated: new Date().toISOString()
          }
        };
      }),

      // Rimuove un widget dalla dashboard
      removeWidget: (widgetId: string) => set((state) => {
        const newWidgets = state.dashboardConfig.widgets.filter(
          widget => widget.id !== widgetId
        );
        return {
          dashboardConfig: {
            ...state.dashboardConfig,
            widgets: newWidgets,
            lastUpdated: new Date().toISOString()
          }
        };
      }),

      // Aggiorna le proprietà di un widget
      updateWidget: (widgetId: string, updates: Partial<Widget>) => set((state) => {
        const newWidgets = state.dashboardConfig.widgets.map(widget =>
          widget.id === widgetId
            ? { ...widget, ...updates }
            : widget
        ) as Widget[];
        
        return {
          dashboardConfig: {
            ...state.dashboardConfig,
            widgets: newWidgets,
            lastUpdated: new Date().toISOString()
          }
        };
      }),

      // Sposta un widget a una nuova posizione
      moveWidget: (widgetId: string, newPosition: number) => set((state) => {
        const widget = state.dashboardConfig.widgets.find(w => w.id === widgetId);
        if (!widget) return state;

        const newWidgets = state.dashboardConfig.widgets
          .filter(w => w.id !== widgetId)
          .map(w => {
            if (w.position > widget.position && w.position <= newPosition) {
              return { ...w, position: w.position - 1 };
            }
            if (w.position < widget.position && w.position >= newPosition) {
              return { ...w, position: w.position + 1 };
            }
            return w;
          }) as Widget[];

        newWidgets.push({ ...widget, position: newPosition } as Widget);
        newWidgets.sort((a, b) => a.position - b.position);

        return {
          dashboardConfig: {
            ...state.dashboardConfig,
            widgets: newWidgets,
            lastUpdated: new Date().toISOString()
          }
        };
      }),

      // Ripristina la dashboard alla configurazione predefinita o a una nuova configurazione
      resetDashboard: (config?: DashboardConfig) => set({
        dashboardConfig: config || defaultDashboardConfig
      }),

      // Imposta lo stato di caricamento di un widget
      setWidgetLoading: (widgetId: string, isLoading: boolean) => set((state) => {
        // Trova il widget da aggiornare
        const widget = state.dashboardConfig.widgets.find(w => w.id === widgetId);

        // Se il widget non esiste o lo stato di caricamento è già quello richiesto, non fare nulla
        if (!widget || widget.isLoading === isLoading) {
          return state;
        }

        // Altrimenti, aggiorna il widget
        const newWidgets = state.dashboardConfig.widgets.map(w =>
          w.id === widgetId
            ? { ...w, isLoading }
            : w
        ) as Widget[];

        return {
          dashboardConfig: {
            ...state.dashboardConfig,
            widgets: newWidgets
          }
        };
      }),

      // Aggiorna i dati di un widget
      updateWidgetData: (widgetId: string, data: any) => set((state) => {
        // Trova il widget da aggiornare
        const widget = state.dashboardConfig.widgets.find(w => w.id === widgetId);

        // Se il widget non esiste, non fare nulla
        if (!widget) {
          return state;
        }

        // Controlla se i dati sono effettivamente cambiati
        let hasChanged = false;
        for (const key in data) {
          if (widget.data[key] !== data[key]) {
            hasChanged = true;
            break;
          }
        }

        // Se i dati non sono cambiati, non fare nulla
        if (!hasChanged) {
          return state;
        }

        // Altrimenti, aggiorna il widget
        const newWidgets = state.dashboardConfig.widgets.map(w =>
          w.id === widgetId
            ? { ...w, data: { ...w.data, ...data } }
            : w
        ) as Widget[];

        return {
          dashboardConfig: {
            ...state.dashboardConfig,
            widgets: newWidgets,
            lastUpdated: new Date().toISOString()
          }
        };
      })
    }),
    {
      name: 'dashboard-widgets-storage',
      partialize: (state) => ({
        dashboardConfig: state.dashboardConfig
      })
    }
  )
);
