import { useState, useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import MainLayout from "@/layouts/MainLayout";
import { useBusinessStore } from "@/store/businessStore";
import { useAuth } from "@/contexts/AuthContext";
import { Database } from "@/integrations/supabase/types";
import { supabase } from "@/integrations/supabase/client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { 
  Building2, 
  MapPin, 
  Mail, 
  Phone, 
  Globe, 
  Calendar, 
  Search, 
  LayoutGrid, 
  Table as TableIcon,
  Edit,
  PackageCheck,
  Clock
} from "lucide-react";

type BusinessWithCounts = Database['public']['Views']['businesses_with_counts']['Row'];

const ListBusinesses = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { businesses, isLoading, fetchBusinesses } = useBusinessStore();
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<'table' | 'card'>('table');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(12);

  useEffect(() => {
    if (user) {
      fetchBusinesses(user.id);
    }
  }, [user, fetchBusinesses]);

  // Fetch full business data including contact info
  const [fullBusinesses, setFullBusinesses] = useState<BusinessWithCounts[]>([]);
  
  useEffect(() => {
    const fetchFullBusinessData = async () => {
      if (!user) return;
      
      try {
        const { data, error } = await supabase
          .from('businesses_with_counts')
          .select('*')
          .eq('owner_id', user.id)
          .order('created_at', { ascending: false });

        if (error) throw error;
        setFullBusinesses(data || []);
      } catch (error) {
        console.error('Error fetching businesses:', error);
      }
    };

    fetchFullBusinessData();
  }, [user]);

  // Use full business data instead of filtered businesses
  const userBusinesses = fullBusinesses;

  // Filter and search businesses
  const filteredBusinesses = useMemo(() => {
    return userBusinesses.filter(business => {
      const matchesSearch = !searchTerm || 
        business.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        business.description?.toLowerCase().includes(searchTerm.toLowerCase());
      
      return matchesSearch;
    });
  }, [userBusinesses, searchTerm]);

  // Pagination
  const totalPages = Math.ceil(filteredBusinesses.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedBusinesses = filteredBusinesses.slice(startIndex, startIndex + itemsPerPage);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const getBusinessInitials = (name: string) => {
    if (!name) return 'B';
    const words = name.split(' ');
    if (words.length >= 2) {
      return `${words[0].charAt(0)}${words[1].charAt(0)}`.toUpperCase();
    }
    return name.charAt(0).toUpperCase();
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('it-IT');
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="space-y-6 p-6">
          <div className="animate-pulse">
            <div className="h-8 w-64 bg-gray-300 rounded mb-2"></div>
            <div className="h-4 w-96 bg-gray-300 rounded mb-8"></div>
            <div className="h-96 bg-gray-300 rounded"></div>
          </div>
        </div>
      </MainLayout>
    );
  }

  const renderTableView = () => (
    <Card>
      <CardHeader>
        <CardTitle>Le Tue Attività</CardTitle>
        <CardDescription>
          Gestisci e visualizza tutte le tue attività registrate
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Attività</TableHead>
                <TableHead>Contatti</TableHead>
                <TableHead>Posizione</TableHead>
                <TableHead>Statistiche</TableHead>
                <TableHead>Data Creazione</TableHead>
                <TableHead>Azioni</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedBusinesses.map((business) => (
                <TableRow key={business.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback className="bg-blue-100 text-blue-600">
                          {getBusinessInitials(business.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{business.name}</div>
                        {business.description && (
                          <div className="text-sm text-muted-foreground max-w-xs truncate">
                            {business.description}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="space-y-1">
                      {business.email && (
                        <div className="flex items-center gap-2 text-sm">
                          <Mail className="h-3 w-3 text-muted-foreground" />
                          <span className="truncate max-w-xs">{business.email}</span>
                        </div>
                      )}
                      {business.phone && (
                        <div className="flex items-center gap-2 text-sm">
                          <Phone className="h-3 w-3 text-muted-foreground" />
                          <span>{business.phone}</span>
                        </div>
                      )}
                      {business.website && (
                        <div className="flex items-center gap-2 text-sm">
                          <Globe className="h-3 w-3 text-muted-foreground" />
                          <span className="truncate max-w-xs">{business.website}</span>
                        </div>
                      )}
                    </div>
                  </TableCell>

                  <TableCell>
                    {business.city || business.address ? (
                      <div className="flex items-start gap-2 text-sm">
                        <MapPin className="h-3 w-3 text-muted-foreground mt-0.5" />
                        <div>
                          {business.city && <div>{business.city}</div>}
                          {business.address && (
                            <div className="text-muted-foreground max-w-xs truncate">
                              {business.address}
                            </div>
                          )}
                        </div>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">N/A</span>
                    )}
                  </TableCell>

                  <TableCell>
                    <div className="space-y-1">
                      <Badge variant="secondary" className="text-xs">
                        <PackageCheck className="h-3 w-3 mr-1" />
                        {business.deal_count || 0} offerte
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        <Clock className="h-3 w-3 mr-1" />
                        {business.booking_count || 0} prenotazioni
                      </Badge>
                      {(business.pending_booking_count || 0) > 0 && (
                        <Badge variant="destructive" className="text-xs">
                          {business.pending_booking_count} in attesa
                        </Badge>
                      )}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      {formatDate(business.created_at)}
                    </div>
                  </TableCell>

                  <TableCell>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigate(`/business/${business.id}/edit`)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Modifica
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Empty State */}
        {filteredBusinesses.length === 0 && (
          <div className="text-center py-12 text-muted-foreground">
            <Building2 className="h-16 w-16 mx-auto mb-4 opacity-20" />
            <h3 className="text-lg font-medium mb-2">Nessuna attività trovata</h3>
            <p className="mb-4">
              {searchTerm 
                ? "Nessuna attività corrisponde alla tua ricerca" 
                : "Non hai ancora creato nessuna attività"
              }
            </p>
            {!searchTerm && (
              <Button onClick={() => navigate('/create-business')}>
                Crea la tua prima attività
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );

  const renderCardView = () => (
    <div className="space-y-6">
      {filteredBusinesses.length === 0 ? (
        <div className="text-center py-12">
          <Building2 className="h-16 w-16 mx-auto mb-4 opacity-20 text-muted-foreground" />
          <h3 className="text-lg font-medium mb-2">Nessuna attività trovata</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm 
              ? "Nessuna attività corrisponde alla tua ricerca" 
              : "Non hai ancora creato nessuna attività"
            }
          </p>
          {!searchTerm && (
            <Button onClick={() => navigate('/create-business')}>
              Crea la tua prima attività
            </Button>
          )}
        </div>
      ) : (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {paginatedBusinesses.map((business) => (
            <Card key={business.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12">
                    <AvatarFallback className="bg-blue-100 text-blue-600">
                      {getBusinessInitials(business.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg truncate">{business.name}</CardTitle>
                    {business.description && (
                      <CardDescription className="line-clamp-2">
                        {business.description}
                      </CardDescription>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Contact Info */}
                <div className="space-y-2">
                  {business.email && (
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span className="truncate">{business.email}</span>
                    </div>
                  )}
                  {business.phone && (
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{business.phone}</span>
                    </div>
                  )}
                  {(business.city || business.address) && (
                    <div className="flex items-start gap-2 text-sm">
                      <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                      <div className="flex-1 min-w-0">
                        {business.city && <div>{business.city}</div>}
                        {business.address && (
                          <div className="text-muted-foreground truncate">
                            {business.address}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {/* Statistics */}
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary" className="text-xs">
                    <PackageCheck className="h-3 w-3 mr-1" />
                    {business.deal_count || 0} offerte
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    <Clock className="h-3 w-3 mr-1" />
                    {business.booking_count || 0} prenotazioni
                  </Badge>
                  {(business.pending_booking_count || 0) > 0 && (
                    <Badge variant="destructive" className="text-xs">
                      {business.pending_booking_count} in attesa
                    </Badge>
                  )}
                </div>

                {/* Created Date */}
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Calendar className="h-3 w-3" />
                  Creata il {formatDate(business.created_at)}
                </div>

                {/* Actions */}
                <div className="pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => navigate(`/business/${business.id}/edit`)}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Modifica Attività
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <MainLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">
            <Building2 className="inline-block h-8 w-8 mr-3" />
            Le Mie Attività
          </h1>
          <p className="text-muted-foreground">
            Gestisci e visualizza tutte le tue attività registrate
          </p>
          <div className="flex items-center gap-4 mt-4">
            <Badge variant="outline" className="text-sm">
              Totale: {userBusinesses.length} attività
            </Badge>
            <Badge variant="outline" className="text-sm">
              Con offerte: {userBusinesses.filter(b => (b.deal_count || 0) > 0).length}
            </Badge>
            <Badge variant="outline" className="text-sm">
              Prenotazioni attive: {userBusinesses.reduce((sum, b) => sum + (b.booking_count || 0), 0)}
            </Badge>
          </div>
        </div>

        {/* Controls */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4 items-center">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Cerca le tue attività..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <Button
                  variant={viewMode === 'table' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('table')}
                >
                  <TableIcon className="h-4 w-4 mr-2" />
                  Tabella
                </Button>
                <Button
                  variant={viewMode === 'card' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('card')}
                >
                  <LayoutGrid className="h-4 w-4 mr-2" />
                  Card
                </Button>
              </div>
              <Button onClick={() => navigate('/create-business')}>
                Nuova Attività
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Content */}
        {viewMode === 'table' ? renderTableView() : renderCardView()}

        {/* Pagination */}
        {filteredBusinesses.length > itemsPerPage && (
          <div className="flex items-center justify-center gap-2 mt-6">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Precedente
            </Button>
            <span className="text-sm text-muted-foreground">
              Pagina {currentPage} di {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Successiva
            </Button>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default ListBusinesses;