import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import "https://deno.land/x/xhr@0.1.0/mod.ts";

// Import LangGraph dependencies
import { ChatOpenAI } from "npm:@langchain/openai@0.3.14";
import { HumanMessage, AIMessage } from "npm:@langchain/core@0.3.34/messages";
import { StateGraph, MessagesAnnotation } from "npm:@langchain/langgraph@0.2.60";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

// LangGraph workflow implementation
class LangGraphChatbot {
  private openaiApiKey: string;
  private app: any;

  constructor(apiKey: string) {
    this.openaiApiKey = apiKey;
    this.app = this.createWorkflow();
  }

  private createWorkflow() {
    // Create a model with streaming enabled
    const model = new ChatOpenAI({
      modelName: "gpt-4o-mini",
      temperature: 0.7,
      openAIApiKey: this.openaiApiKey,
      streaming: true,
    });

    // Define the function that calls the model
    const callModel = async (state: typeof MessagesAnnotation.State) => {
      console.log('Calling OpenAI model with messages:', state.messages.length);
      
      // Add system message for Italian assistant
      const systemMessage = new HumanMessage({
        content: "Sei un assistente AI utile che risponde sempre in italiano. Fornisci risposte dettagliate e accurate alle domande degli utenti."
      });
      
      const messages = [systemMessage, ...state.messages];
      const response = await model.invoke(messages);
      
      console.log('Model response received');
      return { messages: [response] };
    };

    // Define a new graph using MessagesAnnotation
    const workflow = new StateGraph(MessagesAnnotation)
      .addNode("agent", callModel)
      .addEdge("__start__", "agent")
      .addEdge("agent", "__end__");

    // Compile the workflow
    return workflow.compile();
  }

  async *processMessageStream(messages: ChatMessage[]) {
    try {
      console.log('Processing messages through LangGraph workflow with streaming');
      
      // Convert messages to LangChain format
      const langchainMessages = messages.map(msg => {
        if (msg.role === 'user') {
          return new HumanMessage({ content: msg.content });
        } else if (msg.role === 'assistant') {
          return new AIMessage({ content: msg.content });
        }
        return new HumanMessage({ content: msg.content });
      });

      // Stream the workflow
      const stream = await this.app.stream({
        messages: langchainMessages
      });

      for await (const chunk of stream) {
        if (chunk.agent && chunk.agent.messages) {
          const lastMessage = chunk.agent.messages[chunk.agent.messages.length - 1];
          if (lastMessage && lastMessage.content) {
            yield lastMessage.content;
          }
        }
      }
      
    } catch (error) {
      console.error('Error in LangGraph workflow:', error);
      throw error;
    }
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openaiApiKey) {
      throw new Error('OPENAI_API_KEY not found in environment variables');
    }

    const { messages } = await req.json();

    if (!messages || !Array.isArray(messages)) {
      throw new Error('Invalid messages format');
    }

    const chatbot = new LangGraphChatbot(openaiApiKey);

    // Create a readable stream for streaming response
    const stream = new ReadableStream({
      async start(controller) {
        try {
          for await (const chunk of chatbot.processMessageStream(messages)) {
            const encoder = new TextEncoder();
            const data = `data: ${JSON.stringify({ chunk })}\n\n`;
            controller.enqueue(encoder.encode(data));
          }
          
          // Send end marker
          const encoder = new TextEncoder();
          controller.enqueue(encoder.encode('data: [DONE]\n\n'));
          controller.close();
        } catch (error) {
          console.error('Streaming error:', error);
          const encoder = new TextEncoder();
          const errorData = `data: ${JSON.stringify({ error: error.message })}\n\n`;
          controller.enqueue(encoder.encode(errorData));
          controller.close();
        }
      }
    });

    return new Response(stream, {
      headers: {
        ...corsHeaders,
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });

  } catch (error) {
    console.error('Error in langgraph-chat function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});