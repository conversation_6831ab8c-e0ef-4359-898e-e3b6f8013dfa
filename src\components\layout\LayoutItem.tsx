
import { <PERSON>, Card<PERSON>ontent, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { DoorClosed, Table, Armchair, Sofa, Bed, Edit, Trash2, ArrowRight, Users } from "lucide-react";

interface LayoutElement {
  id: string;
  type: string;
  name: string;
  capacity?: number;
  width?: number;
  height?: number;
  parentId?: string | null;
}

interface LayoutItemProps {
  element: LayoutElement;
  isSelected: boolean;
  onSelect: () => void;
  onDelete: () => void;
  onEnter?: () => void;
  parentName?: string;
}

const LayoutItem = ({ element, isSelected, onSelect, onDelete, onEnter, parentName }: LayoutItemProps) => {
  // Determina l'icona in base al tipo di elemento
  const getElementIcon = () => {
    switch (element.type) {
      case "sala":
      case "piano":
        return <DoorClosed className="h-8 w-8" />;
      case "tavolo":
        return <Table className="h-8 w-8" />;
      case "postazione":
      case "attrezzo":
        return <Armchair className="h-8 w-8" />;
      case "camera":
        return <Bed className="h-8 w-8" />;
      case "cabina":
        return <Sofa className="h-8 w-8" />;
      default:
        return <DoorClosed className="h-8 w-8" />;
    }
  };

  const isRoom = element.type === "sala" || element.type === "piano";

  return (
    <Card 
      className={`cursor-pointer transition-all hover:border-primary ${
        isSelected ? "border-primary bg-primary/5" : ""
      }`}
      onClick={onSelect}
    >
      <CardContent className="p-4 flex flex-col items-center">
        <div className="mb-2">
          {getElementIcon()}
        </div>
        <h3 className="font-medium text-center">{element.name}</h3>
        {parentName && (
          <p className="text-xs text-gray-500 mt-1">
            In: {parentName}
          </p>
        )}
        <p className="text-sm text-gray-500 mt-1 flex items-center">
          <Users className="h-3 w-3 mr-1" />
          {element.capacity || 0} {element.capacity === 1 ? "posto" : "posti"}
        </p>
        {(element.width && element.height) && (
          <p className="text-sm text-gray-500 mt-1">
            {element.width}m × {element.height}m
          </p>
        )}
      </CardContent>
      <CardFooter className="flex justify-between p-2">
        {isRoom && onEnter && (
          <Button 
            variant="outline" 
            size="sm" 
            className="h-7 text-xs" 
            onClick={(e) => {
              e.stopPropagation();
              onEnter();
            }}
          >
            Entra <ArrowRight className="ml-1 h-3 w-3" />
          </Button>
        )}
        <div className="ml-auto">
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-7 w-7" 
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default LayoutItem;
