import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import MainLayout from '@/layouts/MainLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AddressAutocomplete, AddressComponents } from '@/components/AddressAutocomplete';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Upload, X, AlertTriangle, Link, Plus } from 'lucide-react';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useCanCreateBusiness } from '@/hooks/useBusinessLimits';

interface Category {
  id: string;
  name: string;
}

const CreateBusiness = () => {
  const navigate = useNavigate();
  const { canCreate, reason, currentCount, maxCount, isUnlimited, isLoading: limitsLoading } = useCanCreateBusiness();
  
  // Form states
  const [formData, setFormData] = useState({
    name: '',
    category_id: '',
    description: '',
    formatted_address: '',
    address: '',
    zip_code: '',
    city: '',
    state: '',
    country: '',
    phone: '',
    email: '',
    website: '',
    latitude:0,
    longitude:0,
  });
  
  const [categories, setCategories] = useState<Category[]>([]);
  const [photos, setPhotos] = useState<File[]>([]);
  const [photoPreviews, setPhotoPreviews] = useState<string[]>([]);
  const [photoMode, setPhotoMode] = useState<'upload' | 'url'>('upload');
  const [photoUrls, setPhotoUrls] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploadingPhotos, setIsUploadingPhotos] = useState(false);

  // Load categories
  useEffect(() => {
    const loadCategories = async () => {
      const { data, error } = await supabase
        .from('categories')
        .select('id, name')
        .order('name');
      
      if (error) {
        console.error('Error loading categories:', error);
      } else {
        setCategories(data || []);
      }
    };

    loadCategories();
  }, []);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleAddressChange = (addressComponents: AddressComponents) => {
    
    setFormData(prev => ({
      ...prev,
      formatted_address: addressComponents.formatted_address,
      address: addressComponents.route + ", " + addressComponents.street_number,
      latitude: addressComponents.latitude,
      longitude: addressComponents.longitude,
      zip_code: addressComponents.postal_code,
      city: addressComponents.locality,
      state: addressComponents.administrative_area_level_2,
      country: addressComponents.country,
    }));
  };

  const handlePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    // Validate file types and sizes
    const validFiles = files.filter(file => {
      const isValidType = ['image/jpeg', 'image/jpg', 'image/png'].includes(file.type);
      const isValidSize = file.size <= 5 * 1024 * 1024; // 5MB
      
      if (!isValidType) {
        toast.error(`${file.name}: Formato non supportato. Usa JPG, JPEG o PNG.`);
        return false;
      }
      
      if (!isValidSize) {
        toast.error(`${file.name}: File troppo grande. Massimo 5MB.`);
        return false;
      }
      
      return true;
    });

    if (validFiles.length > 0) {
      setPhotos(prev => [...prev, ...validFiles]);
      
      // Create previews
      validFiles.forEach(file => {
        const reader = new FileReader();
        reader.onload = (e) => {
          setPhotoPreviews(prev => [...prev, e.target?.result as string]);
        };
        reader.readAsDataURL(file);
      });
    }
  };

  const removePhoto = (index: number) => {
    setPhotos(prev => prev.filter((_, i) => i !== index));
    setPhotoPreviews(prev => prev.filter((_, i) => i !== index));
  };

  const addPhotoUrl = () => {
    setPhotoUrls(prev => [...prev, '']);
  };

  const updatePhotoUrl = (index: number, url: string) => {
    setPhotoUrls(prev => prev.map((item, i) => i === index ? url : item));
  };

  const removePhotoUrl = (index: number) => {
    setPhotoUrls(prev => prev.filter((_, i) => i !== index));
  };

  const uploadPhotos = async (businessId: string): Promise<string[]> => {
    if (photos.length === 0) return [];

    setIsUploadingPhotos(true);
    const uploadedUrls: string[] = [];

    try {
      console.log('Inizio upload di', photos.length, 'foto');
      
      for (const photo of photos) {
        const fileExt = photo.name.split('.').pop();
        const filePath = `${businessId}/${crypto.randomUUID()}.${fileExt}`;
        
        console.log('Uploading file:', filePath);

        const { error: uploadError, data } = await supabase.storage
          .from('business-photos')
          .upload(filePath, photo, {
            upsert: true,
          });

        if (uploadError) {
          console.error('Errore upload foto:', uploadError);
          throw uploadError;
        }

        const { data: { publicUrl } } = supabase.storage
          .from('business-photos')
          .getPublicUrl(filePath);

        console.log('File caricato:', publicUrl);
        uploadedUrls.push(publicUrl);
      }

      console.log('Upload completato, URL:', uploadedUrls);
      
      // Reset delle foto dopo il caricamento riuscito
      setPhotos([]);
      setPhotoPreviews([]);
      
      return uploadedUrls;
    } catch (error) {
      console.error('Errore durante il caricamento delle foto:', error);
      toast.error("Errore durante il caricamento delle foto");
      return [];
    } finally {
      setIsUploadingPhotos(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.address) {
      toast.error("Nome attività e indirizzo sono obbligatori");
      return;
    }

    // Check business creation limits
    if (!canCreate) {
      toast.error(reason || "Non puoi creare più business con il tuo piano attuale");
      return;
    }

    setIsSubmitting(true);

    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        toast.error("Devi essere autenticato per creare un'attività");
        return;
      }

      // Create business
      const { formatted_address, ...businessDataFields } = formData; // remove formatted_address from formData
      const businessData = {
        ...businessDataFields,
        owner_id: user.id,
        photos: [] as string[], // Will be updated after photo upload
      };

      const { data: business, error: businessError } = await supabase
        .from('businesses')
        .insert(businessData)
        .select()
        .single();

      if (businessError) {
        console.error('Error creating business:', businessError);
        toast.error("Errore nella creazione dell'attività");
        return;
      }

      // Collect all photo URLs
      let allPhotoUrls: string[] = [];
      
      // Upload photos if any
      if (photos.length > 0) {
        const uploadedUrls = await uploadPhotos(business.id);
        allPhotoUrls.push(...uploadedUrls);
      }
      
      // Add URL photos (filter out empty ones)
      const validUrlPhotos = photoUrls.filter(url => url.trim());
      allPhotoUrls.push(...validUrlPhotos);

      // Update business with all photo URLs
      if (allPhotoUrls.length > 0) {
        const { error: updateError } = await supabase
          .from('businesses')
          .update({ photos: allPhotoUrls })
          .eq('id', business.id);

        if (updateError) {
          console.error('Error updating business with photos:', updateError);
          // Don't fail the creation, just warn
          toast.error("Attività creata ma errore nel salvataggio delle foto");
        }
      }

      toast.success("Attività creata con successo!");
      navigate('/dashboard');
      
    } catch (error) {
      console.error('Error in form submission:', error);
      toast.error("Errore imprevisto durante la creazione");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle>Crea una nuova attività</CardTitle>
            {!isUnlimited && (
              <div className="text-sm text-gray-600">
                Business: {currentCount} di {maxCount} utilizzati
              </div>
            )}
          </CardHeader>
          <CardContent>
            {/* Business Limits Alert */}
            {!canCreate && !limitsLoading && (
              <Alert variant="destructive" className="mb-6">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {reason} 
                  <Button 
                    variant="link" 
                    className="p-0 h-auto ml-2 text-red-600 underline"
                    onClick={() => navigate('/subscription-plan')}
                  >
                    Aggiorna il piano
                  </Button>
                </AlertDescription>
              </Alert>
            )}
            
            {canCreate && !isUnlimited && currentCount >= maxCount - 1 && (
              <Alert className="mb-6">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Stai per raggiungere il limite di {maxCount} business per il tuo piano. 
                  Dopo questo, dovrai aggiornare il piano per crearne altri.
                </AlertDescription>
              </Alert>
            )}

            
            <form onSubmit={handleSubmit} className={`space-y-6 ${!canCreate ? 'opacity-50' : ''}`}>
              <div className="space-y-2">
                <Label htmlFor="name">Nome attività *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  disabled={!canCreate}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Categoria</Label>
                <Select
                  value={formData.category_id}
                  onValueChange={(value) => handleInputChange('category_id', value)}
                  disabled={!canCreate}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Seleziona una categoria" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Descrizione</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Descrivi la tua attività..."
                  disabled={!canCreate}
                  rows={4}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Indirizzo *</Label>
                <AddressAutocomplete
                  value={formData.formatted_address  || ''}
                  onChange={handleAddressChange}
                  disabled={!canCreate}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Telefono</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  disabled={!canCreate}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  disabled={!canCreate}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="website">Sito web</Label>
                <Input
                  id="website"
                  type="url"
                  value={formData.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  placeholder="https://..."
                  disabled={!canCreate}
                />
              </div>

              <div className="space-y-2">
                <Label>Foto dell'attività</Label>
                <Tabs value={photoMode} onValueChange={(value) => setPhotoMode(value as 'upload' | 'url')} className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="upload" disabled={!canCreate}>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Foto
                    </TabsTrigger>
                    <TabsTrigger value="url" disabled={!canCreate}>
                      <Link className="h-4 w-4 mr-2" />
                      Aggiungi URL
                    </TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="upload" className="space-y-4">
                    <div className={`border-2 border-dashed rounded-lg p-6 text-center ${
                      !canCreate 
                        ? 'border-gray-200 bg-gray-50 cursor-not-allowed' 
                        : 'border-gray-300 hover:border-gray-400'
                    }`}>
                      <input
                        type="file"
                        multiple
                        accept="image/jpeg,image/jpg,image/png"
                        onChange={handlePhotoChange}
                        className="hidden"
                        id="photo-upload"
                        disabled={!canCreate}
                      />
                      <label 
                        htmlFor="photo-upload" 
                        className={`${!canCreate ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                      >
                        <Upload className={`h-8 w-8 mx-auto mb-2 ${!canCreate ? 'text-gray-300' : 'text-gray-400'}`} />
                        <p className={`text-sm ${!canCreate ? 'text-gray-400' : 'text-gray-600'}`}>
                          {!canCreate ? 'Upload non disponibile - limite raggiunto' : 'Clicca per caricare nuove foto'}
                        </p>
                        <p className="text-xs text-gray-400 mt-1">
                          Formato: JPG, PNG. Max 5MB per foto
                        </p>
                      </label>
                    </div>

                    {photoPreviews.length > 0 && (
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {photoPreviews.map((preview, index) => (
                          <div key={index} className="relative">
                            <img
                              src={preview}
                              alt={`Preview ${index + 1}`}
                              className="w-full h-24 object-cover rounded-lg"
                            />
                            <button
                              type="button"
                              onClick={() => removePhoto(index)}
                              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
                              disabled={!canCreate}
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="url" className="space-y-4">
                    <div className="space-y-4">
                      {photoUrls.map((url, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            placeholder="https://esempio.com/foto.jpg"
                            value={url}
                            onChange={(e) => updatePhotoUrl(index, e.target.value)}
                            disabled={!canCreate}
                            className="flex-1"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            size="icon"
                            onClick={() => removePhotoUrl(index)}
                            disabled={!canCreate}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      
                      <Button
                        type="button"
                        variant="outline"
                        onClick={addPhotoUrl}
                        disabled={!canCreate}
                        className="w-full"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Aggiungi URL Foto
                      </Button>
                    </div>

                    {photoUrls.filter(url => url.trim()).length > 0 && (
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {photoUrls.filter(url => url.trim()).map((url, index) => (
                          <div key={index} className="relative">
                            <img
                              src={url}
                              alt={`URL Photo ${index + 1}`}
                              className="w-full h-24 object-cover rounded-lg"
                              onError={(e) => {
                                e.currentTarget.style.display = 'none';
                              }}
                            />
                          </div>
                        ))}
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={isSubmitting || isUploadingPhotos || !canCreate}
              >
                {isSubmitting || isUploadingPhotos ? "Creazione in corso..." : 
                 !canCreate ? "Limite raggiunto" : "Crea attività"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default CreateBusiness;