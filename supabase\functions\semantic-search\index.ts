import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Build comprehensive content for deal embedding
function buildDealContent(deal: any): string {
  const parts = [];
  
  // Basic info
  parts.push(deal.title);
  if (deal.description) parts.push(deal.description);
  
  // Category
  const categoryName = deal.categories?.name || '';
  if (categoryName) parts.push(categoryName);
  
  // Business info
  if (deal.businesses?.name) parts.push(deal.businesses.name);
  
  // Format structured address with dashes
  const addressParts = [];
  if (deal.businesses?.address) addressParts.push(deal.businesses.address);
  if (deal.businesses?.city) addressParts.push(deal.businesses.city);
  if (deal.businesses?.state) addressParts.push(deal.businesses.state);
  if (deal.businesses?.country) addressParts.push(deal.businesses.country);
  if (addressParts.length > 0) {
    parts.push(addressParts.join(' - '));
  }
  
  if (deal.businesses?.latitude && deal.businesses?.longitude) {
    parts.push(`coordinate latitudine ${deal.businesses.latitude} longitudine ${deal.businesses.longitude}`);
  }
  
  // Pricing info in natural language
  if (deal.original_price && deal.discounted_price) {
    const discount = deal.discount_percentage;
    if (discount) {
      parts.push(`prezzo ${deal.discounted_price} euro`);
      parts.push(`sconto ${discount}%`);
      parts.push(`era ${deal.original_price} euro`);
    } else {
      parts.push(`prezzo ${deal.discounted_price} euro`);
    }
  }
  
  // Price range categorization
  const price = parseFloat(deal.discounted_price || deal.original_price);
  if (price) {
    if (price < 20) parts.push('economico conveniente');
    else if (price < 50) parts.push('prezzo medio');
    else if (price < 100) parts.push('premium');
    else parts.push('lusso costoso');
  }
  
  // Deal validity period with specific dates
  if (deal.start_date && deal.end_date) {
    const startDate = new Date(deal.start_date);
    const endDate = new Date(deal.end_date);
    const now = new Date();
    
    // Add formatted dates
    const startDateFormatted = formatDateInItalian(startDate);
    const endDateFormatted = formatDateInItalian(endDate);
    parts.push(`valido dal ${startDateFormatted} al ${endDateFormatted}`);
    
    // Add temporal context
    if (endDate < now) {
      parts.push('scaduto');
    } else if (startDate > now) {
      parts.push('prossimamente');
    } else {
      const daysLeft = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      if (daysLeft <= 7) parts.push('scade presto');
      else if (daysLeft <= 30) parts.push('valido questo mese');
      else parts.push('valido a lungo');
    }
    
    // Add month names for better searchability
    const startMonth = getItalianMonthName(startDate.getMonth());
    const endMonth = getItalianMonthName(endDate.getMonth());
    if (startMonth) parts.push(startMonth);
    if (endMonth && endMonth !== startMonth) parts.push(endMonth);
  }
  
  // Time availability with detailed slot information
  if (deal.time_slots?.schedule) {
    const availability = parseTimeSlots(deal.time_slots.schedule);
    parts.push(...availability);
  }
  
  return parts.filter(Boolean).join(' ');
}

// Format date in Italian format
function formatDateInItalian(date: Date): string {
  const day = date.getDate();
  const month = date.getMonth() + 1;
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
}

// Get Italian month name
function getItalianMonthName(monthIndex: number): string {
  const monthNames = [
    'gennaio', 'febbraio', 'marzo', 'aprile', 'maggio', 'giugno',
    'luglio', 'agosto', 'settembre', 'ottobre', 'novembre', 'dicembre'
  ];
  return monthNames[monthIndex] || '';
}

// Parse time slots to extract availability information
function parseTimeSlots(schedule: any[]): string[] {
  if (!Array.isArray(schedule)) return [];
  
  const availability = [];
  const dayNames = {
    1: 'lunedì',
    2: 'martedì', 
    3: 'mercoledì',
    4: 'giovedì',
    5: 'venerdì',
    6: 'sabato',
    7: 'domenica'
  };
  
  const availableDays = [];
  const timeRanges = new Set();
  const specificTimes = [];
  
  for (const daySchedule of schedule) {
    if (daySchedule.time_slots && daySchedule.time_slots.length > 0) {
      const dayName = dayNames[daySchedule.day as keyof typeof dayNames];
      if (dayName) availableDays.push(dayName);
      
      // Analyze time slots for detailed information
      for (const slot of daySchedule.time_slots) {
        if (slot.available_seats > 0) {
          const startTime = slot.start_time;
          const endTime = slot.end_time;
          
          // Add specific time range
          if (startTime && endTime) {
            const formattedStart = formatTime(startTime);
            const formattedEnd = formatTime(endTime);
            specificTimes.push(`${dayName} ${formattedStart}-${formattedEnd}`);
            
            // Add searchable time descriptions
            const startHour = parseInt(startTime.split(':')[0] || '0');
            if (startHour >= 6 && startHour < 12) {
              timeRanges.add('mattina');
              timeRanges.add('mattino');
            } else if (startHour >= 12 && startHour < 14) {
              timeRanges.add('pranzo');
              timeRanges.add('mezzogiorno');
            } else if (startHour >= 14 && startHour < 18) {
              timeRanges.add('pomeriggio');
            } else if (startHour >= 18 && startHour < 22) {
              timeRanges.add('sera');
              timeRanges.add('serata');
            } else if (startHour >= 22 || startHour < 6) {
              timeRanges.add('notte');
              timeRanges.add('notturno');
            }
            
            // Add hour-specific terms
            if (startHour >= 8 && startHour <= 9) timeRanges.add('prima mattina');
            if (startHour >= 19 && startHour <= 21) timeRanges.add('aperitivo');
            if (startHour >= 20 && startHour <= 22) timeRanges.add('cena');
          }
        }
      }
    }
  }
  
  // Add availability info
  if (availableDays.length > 0) {
    availability.push(`disponibile ${availableDays.join(' ')}`);
  }
  
  if (timeRanges.size > 0) {
    availability.push(`orari ${Array.from(timeRanges).join(' ')}`);
  }
  
  // Add specific time slots for precise searching
  if (specificTimes.length > 0) {
    availability.push(...specificTimes);
  }
  
  // Add day type availability
  const hasWeekdays = availableDays.some(day => !['sabato', 'domenica'].includes(day));
  const hasWeekends = availableDays.some(day => ['sabato', 'domenica'].includes(day));
  
  if (hasWeekdays && hasWeekends) {
    availability.push('tutti i giorni');
    availability.push('sempre aperto');
  } else if (hasWeekends) {
    availability.push('fine settimana weekend');
    availability.push('sabato domenica');
  } else if (hasWeekdays) {
    availability.push('giorni feriali settimana');
    availability.push('lunedì venerdì');
  }
  
  return availability;
}

// Format time from HH:MM:SS to HH:MM
function formatTime(timeString: string): string {
  if (!timeString) return '';
  const parts = timeString.split(':');
  if (parts.length >= 2) {
    return `${parts[0]}:${parts[1]}`;
  }
  return timeString;
}

// Generate embedding using OpenAI
async function generateEmbedding(text: string): Promise<number[]> {
  const response = await fetch('https://api.openai.com/v1/embeddings', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${openAIApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'text-embedding-3-small',
      input: text,
    }),
  });

  if (!response.ok) {
    throw new Error(`OpenAI API error: ${response.statusText}`);
  }

  const data = await response.json();
  return data.data[0].embedding;
}

// Create or update deal embedding
async function upsertDealEmbedding(dealId: string, content: string) {
  const embedding = await generateEmbedding(content);
  
  // First, try to find existing embedding for this deal
  const { data: existingEmbedding } = await supabase
    .from('deal_embeddings')
    .select('id')
    .eq('deal_id', dealId)
    .single();

  if (existingEmbedding) {
    // Update existing embedding
    const { error } = await supabase
      .from('deal_embeddings')
      .update({
        content,
        embedding,
        updated_at: new Date().toISOString()
      })
      .eq('deal_id', dealId);

    if (error) {
      console.error('Error updating deal embedding:', error);
      throw error;
    }
  } else {
    // Insert new embedding
    const { error } = await supabase
      .from('deal_embeddings')
      .insert({
        deal_id: dealId,
        content,
        embedding,
        updated_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error inserting deal embedding:', error);
      throw error;
    }
  }
}

// Search deals by semantic similarity
async function searchDeals(query: string, filters: any = {}) {
  const queryEmbedding = await generateEmbedding(query);
  
  // Convert embedding to PostgreSQL vector format string
  console.log('Query embedding length:', queryEmbedding.length);
  const embeddingString = `[${queryEmbedding.join(',')}]`;
  
  let rpcQuery = supabase.rpc('search_deals_semantic', {
    query_embedding: embeddingString, // Pass as string in vector format
    similarity_threshold: 0.3, // Even lower threshold for Italian text
    match_count: 20
  });

  console.log('Calling RPC with query:', query, 'embedding length:', queryEmbedding.length);

  const { data, error } = await rpcQuery;
  
  if (error) {
    console.error('Error searching deals:', error);
    throw error;
  }

  console.log('Search results:', data?.length || 0, 'deals found');
  return data;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { action, ...params } = await req.json();

    switch (action) {
      case 'upsert_embedding':
        await upsertDealEmbedding(params.deal_id, params.content);
        return new Response(
          JSON.stringify({ success: true }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );

      case 'search':
        const results = await searchDeals(params.query, params.filters);
        return new Response(
          JSON.stringify({ deals: results }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );

      case 'batch_index':
        // Index existing deals with comprehensive information
        const { data: deals, error: dealsError } = await supabase
          .from('deals')
          .select(`
            id, title, description, original_price, discounted_price, discount_percentage,
            start_date, end_date, time_slots,
            businesses!inner(name, city, address, state, country, latitude, longitude),
            categories(name)
          `)
          .eq('status', 'published');

        if (dealsError) throw dealsError;

        const indexPromises = deals.map(async (deal: any) => {
          const content = buildDealContent(deal);
          await upsertDealEmbedding(deal.id, content);
        });

        await Promise.all(indexPromises);

        return new Response(
          JSON.stringify({ success: true, indexed: deals.length }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );

      case 'delete_all':
        // Delete all deal embeddings
        const { error: deleteError } = await supabase
          .from('deal_embeddings')
          .delete()
          .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records

        if (deleteError) throw deleteError;

        return new Response(
          JSON.stringify({ success: true, message: 'Tutti gli embeddings sono stati eliminati' }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );

      default:
        throw new Error('Invalid action');
    }
  } catch (error) {
    console.error('Error in semantic-search function:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});