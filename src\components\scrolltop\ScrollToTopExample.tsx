import React from 'react';
import ScrollToTop from './ScrollToTop';
import { ArrowUp, ChevronUp, MoveUp } from 'lucide-react';

const ScrollToTopExample = () => {
  return (
    <div className="space-y-8 p-6">
      <h1 className="text-2xl font-bold">ScrollToTop Component Examples</h1>
      
      {/* Default ScrollToTop */}
      <div className="border p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Default Style</h2>
        <p>Default ScrollToTop with pangea-blue colors</p>
        <ScrollToTop />
      </div>
      
      {/* Custom Colors */}
      <div className="border p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Custom Colors</h2>
        <p>ScrollToTop with custom colors</p>
        <ScrollToTop 
          bgColor="bg-emerald-500/90"
          hoverBgColor="hover:bg-emerald-600"
          iconColor="text-white"
          borderColor="border border-emerald-300/30"
        />
      </div>
      
      {/* Custom Position */}
      <div className="border p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Custom Position</h2>
        <p>ScrollToTop positioned on the left side</p>
        <ScrollToTop 
          positionClasses="bottom-4 sm:bottom-6 left-4 sm:left-6"
          bgColor="bg-purple-500/90"
          hoverBgColor="hover:bg-purple-600"
        />
      </div>
      
      {/* Custom Icon */}
      <div className="border p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Custom Icon</h2>
        <p>ScrollToTop with a different icon</p>
        <ScrollToTop 
          icon={<ArrowUp className="h-5 w-5 sm:h-6 sm:w-6 text-white animate-bounce-subtle" />}
          bgColor="bg-amber-500/90"
          hoverBgColor="hover:bg-amber-600"
        />
      </div>
      
      {/* Lower Scroll Threshold */}
      <div className="border p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Lower Scroll Threshold</h2>
        <p>ScrollToTop that appears after scrolling just 100px</p>
        <ScrollToTop 
          scrollThreshold={100}
          bgColor="bg-rose-500/90"
          hoverBgColor="hover:bg-rose-600"
          icon={<MoveUp className="h-5 w-5 sm:h-6 sm:w-6 text-white animate-bounce-subtle" />}
        />
      </div>
      
      {/* Larger Button */}
      <div className="border p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Larger Button</h2>
        <p>ScrollToTop with a larger button using className</p>
        <ScrollToTop 
          className="scale-125"
          bgColor="bg-blue-500/90"
          hoverBgColor="hover:bg-blue-600"
        />
      </div>
    </div>
  );
};

export default ScrollToTopExample;
