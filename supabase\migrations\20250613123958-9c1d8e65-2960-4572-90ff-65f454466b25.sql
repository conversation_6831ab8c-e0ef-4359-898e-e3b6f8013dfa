
-- Create a function to update user metadata (including roles)
-- This function will be called by administrators to update user roles
CREATE OR REPLACE FUNCTION update_user_metadata(
  target_user_id uuid,
  new_metadata jsonb
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Update the user metadata in auth.users
  UPDATE auth.users 
  SET user_metadata = user_metadata || new_metadata,
      updated_at = now()
  WHERE id = target_user_id;
END;
$$;

-- Create a function to get user metadata including role
CREATE OR REPLACE FUNCTION get_user_role(user_id uuid)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_role text;
BEGIN
  SELECT COALESCE(user_metadata->>'role', 'user') 
  INTO user_role
  FROM auth.users 
  WHERE id = user_id;
  
  RETURN user_role;
END;
$$;

-- Update the users_with_details view to include role information
DROP VIEW IF EXISTS users_with_details;
CREATE VIEW users_with_details AS
select
  au.id,
  au.email,
  au.created_at as registered_at,
  ud.first_name,
  ud.last_name,
  ud.display_name,
  ud.avatar_url,
  ud.phone_number,
  ud.business_mode,
  ud.default_business_id,
  COALESCE(au.raw_user_meta_data->>'role', 'user') as role
from
  auth.users au
  left join user_details ud on au.id = ud.id
WHERE au.deleted_at IS NULL;
