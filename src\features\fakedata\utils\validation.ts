import { GenerationOptions, FakeDataResult } from '../types';

// Validation utilities for fake data generation

export class DataValidationUtils {
  
  // Validate generation options
  static validateGenerationOptions(options: GenerationOptions): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Basic validation - all options are boolean, so just check they exist
    if (typeof options.useSeasonality !== 'boolean') {
      errors.push('useSeasonality must be a boolean');
    }
    
    if (typeof options.useBusinessLifecycle !== 'boolean') {
      errors.push('useBusinessLifecycle must be a boolean');
    }
    
    if (typeof options.useRealisticClustering !== 'boolean') {
      errors.push('useRealisticClustering must be a boolean');
    }
    
    if (typeof options.useCustomerBehavior !== 'boolean') {
      errors.push('useCustomerBehavior must be a boolean');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  // Validate date ranges
  static validateDateRange(startDate: Date, endDate: Date): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!(startDate instanceof Date) || isNaN(startDate.getTime())) {
      errors.push('Start date is invalid');
    }
    
    if (!(endDate instanceof Date) || isNaN(endDate.getTime())) {
      errors.push('End date is invalid');
    }
    
    if (startDate >= endDate) {
      errors.push('Start date must be before end date');
    }
    
    const maxRangeMs = 365 * 24 * 60 * 60 * 1000; // 1 year
    if (endDate.getTime() - startDate.getTime() > maxRangeMs) {
      errors.push('Date range cannot exceed 1 year');
    }
    
    // Ensure dates are not too far in the past or future
    const now = new Date();
    const twoYearsAgo = new Date(now.getTime() - (2 * 365 * 24 * 60 * 60 * 1000));
    const oneYearFromNow = new Date(now.getTime() + (365 * 24 * 60 * 60 * 1000));
    
    if (startDate < twoYearsAgo) {
      errors.push('Start date cannot be more than 2 years in the past');
    }
    
    if (endDate > oneYearFromNow) {
      errors.push('End date cannot be more than 1 year in the future');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  // Validate generation counts
  static validateGenerationCounts(counts: {
    businesses?: number;
    deals?: number;
    users?: number;  
    bookings?: number;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    Object.entries(counts).forEach(([key, value]) => {
      if (typeof value !== 'number' || value < 0) {
        errors.push(`${key} count must be a positive number`);
      }
      
      if (value > 1000) {
        errors.push(`${key} count cannot exceed 1000 for performance reasons`);
      }
      
      if (!Number.isInteger(value)) {
        errors.push(`${key} count must be an integer`);
      }
    });
    
    // Cross-validation
    if (counts.deals && counts.businesses && counts.deals > counts.businesses * 10) {
      errors.push('Cannot generate more than 10 deals per business');
    }
    
    if (counts.bookings && counts.deals && counts.bookings > counts.deals * 50) {
      errors.push('Cannot generate more than 50 bookings per deal');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  // Validate business data
  static validateBusinessData(business: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Required fields
    const requiredFields = ['name', 'address', 'city', 'category_id'];
    requiredFields.forEach(field => {
      if (!business[field]) {
        errors.push(`${field} is required`);
      }
    });
    
    // Validate coordinates
    if (business.latitude && (business.latitude < -90 || business.latitude > 90)) {
      errors.push('Latitude must be between -90 and 90');
    }
    
    if (business.longitude && (business.longitude < -180 || business.longitude > 180)) {
      errors.push('Longitude must be between -180 and 180');
    }
    
    // Validate email format
    if (business.email && !this.isValidEmail(business.email)) {
      errors.push('Invalid email format');
    }
    
    // Validate phone format
    if (business.phone && !this.isValidItalianPhone(business.phone)) {
      errors.push('Invalid Italian phone format');
    }
    
    // Validate website URL
    if (business.website && !this.isValidURL(business.website)) {
      errors.push('Invalid website URL');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  // Validate deal data
  static validateDealData(deal: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Required fields
    const requiredFields = ['title', 'business_id', 'original_price', 'discounted_price', 'start_date', 'end_date'];
    requiredFields.forEach(field => {
      if (!deal[field]) {
        errors.push(`${field} is required`);
      }
    });
    
    // Validate prices
    if (deal.original_price && deal.discounted_price) {
      if (deal.discounted_price >= deal.original_price) {
        errors.push('Discounted price must be less than original price');
      }
      
      if (deal.original_price <= 0) {
        errors.push('Original price must be positive');
      }
      
      if (deal.discounted_price <= 0) {
        errors.push('Discounted price must be positive');
      }
    }
    
    // Validate dates
    if (deal.start_date && deal.end_date) {
      const startDate = new Date(deal.start_date);
      const endDate = new Date(deal.end_date);
      
      if (startDate >= endDate) {
        errors.push('Deal start date must be before end date');
      }
    }
    
    // Validate discount percentage
    if (deal.discount_percentage) {
      if (deal.discount_percentage < 0 || deal.discount_percentage > 100) {
        errors.push('Discount percentage must be between 0 and 100');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  // Validate booking data
  static validateBookingData(booking: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Required fields
    const requiredFields = ['user_id', 'deal_id', 'booking_date', 'booking_time'];
    requiredFields.forEach(field => {
      if (!booking[field]) {
        errors.push(`${field} is required`);
      }
    });
    
    // Validate booking date
    if (booking.booking_date) {
      const bookingDate = new Date(booking.booking_date);
      const now = new Date();
      
      if (bookingDate < new Date(now.getTime() - (365 * 24 * 60 * 60 * 1000))) {
        errors.push('Booking date cannot be more than 1 year in the past');
      }
    }
    
    // Validate time format
    if (booking.booking_time && !this.isValidTimeFormat(booking.booking_time)) {
      errors.push('Invalid booking time format (should be HH:MM)');
    }
    
    if (booking.booking_end_time && !this.isValidTimeFormat(booking.booking_end_time)) {
      errors.push('Invalid booking end time format (should be HH:MM)');
    }
    
    // Validate prices
    if (booking.original_price && booking.discounted_price) {
      if (booking.discounted_price > booking.original_price) {
        errors.push('Discounted price cannot be greater than original price');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  // Consolidate multiple validation results
  static consolidateResults(results: FakeDataResult[]): FakeDataResult {
    return results.reduce((consolidated, result) => ({
      created: consolidated.created + result.created,
      errors: [...consolidated.errors, ...result.errors],
      warnings: [...consolidated.warnings, ...result.warnings]
    }), { created: 0, errors: [], warnings: [] });
  }
  
  // Private helper methods
  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  
  private static isValidItalianPhone(phone: string): boolean {
    const patterns = [
      /^\+39\s\d{3}\s\d{3}\s\d{4}$/, // Mobile: +39 ************
      /^\+39\s\d{2}\s\d{7,8}$/, // Landline: +39 02 12345678
      /^\d{3}\s\d{3}\s\d{4}$/, // Mobile without country code
      /^\d{2}\s\d{7,8}$/ // Landline without country code
    ];
    
    return patterns.some(pattern => pattern.test(phone));
  }
  
  private static isValidURL(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
  
  private static isValidTimeFormat(time: string): boolean {
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRegex.test(time);
  }
}