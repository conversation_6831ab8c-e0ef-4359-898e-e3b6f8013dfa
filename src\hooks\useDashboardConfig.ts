import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useBusinessStore } from '@/store/businessStore';
import { useWidgetStore } from '@/store/widgetStore';
import { DashboardConfig, Widget, WidgetType } from '@/types/widget';
import { toast } from 'sonner';
import { Json } from '@/types/supabase';

export const useDashboardConfig = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { selectedBusiness } = useBusinessStore();
  const { dashboardConfig, resetDashboard, updateWidgetData, setWidgetLoading } = useWidgetStore();
  
  const loadDashboardConfig = async () => {
    if (!selectedBusiness) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await supabase
        .from('dashboard_configurations')
        .select('config')
        .eq('business_id', selectedBusiness.id)
        .single();
      
      if (error) throw error;
      
      if (data) {
        const configData = data.config as Json;
        resetDashboard(configData as unknown as DashboardConfig);
      }
    } catch (err: any) {
      if (err.code !== 'PGRST116') {
        console.error('Errore durante il caricamento della configurazione dashboard:', err);
        setError('Impossibile caricare la configurazione della dashboard');
        toast.error('Errore durante il caricamento della configurazione dashboard');
      }
    } finally {
      setIsLoading(false);
    }
  };
  
  const saveDashboardConfig = async () => {
    if (!selectedBusiness) return;
    
    setIsSaving(true);
    setError(null);
    
    try {
      const { error } = await supabase
        .from('dashboard_configurations')
        .upsert({
          business_id: selectedBusiness.id,
          config: dashboardConfig as unknown as Json
        }, {
          onConflict: 'business_id',
        });
      
      if (error) throw error;
      
      toast.success('Configurazione dashboard salvata con successo');
    } catch (err: any) {
      console.error('Errore durante il salvataggio della configurazione dashboard:', err);
      setError('Impossibile salvare la configurazione della dashboard');
      toast.error('Errore durante il salvataggio della configurazione dashboard');
    } finally {
      setIsSaving(false);
    }
  };

  const loadWidgetData = async (widget: Widget) => {
    if (!selectedBusiness) return;
    
    setWidgetLoading(widget.id, true);
    
    try {
      switch (widget.type) {
        case WidgetType.BOOKINGS:
          await loadBookingsData(widget.id);
          break;
        case WidgetType.DEALS:
          await loadDealsData(widget.id);
          break;
        case WidgetType.CLIENTS:
          await loadClientsData(widget.id);
          break;
        case WidgetType.NEW_CUSTOMERS:
          await loadNewCustomersData(widget.id);
          break;
        case WidgetType.RECENT_BOOKINGS:
          await loadRecentBookingsData(widget.id);
          break;
        default:
          break;
      }
    } catch (err) {
      console.error(`Errore durante il caricamento dei dati per il widget ${widget.id}:`, err);
    } finally {
      setWidgetLoading(widget.id, false);
    }
  };

  const loadBookingsData = async (widgetId: string) => {
    if (!selectedBusiness) return;
    
    try {
      const now = new Date();
      const nowFormatted = now.toISOString().split('T')[0];
      
      console.log('Caricamento prenotazioni future:', {
        businessId: selectedBusiness.id,
        data: nowFormatted,
        query: 'Filtrando su booking_date >= oggi'
      });
      
      const { data: deals, error: dealsError } = await supabase
        .from('deals')
        .select('id')
        .eq('business_id', selectedBusiness.id);
      
      if (dealsError) throw dealsError;
      
      if (deals && deals.length > 0) {
        const { data: bookingsData, error: bookingsError } = await supabase
          .from('bookings')
          .select('id')
          .in('deal_id', deals.map(d => d.id))
          .gte('booking_date', nowFormatted);
        
        if (bookingsError) throw bookingsError;
        
        console.log('Prenotazioni future trovate:', bookingsData?.length || 0);
        updateWidgetData(widgetId, { count: bookingsData?.length || 0 });
      } else {
        updateWidgetData(widgetId, { count: 0 });
      }
    } catch (error) {
      console.error('Errore nel caricamento delle prenotazioni:', error);
      updateWidgetData(widgetId, { count: 0 });
    }
  };

  const loadDealsData = async (widgetId: string) => {
    if (!selectedBusiness) return;
    
    try {
      const { data: deals, error } = await supabase
        .from('deals')
        .select('id, end_date')
        .eq('business_id', selectedBusiness.id);
      
      if (error) throw error;
      
      const now = new Date();
      const active = deals.filter(deal => new Date(deal.end_date) >= now);
      const expired = deals.filter(deal => new Date(deal.end_date) < now);
      
      updateWidgetData(widgetId, {
        dealsCount: deals.length,
        activeDealsCount: active.length,
        expiredDealsCount: expired.length
      });
    } catch (error) {
      console.error('Errore nel caricamento delle offerte:', error);
      updateWidgetData(widgetId, {
        dealsCount: 0,
        activeDealsCount: 0,
        expiredDealsCount: 0
      });
    }
  };

  const loadClientsData = async (widgetId: string) => {
    if (!selectedBusiness) return;
    
    try {
      console.log('Caricamento dati clienti per business:', {
        businessId: selectedBusiness.id,
        businessName: selectedBusiness.name
      });
      
      const { data: deals, error: dealsError } = await supabase
        .from('deals')
        .select('id')
        .eq('business_id', selectedBusiness.id);
      
      if (dealsError) throw dealsError;
      
      if (deals && deals.length > 0) {
        // Utilizzo di booking_date anziché created_at
        const { data: uniqueClients, error: clientsError } = await supabase
          .from('bookings')
          .select('user_id, booking_date, created_at')
          .in('deal_id', deals.map(d => d.id))
          .not('user_id', 'is', null);
        
        if (clientsError) throw clientsError;
        
        console.log('Dati prenotazioni recuperati per clienti:', {
          count: uniqueClients?.length || 0,
          dates: uniqueClients?.slice(0, 5).map(b => ({ 
            user_id: b.user_id, 
            booking_date: b.booking_date, 
            created_at: b.created_at 
          }))
        });
        
        const uniqueClientIds = new Set();
        if (uniqueClients) {
          uniqueClients.forEach(booking => {
            if (booking.user_id) {
              uniqueClientIds.add(booking.user_id);
            }
          });
        }
        
        console.log('Clienti unici trovati:', uniqueClientIds.size);
        updateWidgetData(widgetId, { count: uniqueClientIds.size });
      } else {
        console.log('Nessun deal trovato per questo business');
        updateWidgetData(widgetId, { count: 0 });
      }
    } catch (error) {
      console.error('Errore nel recupero dei clienti:', error);
      updateWidgetData(widgetId, { count: 0 });
    }
  };

  const loadNewCustomersData = async (widgetId: string) => {
    if (!selectedBusiness) return;
    
    try {
      const now = new Date();
      const thirtyDaysAgo = new Date(now);
      thirtyDaysAgo.setDate(now.getDate() - 30);
      
      const sixtyDaysAgo = new Date(now);
      sixtyDaysAgo.setDate(now.getDate() - 60);
      
      // Formattiamo le date per la query SQL
      const nowFormatted = now.toISOString().split('T')[0];
      const thirtyDaysAgoFormatted = thirtyDaysAgo.toISOString().split('T')[0];
      const sixtyDaysAgoFormatted = sixtyDaysAgo.toISOString().split('T')[0];
      
      console.log('Filtraggio nuovi clienti per business:', {
        businessId: selectedBusiness.id,
        businessName: selectedBusiness.name, 
        now: nowFormatted,
        thirtyDaysAgo: thirtyDaysAgoFormatted,
        sixtyDaysAgo: sixtyDaysAgoFormatted,
        query: 'Filtrando su booking_date'
      });
      
      const { data: deals, error: dealsError } = await supabase
        .from('deals')
        .select('id')
        .eq('business_id', selectedBusiness.id);
      
      if (dealsError) throw dealsError;
      
      if (deals && deals.length > 0) {
        const dealIds = deals.map(d => d.id);
        console.log('Deal IDs trovati per nuovi clienti:', dealIds);
        
        // Clienti del periodo corrente (ultimi 30 giorni)
        const { data: currentPeriodBookings, error: currentError } = await supabase
          .from('bookings')
          .select('user_id, booking_date, created_at')
          .in('deal_id', dealIds)
          .gte('booking_date', thirtyDaysAgoFormatted)
          .lte('booking_date', nowFormatted)
          .not('user_id', 'is', null);
        
        if (currentError) throw currentError;
        
        console.log('Prenotazioni con utenti periodo corrente (ultimi 30 giorni):', {
          count: currentPeriodBookings?.length || 0,
          userIds: currentPeriodBookings?.map(b => b.user_id),
          dates: currentPeriodBookings?.map(b => ({ 
            user_id: b.user_id, 
            booking_date: b.booking_date, 
            created_at: b.created_at 
          }))
        });
        
        // Clienti del periodo precedente (60-30 giorni fa)
        const { data: previousPeriodBookings, error: previousError } = await supabase
          .from('bookings')
          .select('user_id, booking_date, created_at')
          .in('deal_id', dealIds)
          .gte('booking_date', sixtyDaysAgoFormatted)
          .lt('booking_date', thirtyDaysAgoFormatted)
          .not('user_id', 'is', null);
        
        if (previousError) throw previousError;
        
        console.log('Prenotazioni con utenti periodo precedente (60-30 giorni fa):', {
          count: previousPeriodBookings?.length || 0,
          userIds: previousPeriodBookings?.map(b => b.user_id),
          dates: previousPeriodBookings?.map(b => ({ 
            user_id: b.user_id, 
            booking_date: b.booking_date, 
            created_at: b.created_at 
          }))
        });
        
        // Conteggio clienti unici del periodo corrente
        const currentUniqueClients = new Set();
        if (currentPeriodBookings) {
          currentPeriodBookings.forEach(booking => {
            if (booking.user_id) {
              currentUniqueClients.add(booking.user_id);
            }
          });
        }
        
        // Conteggio clienti unici del periodo precedente
        const previousUniqueClients = new Set();
        if (previousPeriodBookings) {
          previousPeriodBookings.forEach(booking => {
            if (booking.user_id) {
              previousUniqueClients.add(booking.user_id);
            }
          });
        }
        
        const currentCount = currentUniqueClients.size;
        const previousCount = previousUniqueClients.size;
        
        console.log('Nuovi clienti trovati:', {
          uniqueCurrentCount: currentCount,
          uniquePreviousCount: previousCount,
          currentPeriodBookings: currentPeriodBookings?.length || 0,
          previousPeriodBookings: previousPeriodBookings?.length || 0
        });
        
        // Calcolo della variazione percentuale
        let percentChange = 0;
        if (previousCount > 0) {
          percentChange = ((currentCount - previousCount) / previousCount) * 100;
        } else if (currentCount > 0) {
          percentChange = 100;
        }
        
        updateWidgetData(widgetId, { 
          count: currentCount,
          period: '30 giorni',
          percentChange: Math.round(percentChange * 10) / 10
        });
      } else {
        console.log('Nessun deal trovato per questo business');
        updateWidgetData(widgetId, { 
          count: 0,
          period: '30 giorni',
          percentChange: 0
        });
      }
    } catch (error) {
      console.error('Errore nel caricamento dei nuovi clienti:', error);
      updateWidgetData(widgetId, { 
        count: 0,
        period: '30 giorni',
        percentChange: 0
      });
    }
  };

  const loadRecentBookingsData = async (widgetId: string) => {
    if (!selectedBusiness) return;
    
    try {
      console.log('Verifico se la vista materializzata contiene già i dati per prenotazioni recenti...');
      const { data: mvData, error: mvError } = await supabase
        .from('mv_business_availability')
        .select('booking_count, recent_bookings, recent_bookings_previous')
        .eq('business_id', selectedBusiness.id)
        .single();
        
      if (!mvError && mvData && mvData.recent_bookings !== null) {
        console.log('Dati per prenotazioni recenti trovati nella vista materializzata:', {
          current: mvData.recent_bookings,
          previous: mvData.recent_bookings_previous
        });
        
        const currentCount = mvData.recent_bookings || 0;
        const previousCount = mvData.recent_bookings_previous || 0;
        
        // Calcolo variazione percentuale
        let percentChange = 0;
        if (previousCount > 0) {
          percentChange = ((currentCount - previousCount) / previousCount) * 100;
        } else if (currentCount > 0) {
          percentChange = 100;
        }
        
        console.log('Calcolo prenotazioni recenti da vista materializzata:', {
          currentCount,
          previousCount,
          percentChange
        });
        
        updateWidgetData(widgetId, {
          count: currentCount,
          period: '30 giorni',
          percentChange: Math.round(percentChange * 10) / 10
        });
        
        return;
      } else if (mvError) {
        console.log('Errore nel recupero dei dati dalla vista materializzata:', mvError);
      } else {
        console.log('Vista materializzata non contiene dati per prenotazioni recenti, uso calcolo diretto');
      }
      
      // Calcolo diretto se la vista materializzata non ha i dati
      const now = new Date();
      
      const thirtyDaysAgo = new Date(now);
      thirtyDaysAgo.setDate(now.getDate() - 30);
      
      const sixtyDaysAgo = new Date(now);
      sixtyDaysAgo.setDate(now.getDate() - 60);
      
      // Formattiamo le date per la query SQL
      const nowFormatted = now.toISOString().split('T')[0];
      const thirtyDaysAgoFormatted = thirtyDaysAgo.toISOString().split('T')[0];
      const sixtyDaysAgoFormatted = sixtyDaysAgo.toISOString().split('T')[0];
      
      console.log('Filtraggio prenotazioni recenti per business:', {
        businessId: selectedBusiness.id,
        businessName: selectedBusiness.name,
        now: nowFormatted,
        thirtyDaysAgo: thirtyDaysAgoFormatted,
        sixtyDaysAgo: sixtyDaysAgoFormatted,
        query: 'Filtrando su booking_date'
      });
      
      const { data: deals, error: dealsError } = await supabase
        .from('deals')
        .select('id')
        .eq('business_id', selectedBusiness.id);
      
      if (dealsError) throw dealsError;
      
      if (deals && deals.length > 0) {
        const dealIds = deals.map(d => d.id);
        console.log('Deal IDs trovati per il business:', dealIds);
        
        // Clienti del periodo corrente (ultimi 30 giorni)
        const { data: currentPeriodBookings, error: currentError } = await supabase
          .from('bookings')
          .select('id, booking_date, created_at')
          .in('deal_id', dealIds)
          .gte('booking_date', thirtyDaysAgoFormatted)
          .lte('booking_date', nowFormatted);
        
        if (currentError) throw currentError;
        
        console.log('Prenotazioni periodo corrente (ultimi 30 giorni):', {
          count: currentPeriodBookings?.length || 0,
          dates: currentPeriodBookings?.map(b => ({ 
            id: b.id, 
            booking_date: b.booking_date, 
            created_at: b.created_at 
          }))
        });
        
        // Clienti del periodo precedente (60-30 giorni fa)
        const { data: previousPeriodBookings, error: previousError } = await supabase
          .from('bookings')
          .select('id, booking_date, created_at')
          .in('deal_id', dealIds)
          .gte('booking_date', sixtyDaysAgoFormatted)
          .lt('booking_date', thirtyDaysAgoFormatted);
        
        if (previousError) throw previousError;
        
        console.log('Prenotazioni periodo precedente (60-30 giorni fa):', {
          count: previousPeriodBookings?.length || 0,
          dates: previousPeriodBookings?.map(b => ({ 
            id: b.id, 
            booking_date: b.booking_date, 
            created_at: b.created_at 
          }))
        });
        
        const currentCount = currentPeriodBookings?.length || 0;
        const previousCount = previousPeriodBookings?.length || 0;
        
        // Calcolo della variazione percentuale
        let percentChange = 0;
        if (previousCount > 0) {
          percentChange = ((currentCount - previousCount) / previousCount) * 100;
        } else if (currentCount > 0) {
          percentChange = 100;
        }
        
        console.log('Aggiornamento widget prenotazioni recenti:', {
          count: currentCount,
          previousCount: previousCount,
          percentChange: percentChange
        });
        
        updateWidgetData(widgetId, { 
          count: currentCount,
          period: '30 giorni',
          percentChange: Math.round(percentChange * 10) / 10
        });
      } else {
        console.log('Nessun deal trovato per questo business');
        updateWidgetData(widgetId, { 
          count: 0,
          period: '30 giorni',
          percentChange: 0
        });
      }
    } catch (error) {
      console.error('Errore nel caricamento delle prenotazioni recenti:', error);
      updateWidgetData(widgetId, { 
        count: 0,
        period: '30 giorni',
        percentChange: 0
      });
    }
  };

  useEffect(() => {
    if (selectedBusiness && dashboardConfig.lastUpdated) {
      saveDashboardConfig();
    }
  }, [dashboardConfig, selectedBusiness]);
  
  useEffect(() => {
    if (selectedBusiness) {
      loadDashboardConfig();
    }
  }, [selectedBusiness]);
  
  return {
    isLoading,
    isSaving,
    error,
    loadDashboardConfig,
    saveDashboardConfig,
    loadWidgetData
  };
};
