import { useState, useEffect, useMemo } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Search, Users, Building2, Mail, Phone, Calendar, UserPlus, Trash2 } from "lucide-react";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import MainLayout from "@/layouts/MainLayout";
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  flexRender,
  create<PERSON>olumnHelper,
  ColumnDef,
} from "@tanstack/react-table";
import { DataTablePagination } from "@/components/DataTablePagination";

// Helper function to convert email text to array
const emailTextToArray = (emailText: string | null): string[] => {
  if (!emailText) return [];
  
  // If it's already a JSON array string, parse it
  if (emailText.startsWith('[') && emailText.endsWith(']')) {
    try {
      const parsed = JSON.parse(emailText);
      return Array.isArray(parsed) ? parsed : [emailText];
    } catch {
      return [emailText];
    }
  }
  
  // If it contains commas, split by comma
  if (emailText.includes(',')) {
    return emailText.split(',').map(email => email.trim()).filter(Boolean);
  }
  
  // Single email
  return [emailText];
};

interface Lead {
  id: number;
  Name: string | null;
  Email: string | null;
  Phone: string | null;
  Address: string | null;
  Category: string | null;
  source: string | null;
  Website: string | null;
  Rating: string | null;
  Review: string | null;
  created_at: string;
  loaded: boolean;
}

const AdminLeads = () => {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [globalFilter, setGlobalFilter] = useState("");
  const [sourceFilter, setSourceFilter] = useState<string>("all");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const { toast } = useToast();

  useEffect(() => {
    fetchLeads();
  }, []);

  const fetchLeads = async () => {
    try {
      const { data, error } = await supabase
        .from('leads')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setLeads(data || []);
    } catch (error) {
      console.error('Error fetching leads:', error);
      toast({
        title: "Errore",
        description: "Impossibile caricare i leads",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleLoaded = async (leadId: number, currentLoaded: boolean) => {
    try {
      // const { error } = await supabase
      //   .from('leads')
      //   .update({ loaded: !currentLoaded })
      //   .eq('id', leadId);

      // if (error) throw error;
      
      // fetchLeads();
      toast({
        title: "Successo",
        description: "Status caricamento aggiornato",
      });
    } catch (error) {
      console.error('Error updating lead:', error);
      toast({
        title: "Errore",
        description: "Errore nell'aggiornamento dello status",
        variant: "destructive",
      });
    }
  };

  const deleteLead = async (leadId: number) => {
    if (!confirm("Sei sicuro di voler eliminare questo lead?")) return;

    try {
      const { error } = await supabase
        .from('leads')
        .delete()
        .eq('id', leadId);

      if (error) throw error;
      
      fetchLeads();
      toast({
        title: "Successo",
        description: "Lead eliminato",
      });
    } catch (error) {
      console.error('Error deleting lead:', error);
      toast({
        title: "Errore",
        description: "Errore nell'eliminazione del lead",
        variant: "destructive",
      });
    }
  };

  const filteredLeads = useMemo(() => {
    let filtered = leads;

    // Filter by global search
    if (globalFilter) {
      const term = globalFilter.toLowerCase();
      filtered = filtered.filter(lead =>
        (lead.Email && lead.Email.toLowerCase().includes(term)) ||
        (lead.Name && lead.Name.toLowerCase().includes(term)) ||
        (lead.Phone && lead.Phone.toLowerCase().includes(term)) ||
        (lead.Address && lead.Address.toLowerCase().includes(term))
      );
    }

    // Filter by source
    if (sourceFilter !== "all") {
      filtered = filtered.filter(lead => lead.source === sourceFilter);
    }

    // Filter by category
    if (categoryFilter !== "all") {
      filtered = filtered.filter(lead => lead.Category === categoryFilter);
    }

    return filtered;
  }, [leads, globalFilter, sourceFilter, categoryFilter]);

  const columns = useMemo<ColumnDef<Lead>[]>(() => [
    {
      accessorKey: "Name",
      header: "Nome",
      cell: ({ getValue }) => (
        <div className="font-medium">{getValue() as string || 'N/A'}</div>
      ),
    },
    {
      accessorKey: "Email",
      header: "Email",
      cell: ({ getValue }) => {
        const rawEmailValue = getValue() as string;
        const emails = emailTextToArray(rawEmailValue);
        
        if (emails.length === 0) {
          return <span className="text-muted-foreground">-</span>;
        }
        
        const firstEmail = emails[0];
        const additionalCount = emails.length - 1;
        
        return (
          <div className="flex items-center gap-2">
            <Mail className="h-4 w-4 text-muted-foreground" />
            <a 
              href={`mailto:${firstEmail}`}
              className="text-sm hover:underline"
            >
              {firstEmail}
            </a>
            {additionalCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                +{additionalCount}
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "Phone",
      header: "Telefono",
      cell: ({ getValue }) => {
        const phone = getValue() as string;
        return phone ? (
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4 text-muted-foreground" />
            <a href={`tel:${phone}`} className="text-sm hover:underline">
              {phone}
            </a>
          </div>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      },
    },
    {
      accessorKey: "Address",
      header: "Indirizzo",
      cell: ({ getValue }) => {
        const address = getValue() as string;
        return address ? (
          <span className="text-sm">{address}</span>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      },
    },
    {
      accessorKey: "Category",
      header: "Categoria",
      cell: ({ getValue }) => (
        <Badge variant="outline">{getValue() as string || 'N/A'}</Badge>
      ),
    },
    {
      accessorKey: "source",
      header: "Fonte",
      cell: ({ getValue }) => (
        <Badge variant="secondary">{getValue() as string || 'N/A'}</Badge>
      ),
    },
    {
      accessorKey: "loaded",
      header: "Status",
      cell: ({ getValue, row }) => (
        <Button
          size="sm"
          variant={getValue() ? "default" : "outline"}
          onClick={() => toggleLoaded(row.original.id, getValue() as boolean)}
        >
          {getValue() ? "Caricato" : "Non Caricato"}
        </Button>
      ),
    },
    {
      accessorKey: "created_at",
      header: "Data Creazione",
      cell: ({ getValue }) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">
            {format(new Date(getValue() as string), 'dd/MM/yyyy HH:mm')}
          </span>
        </div>
      ),
    },
    {
      id: "actions",
      header: "Azioni",
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => deleteLead(row.original.id)}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      ),
    },
  ], []);

  const table = useReactTable({
    data: filteredLeads,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  });

  const exportToCSV = () => {
    const headers = [
      'Nome', 'Email', 'Telefono', 'Indirizzo', 'Categoria', 'Fonte', 'Sito Web', 'Rating', 'Caricato', 'Data Creazione'
    ];
    
    const csvData = filteredLeads.map(lead => [
      lead.Name || '',
      lead.Email || '',
      lead.Phone || '',
      lead.Address || '',
      lead.Category || '',
      lead.source || '',
      lead.Website || '',
      lead.Rating || '',
      lead.loaded ? 'Sì' : 'No',
      lead.created_at ? format(new Date(lead.created_at), 'dd/MM/yyyy HH:mm') : ''
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `leads-${format(new Date(), 'yyyy-MM-dd')}.csv`;
    link.click();
  };

  const stats = {
    total: leads.length,
    loaded: leads.filter(l => l.loaded === true).length,
    unloaded: leads.filter(l => l.loaded === false).length,
    withEmail: leads.filter(l => l.Email).length,
    withPhone: leads.filter(l => l.Phone).length,
  };

  const uniqueSources = [...new Set(leads.map(l => l.source).filter(Boolean))];
  const uniqueCategories = [...new Set(leads.map(l => l.Category).filter(Boolean))];

  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto p-6">
          <div className="flex items-center justify-center min-h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Gestione Leads</h1>
            <p className="text-muted-foreground">Monitora e gestisci i tuoi leads</p>
          </div>
          <div className="flex gap-2">
            <Button onClick={exportToCSV} variant="outline">
              Esporta CSV
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Totale Leads</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Caricati</CardTitle>
              <UserPlus className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.loaded}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Non Caricati</CardTitle>
              <Phone className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.unloaded}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Con Email</CardTitle>
              <Mail className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.withEmail}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Con Telefono</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.withPhone}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Filtri</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cerca per nome, email, telefono, indirizzo..."
                value={globalFilter}
                onChange={(e) => setGlobalFilter(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={sourceFilter}
                onChange={(e) => setSourceFilter(e.target.value)}
                className="px-3 py-2 border rounded-md text-sm"
              >
                <option value="all">Tutte le fonti</option>
                {uniqueSources.map(source => (
                  <option key={source} value={source}>{source}</option>
                ))}
              </select>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="px-3 py-2 border rounded-md text-sm"
              >
                <option value="all">Tutte le categorie</option>
                {uniqueCategories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Leads Table */}
        <Card>
          <CardHeader>
            <CardTitle>Leads</CardTitle>
            <CardDescription>
              {filteredLeads.length} di {leads.length} leads
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <TableHead key={header.id}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </TableHead>
                      ))}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {table.getRowModel().rows?.length ? (
                    table.getRowModel().rows.map((row) => (
                      <TableRow key={row.id}>
                        {row.getVisibleCells().map((cell) => (
                          <TableCell key={cell.id}>
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={columns.length} className="h-24 text-center">
                        Nessun lead trovato con i filtri selezionati
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
            
            <DataTablePagination table={table} />
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default AdminLeads;