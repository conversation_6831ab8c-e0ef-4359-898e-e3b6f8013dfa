
import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Search, Lock, Unlock, User, Building } from 'lucide-react';
import { GitHubRepoData } from '@/types/github';

interface GitHubRepositorySelectorProps {
  open: boolean;
  onClose: () => void;
  repositories: GitHubRepoData[];
  selectedRepositories: GitHubRepoData[];
  accessType: 'all' | 'selected';
  onSave: (accessType: 'all' | 'selected', selectedRepos: GitHubRepoData[]) => void;
  loading?: boolean;
}

export const GitHubRepositorySelector = ({
  open,
  onClose,
  repositories,
  selectedRepositories,
  accessType,
  onSave,
  loading = false
}: GitHubRepositorySelectorProps) => {
  const [localAccessType, setLocalAccessType] = useState<'all' | 'selected'>(accessType);
  const [localSelectedRepos, setLocalSelectedRepos] = useState<GitHubRepoData[]>(selectedRepositories);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    setLocalAccessType(accessType);
    setLocalSelectedRepos(selectedRepositories);
  }, [accessType, selectedRepositories]);

  const filteredRepositories = repositories.filter(repo =>
    repo.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    repo.full_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleRepositoryToggle = (repo: GitHubRepoData) => {
    setLocalSelectedRepos(prev => {
      const isSelected = prev.some(r => r.id === repo.id);
      if (isSelected) {
        return prev.filter(r => r.id !== repo.id);
      } else {
        return [...prev, repo];
      }
    });
  };

  const handleSave = () => {
    onSave(localAccessType, localAccessType === 'all' ? [] : localSelectedRepos);
  };

  const isRepoSelected = (repo: GitHubRepoData) => {
    return localSelectedRepos.some(r => r.id === repo.id);
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Seleziona Repository GitHub</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <RadioGroup
            value={localAccessType}
            onValueChange={(value) => setLocalAccessType(value as 'all' | 'selected')}
            className="space-y-4"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="all" id="all" />
              <Label htmlFor="all" className="flex-1">
                <div>
                  <div className="font-medium">Tutte le repository</div>
                  <div className="text-sm text-gray-500">
                    Accesso a tutte le repository attuali e future
                  </div>
                </div>
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="selected" id="selected" />
              <Label htmlFor="selected" className="flex-1">
                <div>
                  <div className="font-medium">Repository selezionate</div>
                  <div className="text-sm text-gray-500">
                    Scegli specifiche repository da connettere
                  </div>
                </div>
              </Label>
            </div>
          </RadioGroup>

          {localAccessType === 'selected' && (
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Cerca repository..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <ScrollArea className="h-[300px] border rounded-md p-4">
                <div className="space-y-3">
                  {filteredRepositories.map(repo => (
                    <div
                      key={repo.id}
                      className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50"
                    >
                      <Checkbox
                        checked={isRepoSelected(repo)}
                        onCheckedChange={() => handleRepositoryToggle(repo)}
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium truncate">{repo.name}</span>
                          {repo.private ? (
                            <Lock className="h-4 w-4 text-gray-500" />
                          ) : (
                            <Unlock className="h-4 w-4 text-gray-500" />
                          )}
                          {repo.owner.type === 'Organization' ? (
                            <Building className="h-4 w-4 text-blue-500" />
                          ) : (
                            <User className="h-4 w-4 text-green-500" />
                          )}
                        </div>
                        <div className="text-sm text-gray-500 truncate">
                          {repo.full_name}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>

              {localAccessType === 'selected' && (
                <div className="text-sm text-gray-600">
                  {localSelectedRepos.length} repository selezionate
                </div>
              )}
            </div>
          )}

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose} disabled={loading}>
              Annulla
            </Button>
            <Button onClick={handleSave} disabled={loading}>
              {loading ? 'Salvataggio...' : 'Salva Selezione'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
