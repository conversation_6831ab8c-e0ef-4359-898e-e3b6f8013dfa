import { <PERSON><PERSON>, Plus, <PERSON>ting<PERSON>, Mic, Phone, MessageSquare, User } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Switch } from "@/components/ui/switch";
import MainLayout from "@/layouts/MainLayout";
import { useBusinessStore } from "@/store/businessStore";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";
import { CreateAgentDialog } from "@/components/CreateAgentDialog";
import { useAgentLimits } from "@/hooks/useAgentLimits";
import { useQueryClient } from "@tanstack/react-query";

type AIAgent = Database['public']['Tables']['ai_business_agents']['Row'];

const AIAgents = () => {
  const selectedBusiness = useBusinessStore(state => state.selectedBusiness);
  const [agents, setAgents] = useState<AIAgent[]>([]);
  const [loading, setLoading] = useState(true);
  const { data: agentLimits } = useAgentLimits(selectedBusiness?.id);
  const queryClient = useQueryClient();

  useEffect(() => {
    if (selectedBusiness) {
      fetchAgents();
    }
  }, [selectedBusiness]);

  const fetchAgents = async () => {
    if (!selectedBusiness) return;

    try {
      const { data, error } = await supabase
        .from('ai_business_agents')
        .select('*')
        .eq('business_id', selectedBusiness.id)
        .order('created_at', { ascending: true });

      if (error) throw error;
      setAgents(data || []);
    } catch (error) {
      console.error('Error fetching agents:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleAgent = async (agentId: string, isActive: boolean) => {
    try {
      const { error } = await supabase
        .from('ai_business_agents')
        .update({ is_active: isActive })
        .eq('id', agentId);

      if (error) throw error;
      
      // Update local state
      setAgents(prev => prev.map(agent => 
        agent.id === agentId ? { ...agent, is_active: isActive } : agent
      ));

      // Invalidate agent limits cache to reflect changes immediately
      queryClient.invalidateQueries({ 
        queryKey: ["agentLimits", selectedBusiness?.id] 
      });
    } catch (error) {
      console.error('Error updating agent:', error);
    }
  };

  const getAgentIcon = (type: string) => {
    switch (type) {
      case 'booking':
        return Phone;
      case 'customer_support':
        return MessageSquare;
      case 'sales':
        return User;
      default:
        return Bot;
    }
  };

  const getAgentTypeLabel = (type: string) => {
    switch (type) {
      case 'booking':
        return 'Prenotazioni';
      case 'customer_support':
        return 'Supporto Clienti';
      case 'sales':
        return 'Vendite';
      default:
        return type;
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          <div className="animate-pulse">
            <div className="h-8 w-64 bg-gray-300 rounded mb-2"></div>
            <div className="h-4 w-96 bg-gray-300 rounded mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-300 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Agenti AI Voice</h1>
            <p className="text-gray-600">Gestisci i tuoi assistenti virtuali intelligenti</p>
            {selectedBusiness && (
              <p className="text-sm text-gray-500 mt-1">Attività: {selectedBusiness.name}</p>
            )}
          </div>
          {selectedBusiness && (
            <CreateAgentDialog 
              businessId={selectedBusiness.id} 
              onAgentCreated={fetchAgents}
            />
          )}
        </div>

        {!selectedBusiness ? (
          <div className="flex flex-col items-center justify-center py-16 text-gray-500 bg-gray-50 rounded-lg border border-gray-200">
            <Bot className="h-12 w-12 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium mb-2">Nessuna attività selezionata</h3>
            <p className="text-gray-500 max-w-md text-center">
              Seleziona un'attività dal menu laterale per visualizzare e gestire i tuoi agenti AI.
            </p>
          </div>
        ) : agents.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-16 text-gray-500 bg-gray-50 rounded-lg border border-gray-200">
            <Bot className="h-12 w-12 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium mb-2">Nessun agente configurato</h3>
            <p className="text-gray-500 max-w-md text-center mb-4">
              Inizia creando il tuo primo agente AI per automatizzare le interazioni con i clienti.
            </p>
            {selectedBusiness && (
              <CreateAgentDialog 
                businessId={selectedBusiness.id} 
                onAgentCreated={fetchAgents}
              />
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
            {agents.map((agent) => {
              const IconComponent = getAgentIcon(agent.agent_type);
              return (
                <Card key={agent.id} className="relative">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-12 w-12">
                          <AvatarImage src={agent.avatar_url || undefined} />
                          <AvatarFallback>
                            <IconComponent className="h-6 w-6" />
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <CardTitle className="text-lg">{agent.name}</CardTitle>
                          <CardDescription className="text-sm">
                            {getAgentTypeLabel(agent.agent_type)}
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={agent.is_active}
                          onCheckedChange={(checked) => toggleAgent(agent.id, checked)}
                        />
                        <Badge variant={agent.is_active ? "default" : "secondary"}>
                          {agent.is_active ? "Attivo" : "Inattivo"}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {agent.description && (
                      <p className="text-sm text-gray-600">{agent.description}</p>
                    )}
                    
                    <div className="space-y-2">
                      {agent.voice_id && (
                        <div className="flex items-center gap-2 text-sm">
                          <Mic className="h-4 w-4 text-gray-400" />
                          <span>Voce: {agent.voice_id}</span>
                        </div>
                      )}
                      
                      {agent.personality_style && (
                        <div className="flex items-center gap-2 text-sm">
                          <User className="h-4 w-4 text-gray-400" />
                          <span>Stile: {agent.personality_style}</span>
                        </div>
                      )}
                    </div>

                    <div className="flex gap-2 pt-2">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="flex-1"
                        onClick={() => window.location.href = `/ai-agents/${agent.id}/config`}
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        Configura
                      </Button>
                      <Button variant="outline" size="sm">
                        <Phone className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        {/* Agent limits info */}
        {agentLimits && (
          <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-blue-900">Limiti Piano {agentLimits.tierType}</h3>
              <Badge variant="outline" className="bg-white">
                {agentLimits.currentActiveAgents}/{agentLimits.maxAgents} agenti attivi
              </Badge>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-blue-800 mb-2">
                  <strong>Agenti disponibili per il tuo piano:</strong>
                </p>
                <div className="flex flex-wrap gap-2">
                  {agentLimits.allowedAgentDefinitions.map(def => (
                    <Badge key={def.id} variant="secondary" className="text-xs">
                      {getAgentTypeLabel(def.agent_type)}
                    </Badge>
                  ))}
                </div>
              </div>
              <div>
                <p className="text-blue-800 mb-2">
                  <strong>💡 Suggerimento:</strong>
                </p>
                <p className="text-blue-700 text-xs">
                  Puoi avere un solo agente attivo per tipo. Configura le loro personalità 
                  per ottenere il massimo dalle interazioni con i tuoi clienti.
                </p>
              </div>
            </div>
          </div>
        )}
        {/* Agent limits info */}
        <div className="mt-8 p-6 bg-blue-50 rounded-lg border border-blue-200">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">💡 Suggerimento</h3>
          <p className="text-blue-800 text-sm">
            Gli agenti AI Voice possono gestire automaticamente le chiamate in arrivo, prenotazioni, 
            supporto clienti e molto altro. Configura le loro personalità e istruzioni per ottenere 
            il massimo dalle interazioni con i tuoi clienti.
          </p>
        </div>
      </div>
    </MainLayout>
  );
};

export default AIAgents;