
import { Card, CardContent } from "@/components/ui/card";
import { Settings } from "lucide-react";

interface IntegrationsEmptyStateProps {
  type: 'no-business' | 'no-results';
}

export const IntegrationsEmptyState = ({ type }: IntegrationsEmptyStateProps) => {
  if (type === 'no-business') {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-16">
          <Settings className="h-12 w-12 text-gray-300 mb-4" />
          <h3 className="text-lg font-medium mb-2">Seleziona un'attività</h3>
          <p className="text-gray-500 text-center max-w-md">
            Seleziona un'attività dal menu laterale per gestire le integrazioni
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="flex flex-col items-center justify-center py-16">
        <Settings className="h-12 w-12 text-gray-300 mb-4" />
        <h3 className="text-lg font-medium mb-2">Nessuna integrazione trovata</h3>
        <p className="text-gray-500 text-center max-w-md">
          Prova a modificare i filtri di ricerca o la categoria selezionata
        </p>
      </CardContent>
    </Card>
  );
};
