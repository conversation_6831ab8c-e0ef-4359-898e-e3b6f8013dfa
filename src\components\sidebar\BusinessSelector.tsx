
import { useState, useEffect } from "react";
import { Business } from "@/store/businessStore";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useToast } from "@/components/ui/use-toast";
import {
  ChevronsUpDown,
  ChevronRight,
  PlusCircle,
  Search,
  Tag,
  PackageCheck,
  Filter,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem } from "../ui/sidebar";
import { useAuth } from "@/contexts/AuthContext";
import { useBusinessStore } from "@/store/businessStore";
import { useNavigate } from "react-router-dom";

interface BusinessSelectorProps {
  businesses: Business[];
  selectedBusiness: Business | null;
  isLoading: boolean;
  onSelectBusiness: (business: Business) => void;
}

export const BusinessSelector = ({
  businesses,
  selectedBusiness,
  isLoading,
  onSelectBusiness,
}: BusinessSelectorProps) => {

  const navigate = useNavigate();
  
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [showWithDeals, setShowWithDeals] = useState(false);
  const { setDefaultBusiness } = useBusinessStore();

  // Filtra i risultati in base al termine di ricerca e ai filtri selezionati
  const filteredBusinesses = businesses.filter((business) => {
    // Filtra prima per termine di ricerca
    const matchesSearch = business.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());

    // Poi applica il filtro "Con offerte" se selezionato
    const matchesDealsFilter = showWithDeals ? business.deal_count > 0 : true;

    return matchesSearch && matchesDealsFilter;
  });

  // Get business initials for avatar fallback
  const getBusinessInitials = (name: string) => {
    if (!name) return "B";

    const words = name.split(" ");
    if (words.length >= 2) {
      return `${words[0].charAt(0)}${words[1].charAt(0)}`.toUpperCase();
    }
    return name.charAt(0).toUpperCase();
  };

  // Handle new business click
  const handleNewBusinessClick = (e: React.MouseEvent) => {
    e.preventDefault();
   
    navigate("/create-business");
  };

  // Gestisci la selezione dell'attività
  const handleSelectBusiness = async (business: Business) => {
    // Aggiorna l'attività selezionata 
    onSelectBusiness(business);
    
    // Se l'utente è autenticato, imposta questa come predefinita
    if (user && business.id !== selectedBusiness?.id) {
      await setDefaultBusiness(user.id, business);
    }
  };

  // Toggle filter for businesses with deals
  const toggleShowWithDeals = () => {
    setShowWithDeals(!showWithDeals);
  };

  // Aggiungiamo console.log per il debug
  useEffect(() => {
    console.log("BusinessSelector - businesses:", businesses);
    console.log("BusinessSelector - filteredBusinesses:", filteredBusinesses);
    console.log("BusinessSelector - selectedBusiness:", selectedBusiness);
  }, [businesses, filteredBusinesses, selectedBusiness]);

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="flex items-center">
                {isLoading ? (
                  <div className="h-8 w-8 mr-2 rounded-full bg-gray-200 animate-pulse"></div>
                ) : (
                  <Avatar className="h-8 w-8 mr-2 bg-blue-100 border border-blue-200">
                    <AvatarFallback className="bg-blue-100 text-blue-600">
                      {selectedBusiness
                        ? getBusinessInitials(selectedBusiness.name)
                        : "B"}
                    </AvatarFallback>
                  </Avatar>
                )}
                <div className="flex flex-col items-start">
                  {isLoading ? (
                    <>
                      <span className="inline-block w-20 h-4 bg-gray-200 animate-pulse rounded"></span>
                      <span className="inline-block w-16 h-3 bg-gray-200 animate-pulse rounded mt-1"></span>
                    </>
                  ) : (
                    <>
                      <span className="text-sm font-medium">
                        {selectedBusiness?.name || "Seleziona Attività"}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {selectedBusiness?.deal_count || 0} offerte
                      </span>
                    </>
                  )}
                </div>
              </div>
              <ChevronsUpDown className="h-4 w-4 opacity-50" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="start"
            alignOffset={0}
            side="right"
            sideOffset={16}
            className="w-64"
          >
            <DropdownMenuLabel>Attività</DropdownMenuLabel>

            {/* Casella di ricerca */}
            <div className="px-2 py-1.5">
              <div className="flex items-center px-2 py-1 border rounded-md focus-within:ring-1 focus-within:ring-blue-500 bg-background">
                <Search className="h-4 w-4 mr-2 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder="Cerca attività..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="h-8 p-0 border-0 focus-visible:ring-0 focus-visible:ring-offset-0 text-sm"
                />
              </div>
            </div>

            {/* Sezione filtri */}
            <div className="px-2 py-1.5">
              <div className="flex items-center mb-1">
                <Filter className="h-4 w-4 mr-1 text-muted-foreground" />
                <span className="text-xs font-medium text-muted-foreground">
                  Filtri
                </span>
              </div>
              <div className="flex items-center space-x-2 px-1 py-1">
                <Checkbox
                  id="with-deals"
                  checked={showWithDeals}
                  onCheckedChange={toggleShowWithDeals}
                />
                <label
                  htmlFor="with-deals"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
                >
                  Con offerte
                  <Tag className="h-3.5 w-3.5 ml-1 text-blue-600" />
                </label>
              </div>
            </div>

            <DropdownMenuSeparator />

            {isLoading ? (
              <div className="p-2">
                <div className="flex items-center space-y-2">
                  <div className="h-5 w-5 rounded-full bg-gray-200 animate-pulse mr-2"></div>
                  <div className="h-4 w-32 bg-gray-200 animate-pulse rounded"></div>
                </div>
              </div>
            ) : filteredBusinesses.length === 0 ? (
              <div className="p-4 text-sm text-center">
                <div className="flex flex-col items-center justify-center text-muted-foreground">
                  <Filter className="h-10 w-10 mb-2 opacity-20" />
                  <p>Nessuna attività corrisponde ai filtri selezionati</p>
                  {showWithDeals && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="mt-2 text-xs"
                      onClick={() => setShowWithDeals(false)}
                    >
                      Rimuovi filtri
                    </Button>
                  )}
                </div>
              </div>
            ) : (
              <ScrollArea className="h-[200px] py-1">
                {filteredBusinesses.map((business) => (
                  <DropdownMenuItem
                    key={business.id}
                    className="flex items-center justify-between mx-1 pl-2 pr-1 py-2"
                    onClick={() => {
                      handleSelectBusiness(business);
                      setSearchTerm("");
                      setIsOpen(false);
                    }}
                  >
                    <div className="flex items-center">
                      <Avatar className="h-6 w-6 mr-2 bg-blue-100">
                        <AvatarFallback className="text-xs bg-blue-100 text-blue-600">
                          {getBusinessInitials(business.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <span className="text-sm">{business.name}</span>
                        <span className="text-xs text-muted-foreground">
                          {business.deal_count || 0} offerte
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      {business.deal_count > 0 && (
                        <Badge
                          variant="outline"
                          className="flex items-center h-5 px-1 border-blue-200 bg-blue-50 text-blue-700"
                        >
                          <PackageCheck className="h-3 w-3 mr-0.5" />
                          <span className="text-xs">{business.deal_count}</span>
                        </Badge>
                      )}
                      {selectedBusiness?.id === business.id && (
                        <ChevronRight className="h-4 w-4 ml-1 text-muted-foreground" />
                      )}
                    </div>
                  </DropdownMenuItem>
                ))}
              </ScrollArea>
            )}

            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <a
                      href="#"
                      className="flex items-center w-full"
                      onClick={handleNewBusinessClick}
                    >
                      <PlusCircle className="mr-2 h-4 w-4 text-blue-600" />
                      Crea nuova attività
                    </a>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Prossimamente!</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
};
