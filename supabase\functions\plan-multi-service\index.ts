import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

console.log('Environment check:', {
  hasOpenAIKey: !!openAIApiKey,
  supabaseUrl: supabaseUrl,
  hasServiceKey: !!supabaseServiceKey
});

const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface ServiceRequest {
  type: string;
  preferences: string[];
  constraints: {
    time?: string;
    location?: string;
    budget?: number;
  };
}

interface OptimizedPlan {
  services: Array<{
    service_type: string;
    deal: any;
    scheduled_time: string;
    travel_time_minutes: number;
    cost: number;
  }>;
  total_cost: number;
  total_time_minutes: number;
  route_optimized: boolean;
}

// Parse multi-service request using OpenAI
async function parseMultiServiceRequest(requestText: string): Promise<{
  services: ServiceRequest[];
  constraints: any;
  budget: number;
}> {
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${openAIApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: `Analizza una richiesta multi-servizio e restituisci JSON strutturato.
          
          Estrai:
          - services: array di servizi richiesti (parrucchiere, spa, ristorante, etc.)
          - constraints: vincoli temporali, geografici, budget
          - budget: budget totale se specificato
          
          Tipi di servizi comuni: parrucchiere, barbiere, spa, ristorante, hotel, palestra, bellezza, cinema, teatro, bar, aperitivo
          
          Rispondi SOLO con JSON valido.`
        },
        {
          role: 'user',
          content: requestText
        }
      ],
      temperature: 0.1
    }),
  });

  const data = await response.json();
  return JSON.parse(data.choices[0].message.content);
}

// Search deals for specific service type
async function searchDealsForService(serviceType: string, constraints: any): Promise<any[]> {
  const response = await fetch(`${supabaseUrl}/functions/v1/semantic-search`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${supabaseServiceKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      action: 'search',
      query: serviceType,
      filters: {
        budget_max: constraints.budget,
        city: constraints.location
      }
    }),
  });

  const result = await response.json();
  return result.deals || [];
}

// Calculate travel time between two points (simplified)
function calculateTravelTime(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const distance = Math.sqrt(Math.pow(lat2 - lat1, 2) + Math.pow(lng2 - lng1, 2));
  // Simplified: assume 30 minutes per degree (very rough approximation)
  return Math.round(distance * 30);
}

// Optimize multi-service plan
async function optimizeMultiServicePlan(
  services: ServiceRequest[],
  constraints: any,
  budget: number
): Promise<OptimizedPlan> {
  const serviceDeals: any[] = [];
  
  // Search deals for each service
  for (const service of services) {
    const deals = await searchDealsForService(service.type, {
      ...service.constraints,
      budget: budget / services.length, // Distribute budget evenly
      location: constraints.location
    });
    
    serviceDeals.push({
      service_type: service.type,
      deals: deals.slice(0, 5), // Top 5 deals
      constraints: service.constraints
    });
  }

  // Simple optimization: pick best deal for each service within budget
  const optimizedServices = [];
  let totalCost = 0;
  let totalTime = 0;
  let currentLat = 45.4642; // Milan center as default
  let currentLng = 9.1900;
  let currentTime = constraints.start_time || '14:00';

  for (let i = 0; i < serviceDeals.length; i++) {
    const serviceData = serviceDeals[i];
    const availableDeals = serviceData.deals.filter(
      (deal: any) => deal.discounted_price <= (budget - totalCost)
    );

    if (availableDeals.length === 0) continue;

    // Pick the deal with best price/distance ratio
    let bestDeal = availableDeals[0];
    let bestScore = 0;

    for (const deal of availableDeals) {
      if (deal.business_latitude && deal.business_longitude) {
        const travelTime = calculateTravelTime(
          currentLat, currentLng,
          deal.business_latitude, deal.business_longitude
        );
        
        // Score based on price and convenience
        const priceScore = (1 - deal.discounted_price / budget) * 0.6;
        const distanceScore = (1 - travelTime / 60) * 0.4; // Normalize travel time
        const score = priceScore + distanceScore;

        if (score > bestScore) {
          bestScore = score;
          bestDeal = deal;
        }
      }
    }

    const travelTime = bestDeal.business_latitude ? calculateTravelTime(
      currentLat, currentLng,
      bestDeal.business_latitude, bestDeal.business_longitude
    ) : 15; // Default 15 minutes

    // Calculate next time slot
    const timeSlots = currentTime.split(':');
    const currentMinutes = parseInt(timeSlots[0]) * 60 + parseInt(timeSlots[1]) + travelTime;
    const nextHour = Math.floor(currentMinutes / 60);
    const nextMinute = currentMinutes % 60;
    const scheduledTime = `${nextHour.toString().padStart(2, '0')}:${nextMinute.toString().padStart(2, '0')}`;

    optimizedServices.push({
      service_type: serviceData.service_type,
      deal: bestDeal,
      scheduled_time: scheduledTime,
      travel_time_minutes: travelTime,
      cost: bestDeal.discounted_price
    });

    totalCost += bestDeal.discounted_price;
    totalTime += travelTime + 90; // Assume 90 minutes per service
    currentLat = bestDeal.business_latitude || currentLat;
    currentLng = bestDeal.business_longitude || currentLng;
    currentTime = scheduledTime;
  }

  return {
    services: optimizedServices,
    total_cost: totalCost,
    total_time_minutes: totalTime,
    route_optimized: true
  };
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (!openAIApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const { request_text, user_id } = await req.json();

    if (!request_text || !user_id) {
      throw new Error('request_text and user_id are required');
    }

    console.log('Processing multi-service request:', request_text);

    // Parse the request
    const parsed = await parseMultiServiceRequest(request_text);
    console.log('Parsed request:', parsed);

    // Optimize the plan
    const optimizedPlan = await optimizeMultiServicePlan(
      parsed.services,
      parsed.constraints,
      parsed.budget
    );

    console.log('Optimized plan:', optimizedPlan);

    // Save the plan to database
    const { data: savedPlan, error: saveError } = await supabase
      .from('multi_service_plans')
      .insert({
        user_id,
        request_text,
        services: parsed.services,
        total_budget: parsed.budget,
        optimized_plan: optimizedPlan,
        status: 'optimized'
      })
      .select()
      .single();

    if (saveError) {
      console.error('Error saving plan:', saveError);
      throw saveError;
    }

    // Create coordinated bookings entries
    const coordinatedBookings = optimizedPlan.services.map((service, index) => ({
      plan_id: savedPlan.id,
      deal_id: service.deal.deal_id,
      service_order: index + 1,
      scheduled_time: `${new Date().toISOString().split('T')[0]}T${service.scheduled_time}:00Z`,
      travel_time_minutes: service.travel_time_minutes,
      status: 'planned'
    }));

    if (coordinatedBookings.length > 0) {
      const { error: bookingsError } = await supabase
        .from('coordinated_bookings')
        .insert(coordinatedBookings);

      if (bookingsError) {
        console.error('Error saving coordinated bookings:', bookingsError);
      }
    }

    // Generate human-readable response
    const responseText = generateResponseText(optimizedPlan, parsed.budget);

    return new Response(
      JSON.stringify({
        success: true,
        plan_id: savedPlan.id,
        optimized_plan: optimizedPlan,
        response_text: responseText,
        savings: Math.max(0, parsed.budget - optimizedPlan.total_cost)
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Error in plan-multi-service function:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});

function generateResponseText(plan: OptimizedPlan, budget: number): string {
  if (plan.services.length === 0) {
    return "Mi dispiace, non sono riuscito a trovare una combinazione di servizi che rispetti il tuo budget. Potresti aumentare il budget o essere più flessibile sui servizi?";
  }

  let response = "Ho trovato un percorso perfetto per te! ";
  
  plan.services.forEach((service, index) => {
    if (index > 0) response += ", poi ";
    response += `${service.deal.title} da ${service.deal.business_name} (€${service.cost}) alle ${service.scheduled_time}`;
  });

  response += `. Totale: €${plan.total_cost}`;
  
  if (budget > plan.total_cost) {
    response += `, risparmi €${budget - plan.total_cost}`;
  }
  
  response += "! Vuoi che prenoti tutto?";
  
  return response;
}