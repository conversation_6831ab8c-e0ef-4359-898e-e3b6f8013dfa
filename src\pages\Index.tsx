import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Calendar, Bot, Clock, Store, Users, Star, Shield, ChevronRight, BarChart3, CheckCircle2, Heart, Share2, User, Wand2, LayoutGrid, LayoutList, Hexagon, Columns } from "lucide-react";
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import Agent from "@/components/Agent";
import AssistantToast from "@/components/AssistantToast";
import TeamMemberCard from "@/components/TeamMemberCard";
import TeamCarousel from "@/components/TeamCarousel";
import TeamGrid from "@/components/TeamGrid";
import TeamHexGrid from "@/components/TeamHexGrid";
import ChallengesSolution from "@/components/ChallengesSolution";
import FloatingMenuBar from "@/components/floatingbarmenu/FloatingMenuBar";
import { teamPlaceholders } from "@/assets/team-placeholders";
import { teamAvatars } from "@/assets/team-avatars";
import "@/styles/highlight.css";
import "@/styles/hide-scrollbar.css";
import "@/styles/hex-grid.css";
import { assistantAvatar } from "@/assets/assistant-avatar";
import ScrollToTop from "@/components/scrolltop/ScrollToTop";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import BusinessCarousel from "@/components/BusinessCarousel";
import { usePricingTiers, formatPrice } from "@/queries/usePricingTiers";
import { PricingCard } from "@/components/PricingCard";
const Index = () => {
  const [activeSection, setActiveSection] = useState("hero");
  const [scrolled, setScrolled] = useState(false);
  const [teamLayout, setTeamLayout] = useState<"featured" | "carousel" | "grid" | "hex">("hex");
  const [isTestMode, setIsTestMode] = useState(false);
  const [isYearly, setIsYearly] = useState(false);

  // Fetch pricing tiers from database
  const {
    data: pricingTiers,
    isLoading: pricingLoading,
    error: pricingError
  } = usePricingTiers();

  // Team members data
  const teamMembers = [{
    name: "Sofia - Responsabile Prenotazioni",
    role: "Responsabile Prenotazioni",
    description: "Risponde 24/7, consiglia orari disponibili, propone sconti nelle ore meno frequentate e conferma tramite SMS o email.",
    avatar: teamAvatars.sofia,
    videoPlaceholder: teamPlaceholders.sofia
  }, {
    name: "Marco - Specialista Vendite",
    role: "Specialista Vendite",
    description: "Suggerisce servizi aggiuntivi, pacchetti o prodotti in base alle preferenze del cliente, aumentando il valore medio per prenotazione.",
    avatar: teamAvatars.marco,
    videoPlaceholder: teamPlaceholders.marco
  }, {
    name: "Giulia - Analista Dati",
    role: "Analista Dati",
    description: "Ti mostra quali orari sono più richiesti, quali offerte funzionano meglio e ti suggerisce miglioramenti strategici.",
    avatar: teamAvatars.giulia,
    videoPlaceholder: teamPlaceholders.giulia
  }, {
    name: "Luca - Specialista Marketingi",
    role: "Specialista Servizio Marketing",
    description: "Genera contenuti per i social media, promuove con campagne mirate e raccoglie feedback per migliorare costantemente la visibilità online.",
    avatar: teamAvatars.luca,
    videoPlaceholder: teamPlaceholders.luca
  }, {
    name: "Anna - Specialista Supporto Clienti",
    role: "Specialista Supporto Clienti",
    description: "Risponde a domande, risolve problemi e raccoglie feedback dai clienti, garantendo un'assistenza rapida ed efficace per massimizzare la soddisfazione.",
    avatar: teamAvatars.anna,
    videoPlaceholder: teamPlaceholders.anna
  }];
  useEffect(() => {
    const handleScroll = () => {
      const sections = [{
        id: "hero",
        offset: 0
      }, {
        id: "problemi",
        offset: document.getElementById("problemi")?.offsetTop || 0
      }, {
        id: "interazione",
        offset: document.getElementById("interazione")?.offsetTop || 0
      }, {
        id: "team",
        offset: document.getElementById("team")?.offsetTop || 0
      }, {
        id: "vantaggi",
        offset: document.getElementById("vantaggi")?.offsetTop || 0
      }, {
        id: "testimonianze",
        offset: document.getElementById("testimonianze")?.offsetTop || 0
      }, {
        id: "prezzi",
        offset: document.getElementById("prezzi")?.offsetTop || 0
      }, {
        id: "faq",
        offset: document.getElementById("faq")?.offsetTop || 0
      }];
      const scrollPosition = window.scrollY + 100;
      setScrolled(window.scrollY > 100);
      for (let i = sections.length - 1; i >= 0; i--) {
        if (scrollPosition >= sections[i].offset) {
          setActiveSection(sections[i].id);
          break;
        }
      }
    };
    window.addEventListener("scroll", handleScroll);
    handleScroll();
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      window.scrollTo({
        top: element.offsetTop - 80,
        behavior: "smooth"
      });
    }
  };
  return <div className="min-h-screen bg-gradient-to-b from-primary-50 to-white">
      <AssistantToast />

      <nav className="border-b bg-white/80 backdrop-blur-sm fixed w-full z-50">
        <div className="container mx-auto px-4 h-16 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Calendar className="h-6 w-6 text-primary-600" />
            <div className="flex items-end space-x-2">
              <span className="text-2xl text-gray-900 font-bold">
                CatchUp Manager
              </span>
              
              <span className="text-xs text-primary-600 -mb-1 font-semibold">
                Ti riempiamo gli orari di bassa affluenza.
              </span>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" className="text-gray-600" asChild>
              <Link to="/auth">Accedi</Link>
            </Button>
            <Button className="bg-primary-600 hover:bg-primary-700 text-white" asChild>
              <Link to="/auth">Inizia Ora</Link>
            </Button>
          </div>
        </div>
      </nav>

      {/* Menu di navigazione flottante */}
      <FloatingMenuBar items={[{
      id: "hero",
      label: "Home"
    }, {
      id: "problemi",
      label: "Problemi"
    }, {
      id: "interazione",
      label: "Assistente"
    }, {
      id: "team",
      label: "Team AI"
    }, {
      id: "vantaggi",
      label: "Vantaggi"
    }, {
      id: "testimonianze",
      label: "Clienti"
    }, {
      id: "prezzi",
      label: "Prezzi"
    }, {
      id: "faq",
      label: "FAQ"
    }]} activeItemId={activeSection} topOffset={80} onItemClick={scrollToSection} />

      <section id="hero" className="pt-24 sm:pt-28 md:pt-32 pb-16 sm:pb-20 px-4 md:px-8 lg:px-12 container mx-auto">
        <div className="grid md:grid-cols-2 gap-8 items-center">
          <motion.div initial={{
          opacity: 0,
          y: 20
        }} animate={{
          opacity: 1,
          y: 0
        }} transition={{
          duration: 0.5
        }} className="flex flex-col space-y-4 sm:space-y-6">
            <div className="relative mb-2">
              <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-xs font-medium">
                Prova Gratuita di 90 Giorni
              </span>
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-gray-900 leading-tight">
              Entra nel marketplace di CatchUp {" "} e <span className="bg-gradient-to-r from-primary-600 via-primary-500 to-primary-700 bg-clip-text text-transparent">
                Triplica le tue Prenotazioni
              </span>
            </h1>
            <p className="text-lg text-gray-600 leading-relaxed">
              Dedicati a cio' che per te è importante!<br /> Alla gestione
              dell'agenda ed alle campagne marketing ci pensa CatchUp,
              un marketplace in cui le aziende che offrono servizi si incontrano con i consumatori.
              Il tuo <span className="font-bold text-primary-600">Team di Intelligenza Artificiale</span> che lavora per te 24/7 
              ed in 72 lingue, promuovendo gli orari meno frequentati per <span className="font-bold text-primary-600">riempire tutti i posti liberi</span>.

            </p>
            <div className="flex flex-col sm:flex-row gap-4 pt-4">
              <Button size="lg" className="bg-primary-600 hover:bg-primary-700 text-white">
                Prova Gratis per 90 Giorni
              </Button>
              <Button variant="outline" size="lg" className="border-primary-200 text-primary-700 hover:bg-primary-50">
                <span>Guarda Demo di 7 Minuti</span>
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
            <div className="flex items-center space-x-2 pt-2">
              <CheckCircle2 className="h-5 w-5 text-green-500" />
              <span className="text-gray-600">Nessuna carta di credito richiesta</span>
            </div>
            <div className="mt-4 relative">
              <motion.div initial={{
              opacity: 0
            }} animate={{
              opacity: 1
            }} transition={{
              duration: 0.8,
              delay: 0.5
            }} className="flex items-center space-x-4 bg-white/90 backdrop-blur-sm p-3 rounded-lg shadow-sm border border-gray-100">
                <div className="flex-shrink-0 bg-primary-50 p-2 rounded-full">
                  <Bot className="h-5 w-5 text-primary-600" />
                </div>
                <div className="text-sm">
                  <p className="font-medium text-gray-800">
                    Prova CatchUp gratis per 90 giorni
                  </p>
                  <p className="text-gray-500">
                    Garantiamo un aumento del 35% delle prenotazioni
                  </p>
                </div>
              </motion.div>
            </div>
          </motion.div>

          <div id="agent-component" className="relative">
            <Agent />
          </div>
        </div>
      </section>

      <section id="problemi" className="py-12 sm:py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8 sm:mb-12">
            <motion.h2 initial={{
            opacity: 0,
            y: 20
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.5
          }} className="text-3xl md:text-4xl font-bold text-gray-900">
              <span className="text-primary-600">Orari di bassa affluenza?</span>{" "}
              Le soluzioni CatchUp
            </motion.h2>
          </div>

          <div className="grid md:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {[{
            problem: "Slot Vuoti",
            solution: "Fascie orarie di bassa frequenza? Il tuo assistente IA negozia in autonomia il prezzo del servizio in modo da far alzare la richiesta.",
            icon: <BarChart3 className="h-10 w-10 text-primary-600" />,
            delay: 0.3
          }, {
            problem: "Prenotazioni Perse",
            solution: "Ansia di perdere prenotazioni? Il tuo assistente IA risponde 24 ore su 24, non perdendo mai un'opportunità.",
            icon: <Clock className="h-10 w-10 text-primary-600" />,
            delay: 0.1
          }, {
            problem: "Staff Sovraccarico",
            solution: "Il tuo team perde tempo prezioso al telefono? Ci pensa il tuo assistente IA, rispondendo ad ogni domanda dei tuoi clienti. ",
            icon: <Users className="h-10 w-10 text-primary-600" />,
            delay: 0.2
          }, {
            problem: "Visibilità Sui Social",
            solution: "Difficoltà a gestire la presenza online? Il tuo assistente IA crea e pubblica campagne marketing sui social",
            icon: <Share2 className="h-10 w-10 text-primary-600" />,
            delay: 0.4
          }].map((item, index) => <motion.div key={index} initial={{
            opacity: 0,
            y: 20
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.5,
            delay: item.delay
          }} className="bg-white p-6 rounded-xl shadow-md border border-gray-100 hover:shadow-lg transition-shadow text-center">
                <div className="bg-primary-50 p-4 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                  {item.icon}
                </div>
                <h3 className="text-xl font-bold mb-2 text-gray-900">
                  {item.problem}
                </h3>
                <p className="text-gray-600">{item.solution}</p>
              </motion.div>)}
          </div>
        </div>
      </section>
    {/* 
          <section id="interazione" className="py-12 sm:py-16 md:py-20 bg-white">
            <div className="container mx-auto px-4">
              <div className="text-center mb-8 sm:mb-12">
                <motion.h2
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5 }}
                  className="text-3xl md:text-4xl font-bold text-gray-900"
                >
                  Un Assistente che{" "}
                  <span className="text-primary-600">Parla e Capisce</span> come una
                  Persona
                </motion.h2>
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto"
                >
                  I tuoi clienti parlano e l'assistente fa il resto
                </motion.p>
              </div>
               <div className="flex flex-col lg:flex-row items-center gap-8 mb-8">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5 }}
                  className="lg:w-1/2"
                >
                  <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                    <img
                      src="/team.png"
                      alt="Voice Interaction"
                      className="w-full mb-4"
                    />
                    {/* <div className="bg-gray-50 rounded-t-lg p-4 flex items-center border-b">
                      <Mic className="h-6 w-6 text-primary-600 mr-2" />
                      <div className="flex-1">
                        <h4 className="font-medium">Il Tuo Assistente CatchUp</h4>
                        <p className="text-xs text-gray-500">Attivo 24 ore su 24, 7 giorni su 7</p>
                      </div>
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">Online</span>
                    </div>
                     <div className="p-4 space-y-4">
                      <div className="flex">
                        <div className="bg-primary-100 text-primary-800 rounded-lg p-3 max-w-[80%]">
                          <p className="text-sm">Buongiorno! Sono l'assistente di Trattoria Bella Napoli. Come posso aiutarti oggi?</p>
                        </div>
                      </div>
                       <div className="flex justify-end">
                        <div className="bg-gray-100 rounded-lg p-3 max-w-[80%]">
                          <p className="text-sm">Vorrei prenotare un tavolo per domani sera per 4 persone.</p>
                        </div>
                      </div>
                       <div className="flex">
                        <div className="bg-primary-100 text-primary-800 rounded-lg p-3 max-w-[80%]">
                          <p className="text-sm">Certamente! Per domani sera abbiamo disponibilità alle 19:30 e alle 21:00. Inoltre, se prenoti alle 19:30, c'è uno sconto del 15% sul menu degustazione. Ti interessa?</p>
                        </div>
                      </div>
                       <div className="flex justify-end">
                        <div className="bg-gray-100 rounded-lg p-3 max-w-[80%]">
                          <p className="text-sm">Perfetto, prenoto alle 19:30 per approfittare dello sconto. Grazie!</p>
                        </div>
                      </div>
                       <div className="flex">
                        <div className="bg-primary-100 text-primary-800 rounded-lg p-3 max-w-[80%]">
                          <p className="text-sm">Ottima scelta! Ho prenotato un tavolo per 4 persone domani alle 19:30 con lo sconto del 15% sul menu degustazione. Riceverai una conferma via SMS. Posso aiutarti con altro?</p>
                        </div>
                      </div>
                       <div className="flex items-center pt-4 border-t">
                        <div className="flex-1 relative">
                          <div className="flex items-center justify-center">
                            <div className="relative">
                               <div className="absolute inset-0 rounded-full bg-primary-400 animate-ping opacity-30" style={{ animationDuration: '2s' }}></div>
                              <Button
                                size="sm"
                                className="rounded-full bg-primary-600 hover:bg-primary-700 relative z-10 p-0 overflow-hidden"
                                onClick={() => {
                                  const agentElement = document.getElementById('agent-component');
                                  if (agentElement) {
                                    agentElement.scrollIntoView({ behavior: 'smooth' });
                                    agentElement.classList.add('highlight-pulse');
                                    setTimeout(() => {
                                      agentElement.classList.remove('highlight-pulse');
                                    }, 2000);
                                  }
                                }}
                              >
                                <img src={assistantAvatar.url} alt="Assistente" className="w-8 h-8 object-cover" />
                              </Button>
                            </div>
                            <span className="ml-2 text-sm text-gray-500">Prova l'assistente vocale!</span>
                          </div>
                        </div>
                      </div>
                    </div> 
                  </div>
                </motion.div>
                 <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="lg:w-1/2"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[
                      {
                        icon: (
                          <div className="rounded-full overflow-hidden w-8 h-8 border-2 border-primary-400">
                            <img
                              src={assistantAvatar.url}
                              alt="Assistente"
                              className="w-full h-full object-cover"
                            />
                          </div>
                        ),
                        title: "Basta Parlare Normalmente",
                        description:
                          "I clienti parlano con l'assistente come farebbero con una persona vera, usando frasi naturali senza comandi speciali.",
                      },
                      {
                        icon: <User className="h-8 w-8 text-primary-600" />,
                        title: "Zero Attese, Risposta Immediata",
                        description:
                          "L'assistente risponde subito, 24 ore su 24, anche quando tu e il tuo staff non siete disponibili.",
                      },
                      {
                        icon: <Bot className="h-8 w-8 text-primary-600" />,
                        title: "Sa Quello Che Offri",
                        description:
                          "Configurato con i tuoi servizi, prezzi e disponibilità, risponde con precisione e propone le tue offerte speciali.",
                      },
                      {
                        icon: <Wand2 className="h-8 w-8 text-primary-600" />,
                        title: "Gestisce Tutto Automaticamente",
                        description:
                          "Inserisce gli appuntamenti nel tuo calendario, invia conferme e ti libera dalle attività ripetitive.",
                      },
                    ].map((step, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.5, delay: 0.1 * index }}
                        className="bg-white p-5 rounded-xl shadow-md border border-gray-100 hover:shadow-lg transition-shadow"
                      >
                        <div className="bg-primary-50 p-3 rounded-full w-fit mb-3">
                          {step.icon}
                        </div>
                        <h3 className="text-lg font-semibold mb-2 text-gray-900">
                          {step.title}
                        </h3>
                        <p className="text-gray-600 text-sm">{step.description}</p>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
             
              </div>
            </div>
          </section> */}
   <section id="sfide" className="py-12 sm:py-16 md:py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8 sm:mb-12 md:mb-16">
            <motion.h2 initial={{
            opacity: 0,
            y: 20
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.5
          }} className="text-3xl md:text-4xl font-bold text-gray-900">
              Per te un {" "}
              <span className="text-primary-600">Team di Agenti IA</span> che lavora anche di notte
            </motion.h2>
          </div>

          <ChallengesSolution left_column_title="Prima di CatchUp" right_column_title="Con CatchUp" challenges={[{
          question: "Perdi prenotazioni quando il tuo negozio è chiuso o il personale è occupato?",
          solution: "Il tuo team IA risponde 24/7, assicurando che ogni potenziale cliente riceva attenzione immediata, anche di notte o nei weekend."
        }, {
          question: "Il tuo staff perde tempo prezioso al telefono invece di concentrarsi sul servizio?",
          solution: "Il tuoi assistenti IA gestiscono tutte le prenotazioni e le domande frequenti, permettendo al tuo staff di concentrarsi sul cliente."
        }, {
          question: "Hai fasce orarie vuote che costano denaro alla tua attività?",
          solution: "Il tuo team IA promuove gli orari meno prenotati con offerte automatiche, ottimizzando il tuo calendario e massimizzando i ricavi."
        }, {
          question: "Fai fatica a mantenere un follow-up costante con i clienti dopo il servizio?",
          solution: "Il tuoi assistenti IA gestiscono automaticamente i follow-up, inviando promemoria, raccogliendo feedback e proponendo nuovi appuntamenti al momento giusto."
        }, {
          question: "Ti manca un servizio clienti efficiente che risponda rapidamente alle richieste?",
          solution: "Il tuo assistente IA del servizio clienti è disponibile 24/7 per rispondere a domande, risolvere problemi e raccogliere feedback, garantendo la massima soddisfazione dei clienti."
        }]} />
        </div>
      </section>
      <section id="team" className="py-12 sm:py-16 md:py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-6 sm:mb-8">
            <motion.h2 initial={{
            opacity: 0,
            y: 20
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.5
          }} className="text-3xl md:text-4xl font-bold text-gray-900">
              Incontra il Tuo <span className="text-primary-600">Team AI</span>
            </motion.h2>
            <motion.p initial={{
            opacity: 0,
            y: 20
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.5,
            delay: 0.1
          }} className="text-gray-600 mt-4 max-w-2xl mx-auto">
              Il tuo team di assistenti AI specializzati, ciascuno con un ruolo
              specifico per massimizzare i risultati
            </motion.p>
          </div>

                  {/* Layout Switcher */}
        {isTestMode ? <>
          <motion.div initial={{
            opacity: 0,
            y: 10
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.3
          }} className="flex justify-center gap-2 mb-10">
            <Button variant={teamLayout === "featured" ? "default" : "outline"} size="sm" onClick={() => setTeamLayout("featured")} className="flex items-center gap-1">
              <Columns className="h-4 w-4" />
              <span className="hidden sm:inline">Featured</span>
            </Button>
            <Button variant={teamLayout === "carousel" ? "default" : "outline"} size="sm" onClick={() => setTeamLayout("carousel")} className="flex items-center gap-1">
              <LayoutList className="h-4 w-4" />
              <span className="hidden sm:inline">Carousel</span>
            </Button>
            <Button variant={teamLayout === "grid" ? "default" : "outline"} size="sm" onClick={() => setTeamLayout("grid")} className="flex items-center gap-1">
              <LayoutGrid className="h-4 w-4" />
              <span className="hidden sm:inline">Grid</span>
            </Button>
            <Button variant={teamLayout === "hex" ? "default" : "outline"} size="sm" onClick={() => setTeamLayout("hex")} className="flex items-center gap-1">
              <Hexagon className="h-4 w-4" />
              <span className="hidden sm:inline">Hexagon</span>
            </Button>
          </motion.div>
          </> : null}
          {/* Featured Layout */}
          {teamLayout === "featured" && <>
              {/* Featured Team Member */}
              <motion.div initial={{
            opacity: 0,
            y: 20
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.5
          }} className="mb-12">
                <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-primary-100 max-w-4xl mx-auto">
                  <div className="flex flex-col md:flex-row">
                    {/* Featured Image */}
                    <div className="md:w-1/2 relative">
                      <img src={teamPlaceholders.sofia} alt="Sofia - Team Leader" className="w-full h-full object-cover aspect-video md:aspect-auto" />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end p-6 md:hidden">
                        <div className="text-white">
                          <h3 className="text-2xl font-bold">Sofia</h3>
                          <p className="text-primary-200 font-medium">Team Leader & Responsabile Prenotazioni</p>
                        </div>
                      </div>
                    </div>

                    {/* Featured Content */}
                    <div className="md:w-1/2 p-6 md:p-8 flex flex-col justify-center">
                      <div className="flex items-center mb-4">
                        <img src={teamAvatars.sofia} alt="Sofia" className="w-16 h-16 rounded-full object-cover border-2 border-primary-100 mr-4" />
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900">Sofia</h3>
                          <p className="text-primary-600 font-medium">Team Leader & Responsabile Prenotazioni</p>
                        </div>
                      </div>
                      <p className="text-gray-700 mb-6">
                        Risponde 24/7, consiglia orari disponibili, propone sconti nelle ore meno frequentate e conferma tramite SMS o email. Come Team Leader, coordina gli altri assistenti AI per garantire un'esperienza fluida e integrata.
                      </p>
                      <div className="flex flex-wrap gap-3">
                        <span className="bg-primary-50 text-primary-700 px-3 py-1 rounded-full text-sm font-medium">Prenotazioni</span>
                        <span className="bg-primary-50 text-primary-700 px-3 py-1 rounded-full text-sm font-medium">Coordinamento</span>
                        <span className="bg-primary-50 text-primary-700 px-3 py-1 rounded-full text-sm font-medium">Disponibile 24/7</span>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Team Grid */}
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
                {teamMembers.slice(1).map((member, index) => <TeamMemberCard key={index} name={member.name} role={member.role} description={member.description} avatar={member.avatar} videoPlaceholder={member.videoPlaceholder} delay={0.1 + index * 0.1} />)}
              </div>
            </>}

          {/* Carousel Layout */}
          {teamLayout === "carousel" && <TeamCarousel members={teamMembers} />}

          {/* Grid Layout */}
          {teamLayout === "grid" && <TeamGrid members={teamMembers} />}

          {/* Hexagon Layout */}
          {teamLayout === "hex" && <TeamHexGrid members={teamMembers} />}
        </div>
      </section>
      <section id="target" className="py-12 sm:py-16 md:py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8 sm:mb-12">
            <motion.h2 initial={{
            opacity: 0,
            y: 20
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.5
          }} className="text-3xl md:text-4xl font-bold text-gray-900">
              <span className="text-primary-600">Per chi è</span>
            </motion.h2>
          </div>

          <div className="flex justify-center">
            <motion.div initial={{
            opacity: 0,
            y: 20
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.5
          }} className="max-w-4xl">
              <BusinessCarousel />
            </motion.div>
          </div>
        </div>
   </section>
 
      <section id="vantaggi" className="py-12 sm:py-16 md:py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <motion.h2 initial={{
            opacity: 0,
            y: 20
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.5
          }} className="text-3xl md:text-4xl font-bold text-gray-900">
              Che cosa ottieni con {" "}
              <span className="text-primary-600">CatchUp</span>
            </motion.h2>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {[{
            icon: <Store className="h-8 w-8 text-primary-600" />,
            title: "Il primo marketplace di intelligenza artificale",
            description: "La tua attività compare direttamente su app e sito CatchUp, raggiungendo subito migliaia di consumatori in cerca di servizi, senza dover comprare traffico o fare campagne proprie."
          }, {
            icon: <BarChart3 className="h-8 w-8 text-primary-600" />,
            title: "Calendario smart & promozioni automatiche",
            description: "Un algoritmo suggerisce e attiva offerte mirate nelle ore vuote, trasformando capacità inutilizzata in vendite extra e nuovo fatturato."
          }, {
            icon: <Bot className="h-8 w-8 text-primary-600" />,
            title: "Assistenti digitali proattivo",
            description: "Agenti IA gestiscono promozioni, prenotazioni, vendite e marketing al posto tuo, riducendo il tempo speso per queste attività e tagliando i \"no-show\"."
          }, {
            icon: <Shield className="h-8 w-8 text-primary-600" />,
            title: "Pagamenti online integrati e sicuri",
            description: "Checkout online (Stripe/PayPal & wallet CatchUp), conferme automatiche, incassi istantanei e gestione no-show."
          }].map((benefit, index) => <motion.div key={index} initial={{
            opacity: 0,
            y: 20
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.5,
            delay: 0.1 * index
          }} className="bg-white p-6 rounded-xl shadow-md border border-gray-100 hover:shadow-lg transition-shadow">
                <div className="bg-primary-50 p-4 rounded-full w-16 h-16 flex items-center justify-center mb-4">
                  {benefit.icon}
                </div>
                <h3 className="text-xl font-bold mb-3 text-gray-900">
                  {benefit.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">{benefit.description}</p>
              </motion.div>)}
          </div>

          <motion.div initial={{
          opacity: 0,
          y: 20
        }} whileInView={{
          opacity: 1,
          y: 0
        }} viewport={{
          once: true
        }} transition={{
          duration: 0.5,
          delay: 0.4
        }} className="text-center pt-8">
            <Button className="bg-primary-600 hover:bg-primary-700 text-white">
              Inizia la Prova Gratuita
            </Button>
          </motion.div>
        </div>
      </section>
      
      {/*
       <section id="testimonianze" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <motion.h2
              initial={{
                opacity: 0,
                y: 20,
              }}
              whileInView={{
                opacity: 1,
                y: 0,
              }}
              viewport={{
                once: true,
              }}
              transition={{
                duration: 0.5,
              }}
              className="text-3xl md:text-4xl font-bold text-gray-900"
            >
              Cosa Dicono i Nostri{" "}
              <span className="text-primary-600">Clienti</span>
            </motion.h2>
          </div>
           <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                name: "Marco Rossi",
                role: "Ristorante Da Marco",
                content:
                  "Prima perdevo il 30% delle chiamate per prenotazioni. Ora il mio assistente risponde sempre e ha aumentato i clienti del 40%, soprattutto nelle fasce orarie prima vuote.",
                delay: 0.1,
              },
              {
                name: "Giulia Bianchi",
                role: "Salon Beauty",
                content:
                  "I clienti prenotano a qualsiasi ora e ricevono subito conferma. Il mio staff può concentrarsi solo sui trattamenti, senza interruzioni per gestire il telefono.",
                delay: 0.2,
              },
              {
                name: "Luca Verdi",
                role: "Palestra FitLife",
                content:
                  "All'inizio ero scettico sulla tecnologia, ma la facilità d'uso mi ha stupito. In 10 minuti l'assistente era operativo e ora gestisce tutte le iscrizioni ai corsi.",
                delay: 0.3,
              },
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{
                  opacity: 0,
                  y: 20,
                }}
                whileInView={{
                  opacity: 1,
                  y: 0,
                }}
                viewport={{
                  once: true,
                }}
                transition={{
                  duration: 0.5,
                  delay: testimonial.delay,
                }}
                className="bg-white p-6 rounded-xl shadow-md border border-gray-100"
              >
                <div className="flex items-center space-x-1 mb-4">
                  <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                  <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                  <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                  <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                  <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                </div>
                <p className="text-gray-600 mb-6 italic">
                  "{testimonial.content}"
                </p>
                <div className="flex items-center space-x-3">
                  <div className="bg-primary-100 text-primary-800 h-10 w-10 rounded-full flex items-center justify-center font-semibold">
                    {testimonial.name.charAt(0)}
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">
                      {testimonial.name}
                    </h4>
                    <p className="text-sm text-gray-600">{testimonial.role}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
       </section>
       */}
      <section className="py-20 bg-primary-600">
        <div className="container mx-auto px-4 text-center max-w-3xl">
          <motion.h2 initial={{
          opacity: 0,
          y: 20
        }} whileInView={{
          opacity: 1,
          y: 0
        }} viewport={{
          once: true
        }} transition={{
          duration: 0.5
        }} className="text-3xl md:text-4xl font-bold text-white mb-6">
            Prova Gratuita di 90 Giorni con Risultati Garantiti
          </motion.h2>
          <motion.p initial={{
          opacity: 0,
          y: 20
        }} whileInView={{
          opacity: 1,
          y: 0
        }} viewport={{
          once: true
        }} transition={{
          duration: 0.5,
          delay: 0.1
        }} className="text-primary-100 text-lg mb-8">
            Prova il tuo assistente AI gratis per 90 giorni e ottieni ......
          </motion.p>

          <motion.div initial={{
          opacity: 0,
          y: 20
        }} whileInView={{
          opacity: 1,
          y: 0
        }} viewport={{
          once: true
        }} transition={{
          duration: 0.5,
          delay: 0.2
        }} className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-primary-700 hover:bg-primary-50">
              Inizia la Prova Gratuita
            </Button>
            <Button size="lg" variant="outline" className="border-white text-primary-700 hover:bg-primary-700">
              Guarda la Demo di 7 Minuti
            </Button>
          </motion.div>
          <p className="text-primary-100 mt-4">
            Prova gratuita di 90 giorni. Nessuna carta di credito richiesta.
          </p>
        </div>
      </section>

      <section id="prezzi" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <motion.h2 initial={{
            opacity: 0,
            y: 20
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.5
          }} className="text-3xl md:text-4xl font-bold text-gray-900">
              Piani e <span className="text-primary-600">Prezzi</span>
            </motion.h2>
            <motion.p initial={{
            opacity: 0,
            y: 20
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.5,
            delay: 0.1
          }} className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              Scegli il piano più adatto alle esigenze della tua attività
            </motion.p>
          </div>

          {/* Monthly/Yearly Toggle */}
          <div className="flex items-center justify-center mb-8">
            <div className="flex items-center space-x-4 bg-white p-2 rounded-lg border border-gray-200">
              <Label htmlFor="billing-toggle" className={`text-sm font-medium transition-colors ${!isYearly ? 'text-primary-600' : 'text-gray-500'}`}>
                Mensile
              </Label>
              <Switch id="billing-toggle" checked={isYearly} onCheckedChange={setIsYearly} />
              <Label htmlFor="billing-toggle" className={`text-sm font-medium transition-colors ${isYearly ? 'text-primary-600' : 'text-gray-500'}`}>
                Annuale
              </Label>
              {isYearly && <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                  Risparmia fino al 17%
                </span>}
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {pricingLoading ?
          // Loading skeleton
          Array.from({
            length: 3
          }).map((_, index) => <div key={index} className="bg-white p-6 rounded-xl shadow-md border border-gray-100">
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded mb-4"></div>
                    <div className="h-8 bg-gray-200 rounded mb-4"></div>
                    <div className="space-y-2 mb-6">
                      {Array.from({
                  length: 4
                }).map((_, i) => <div key={i} className="h-3 bg-gray-200 rounded"></div>)}
                    </div>
                    <div className="h-10 bg-gray-200 rounded"></div>
                  </div>
                </div>) : pricingError ? <div className="col-span-3 text-center text-red-600">
                Errore nel caricamento dei prezzi. Riprova più tardi.
              </div> : pricingTiers?.map((tier, index) => {
            const isPopular = tier.tier_type === 'professional';
            return <PricingCard key={tier.id} tier={tier} isYearly={isYearly} isPopular={isPopular} delay={index * 0.1} />;
          })}
          </div>
        </div>
      </section>

      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <motion.h2 initial={{
            opacity: 0,
            y: 20
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.5
          }} className="text-3xl md:text-4xl font-bold text-gray-900">
              I Nostri <span className="text-primary-600">Valori</span>
            </motion.h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[{
            icon: <Clock className="h-8 w-8 text-primary-600" />,
            title: "Tempo",
            description: "Valorizziamo il tuo tempo perché sei speciale. Ogni minuto merita di essere vissuto al meglio, sia per chi offre servizi sia per chi li riceve.",
            delay: 0.1
          }, {
            icon: <Users className="h-8 w-8 text-primary-600" />,
            title: "Accessibilità",
            description: "Democratizziamo l'accesso a servizi di qualità per tutti abbattendo le barriere tecnologiche.",
            delay: 0.2
          }, {
            icon: <Heart className="h-8 w-8 text-primary-600" />,
            title: "Inclusività",
            description: "Crediamo che ogni persona debba poter accedere ai servizi di cui ha bisogno, indipendentemente dalle proprie caratteristiche o condizioni.",
            delay: 0.3
          }, {
            icon: <Heart className="h-8 w-8 text-primary-600" />,
            title: "Solidarietà",
            description: "Una percentuale dei nostri ricavi viene destinata a finanziare progetti di impatto sociale, perché crediamo che il successo debba generare valore per l'intera comunità.",
            delay: 0.3
          }, {
            icon: <Heart className="h-8 w-8 text-primary-600" />,
            title: "Trasparenza e fiducia",
            description: "Costruiamo un marketplace basato sulla chiarezza delle condizioni, l'affidabilità delle informazioni e la protezione della privacy di tutti gli utenti.",
            delay: 0.3
          }, {
            icon: <Heart className="h-8 w-8 text-primary-600" />,
            title: "Sostenibilità ambientale",
            description: "Crediamo che ogni persona debba poter accedere ai servizi di cui ha bisogno, indipendentemente dalle proprie caratteristiche o condizioni.",
            delay: 0.3
          }].map((value, index) => <motion.div key={index} initial={{
            opacity: 0,
            y: 20
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.5,
            delay: value.delay
          }} className="bg-white p-6 rounded-xl shadow-md border border-gray-100 hover:shadow-lg transition-shadow">
                <div className="bg-primary-50 p-3 rounded-full w-fit mb-4">
                  {value.icon}
                </div>
                <h3 className="text-xl font-semibold mb-3 text-gray-900">
                  {value.title}
                </h3>
                <p className="text-gray-600">{value.description}</p>
              </motion.div>)}
          </div>
        </div>
      </section>

      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <motion.h2 initial={{
            opacity: 0,
            y: 20
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.5
          }} className="text-3xl md:text-4xl font-bold text-gray-900">
              Come Attivare il Tuo{" "}
              <span className="text-primary-600">Team AI</span> in 3 Semplici
              Passi
            </motion.h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {[{
            title: "Scegli il Tuo Piano",
            description: "Seleziona il piano più adatto alle esigenze della tua attività, in base al numero di prenotazioni che desideri gestire.",
            icon: <Store className="h-6 w-6 text-primary-600" />,
            step: "1",
            delay: 0.1,
            image: "/images/steps/step1.jpg",
            alt: "Persona che seleziona un piano su un tablet"
          }, {
            title: "Configurazione Rapida",
            description: "Il nostro team configura il tuo assistente in soli 15 minuti, personalizzandolo per la tua attività e i tuoi servizi.",
            icon: <Bot className="h-6 w-6 text-primary-600" />,
            step: "2",
            delay: 0.2,
            image: "/images/steps/step2.jpg",
            alt: "Team che configura un sistema"
          }, {
            title: "Inizia a Ricevere Prenotazioni",
            description: "Il tuo team AI inizia subito a lavorare, rispondendo alle chiamate e gestendo le prenotazioni 24/7.",
            icon: <BarChart3 className="h-6 w-6 text-primary-600" />,
            step: "3",
            delay: 0.3,
            image: "/images/steps/step3.jpg",
            alt: "Dispositivo che mostra prenotazioni in arrivo"
          }].map((step, index) => <motion.div key={index} initial={{
            opacity: 0,
            y: 20
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.5,
            delay: step.delay
          }} className="bg-white p-6 rounded-xl border border-gray-100 hover:shadow-xl hover:border-primary-200 transition-all duration-300 text-center relative overflow-hidden group">
                <div className="absolute -top-4 -left-4 bg-primary-600 text-white w-8 h-8 rounded-full flex items-center justify-center font-bold text-lg z-10">
                  {step.step}
                </div>

                <div className="absolute top-2 left-2 bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full text-xs font-medium text-primary-700 z-10 shadow-sm">
                  Passo {step.step}: {step.title}
                </div>

                <div className="mb-6 rounded-lg overflow-hidden h-48 relative">
                  <img src={step.image} alt={step.alt} className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                </div>

                <div className="bg-primary-50 p-3 rounded-full w-16 h-16 flex items-center justify-center mx-auto -mt-10 mb-4 relative z-10 border-4 border-white shadow-md transition-all duration-300 group-hover:bg-primary-100 group-hover:scale-110">
                  {step.icon}
                </div>

                <h3 className="text-xl font-bold mb-2 text-gray-900 transition-all duration-300 group-hover:text-primary-600">
                  {step.title}
                </h3>
                <p className="text-gray-600 transition-all duration-300 group-hover:text-gray-700">{step.description}</p>
              </motion.div>)}
          </div>
        </div>
      </section>

      <section className="py-16 bg-primary-600 text-white">
        <div className="container mx-auto px-4 text-center max-w-3xl">
          <motion.div initial={{
          opacity: 0,
          y: 20
        }} whileInView={{
          opacity: 1,
          y: 0
        }} viewport={{
          once: true
        }} transition={{
          duration: 0.5
        }} className="bg-primary-700 p-8 rounded-2xl shadow-md border border-primary-500">
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
              <span className="text-primary-100">
                Prova Gratuita di 90 Giorni
              </span>
            </h2>
            <p className="text-primary-100 mb-6">
              Prova CatchUp gratuitamente per 90 giorni e ottieni ...... Nessun rischio, nessun
              impegno iniziale.
            </p>
            <Button size="lg" className="bg-white hover:bg-primary-50 text-primary-700">
              Inizia la Prova Gratuita
            </Button>
            <p className="text-sm text-primary-200 mt-3">
              Inizia subito - Configurazione in soli 15 minuti
            </p>
          </motion.div>
        </div>
      </section>

      <section id="faq" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <motion.h2 initial={{
            opacity: 0,
            y: 20
          }} whileInView={{
            opacity: 1,
            y: 0
          }} viewport={{
            once: true
          }} transition={{
            duration: 0.5
          }} className="text-3xl md:text-4xl font-bold text-gray-900">
              Domande <span className="text-primary-600">Frequenti</span>
            </motion.h2>
          </div>

          <div className="max-w-3xl mx-auto">
            <Accordion type="single" collapsible className="space-y-4">
              {[{
              question: "Come si configura l'Assistente CatchUp?",
              answer: "È semplicissimo: ti guidiamo passo-passo per inserire i tuoi servizi, orari e prezzi in circa 15 minuti. Non serve nessuna competenza tecnica e funziona con qualsiasi dispositivo."
            }, {
              question: "Come ricevo le prenotazioni confermate dall'assistente?",
              answer: "Puoi scegliere come ricevere notifiche: via email, SMS o direttamente nel tuo calendario esistente (Google Calendar, Outlook, ecc.). Inoltre, puoi visualizzare tutte le prenotazioni nella dashboard CatchUp."
            }, {
              question: "Posso personalizzare come l'assistente parla ai miei clienti?",
              answer: "Assolutamente sì! Puoi definire il tono di voce, le politiche di sconto, i messaggi di benvenuto e molto altro. L'assistente parlerà esattamente come vuoi tu, rappresentando al meglio il tuo brand."
            }, {
              question: "Funziona con il mio sistema di prenotazioni attuale?",
              answer: "Sì, CatchUp si integra con la maggior parte dei sistemi di prenotazione esistenti come Booking, TheFork, Mindbody, e molti altri. Durante la configurazione, ti aiuteremo a collegare il tuo sistema attuale."
            }, {
              question: "È necessario un dispositivo speciale per utilizzare CatchUp?",
              answer: "No, non serve nessun dispositivo. I tuoi clienti possono parlare con l'assistente direttamente dal loro telefono o computer, senza scaricare app. Per te basta un browser web per configurare e monitorare l'assistente."
            }].map((faq, index) => <motion.div key={index} initial={{
              opacity: 0,
              y: 20
            }} whileInView={{
              opacity: 1,
              y: 0
            }} viewport={{
              once: true
            }} transition={{
              duration: 0.5,
              delay: 0.1 * index
            }}>
                  <AccordionItem value={`item-${index}`} className="border border-gray-100 rounded-xl shadow-sm bg-white overflow-hidden">
                    <AccordionTrigger className="px-6 py-4 text-lg font-semibold text-gray-900 hover:no-underline hover:bg-gray-50">
                      {faq.question}
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-4 text-gray-600">
                      {faq.answer}
                    </AccordionContent>
                  </AccordionItem>
                </motion.div>)}
            </Accordion>
          </div>
        </div>
      </section>

      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Calendar className="h-6 w-6 text-primary-400" />
                <span className="text-xl font-semibold">CatchUp AI</span>
              </div>
              <p className="text-gray-400 mb-4">
                Team di assistenti AI che triplicano le prenotazioni della tua
                attività.
              </p>
              <div className="flex space-x-4">{/* Social media icons */}</div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Prodotto</h3>
              <ul className="space-y-2">
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Team AI
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Piani e Prezzi
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Integrazioni
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Casi di Successo
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Azienda</h3>
              <ul className="space-y-2">
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Chi Siamo
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    I Nostri Valori
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Blog
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Contatti
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Supporto</h3>
              <ul className="space-y-2">
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Centro Assistenza
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Documentazione
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Community
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Status
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © {new Date().getFullYear()} CatchUp AI. Tutti i diritti riservati.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">
                Termini di Servizio
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </footer>

      <ScrollToTop bgColor="bg-primary-600/90" hoverBgColor="hover:bg-primary-700" iconColor="text-white" borderColor="border border-primary-300/30" shadowClass="shadow-lg shadow-primary-500/20" />
    </div>;
};
export default Index;