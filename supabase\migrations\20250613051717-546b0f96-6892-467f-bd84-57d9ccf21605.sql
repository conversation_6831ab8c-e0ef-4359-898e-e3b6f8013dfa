
-- Create table for storing integration OAuth authorizations
CREATE TABLE public.integration_authorizations (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
  integration_id TEXT NOT NULL,
  provider TEXT NOT NULL,
  access_token TEXT NOT NULL,
  refresh_token TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  scope TEXT,
  authorized_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Ensure one active authorization per user/business/integration combination
  UNIQUE(user_id, business_id, integration_id, is_active) DEFERRABLE INITIALLY DEFERRED
);

-- Add Row Level Security (RLS)
ALTER TABLE public.integration_authorizations ENABLE ROW LEVEL SECURITY;

-- Create policies for RLS
CREATE POLICY "Users can view their own integration authorizations" 
  ON public.integration_authorizations 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own integration authorizations" 
  ON public.integration_authorizations 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own integration authorizations" 
  ON public.integration_authorizations 
  FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own integration authorizations" 
  ON public.integration_authorizations 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Create index for better performance
CREATE INDEX idx_integration_authorizations_user_business 
  ON public.integration_authorizations(user_id, business_id);

CREATE INDEX idx_integration_authorizations_integration 
  ON public.integration_authorizations(integration_id);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_integration_authorizations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_integration_authorizations_updated_at
  BEFORE UPDATE ON public.integration_authorizations
  FOR EACH ROW
  EXECUTE FUNCTION update_integration_authorizations_updated_at();
