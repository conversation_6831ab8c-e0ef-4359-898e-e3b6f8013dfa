import { BusinessGenerator } from './generators/businessGenerator';
import { DealGenerator } from './generators/dealGenerator';
import { UserGenerator } from './generators/userGenerator';
import { BookingGenerator } from './generators/bookingGenerator';
import { DataValidationUtils } from './utils/validation';
import { DataQualityUtils } from './utils/dataQuality';
import { BusinessClusteringUtils } from './utils/clustering';
import { 
  GenerationOptions, 
  FakeDataResult, 
  BusinessCategory 
} from './types';
import { supabase } from '@/integrations/supabase/client';

/**
 * Centralized manager for all fake data generation operations
 * This class orchestrates the generation of realistic synthetic data
 * following Italian business patterns and customer behavior.
 */
export class FakeDataManager {
  private businessGenerator: BusinessGenerator;
  private dealGenerator: DealGenerator;
  private userGenerator: UserGenerator;
  private bookingGenerator: BookingGenerator;

  constructor() {
    this.businessGenerator = new BusinessGenerator();
    this.dealGenerator = new DealGenerator();
    this.userGenerator = new UserGenerator();
    this.bookingGenerator = new BookingGenerator();
  }

  // Business Generation
  async generateBusinesses(
    categoryId: string,
    cityName: string,
    count: number,
    options: GenerationOptions,
    onProgress?: (current: number, total: number, message: string) => void
  ): Promise<FakeDataResult> {
    // Validate inputs
    const countValidation = DataValidationUtils.validateGenerationCounts({ businesses: count });
    if (!countValidation.isValid) {
      return {
        created: 0,
        errors: countValidation.errors,
        warnings: []
      };
    }

    const optionsValidation = DataValidationUtils.validateGenerationOptions(options);
    if (!optionsValidation.isValid) {
      return {
        created: 0,
        errors: optionsValidation.errors,
        warnings: []
      };
    }

    try {
      onProgress?.(0, count, 'Inizializzazione generazione business...');
      
      const result = await this.businessGenerator.generateMultipleBusinesses(
        categoryId,
        cityName,
        count,
        (current, total) => onProgress?.(current, total, `Creazione business ${current}/${total}`)
      );

      // Apply data quality improvements if requested
      if (options.useRealisticClustering && result.created > 0) {
        result.warnings.push('Applicato clustering geografico realistico');
      }

      return result;

    } catch (error) {
      return {
        created: 0,
        errors: [`Errore generale: ${error}`],
        warnings: []
      };
    }
  }

  // Deal Generation
  async generateDeals(
    businessIds: string[],
    dateRange: { start: Date; end: Date },
    dealsPerBusiness: number = 1,
    options: GenerationOptions,
    onProgress?: (current: number, total: number, message: string) => void
  ): Promise<FakeDataResult> {
    // Validate inputs
    const dateValidation = DataValidationUtils.validateDateRange(dateRange.start, dateRange.end);
    if (!dateValidation.isValid) {
      return {
        created: 0,
        errors: dateValidation.errors,
        warnings: []
      };
    }

    const countValidation = DataValidationUtils.validateGenerationCounts({ 
      deals: businessIds.length * dealsPerBusiness 
    });
    if (!countValidation.isValid) {
      return {
        created: 0,
        errors: countValidation.errors,
        warnings: []
      };
    }

    try {
      onProgress?.(0, businessIds.length, 'Inizializzazione generazione deals...');

      const result = await this.dealGenerator.generateMultipleDeals(
        businessIds,
        dateRange,
        dealsPerBusiness,
        onProgress
      );

      // Add seasonal information if enabled
      if (options.useSeasonality && result.created > 0) {
        result.warnings.push('Applicati pattern stagionali realistici');
      }

      return result;

    } catch (error) {
      return {
        created: 0,
        errors: [`Errore generale: ${error}`],
        warnings: []
      };
    }
  }

  // User Generation
  async generateUsers(
    count: number,
    startingIndex: number = 1,
    options: GenerationOptions,
    onProgress?: (current: number, total: number, message: string) => void
  ): Promise<FakeDataResult> {
    // Validate inputs
    const countValidation = DataValidationUtils.validateGenerationCounts({ users: count });
    if (!countValidation.isValid) {
      return {
        created: 0,
        errors: countValidation.errors,
        warnings: []
      };
    }

    try {
      onProgress?.(0, count, 'Inizializzazione generazione utenti...');

      const result = await this.userGenerator.generateMultipleUsers(
        count,
        startingIndex,
        onProgress
      );

      // Add customer behavior modeling if enabled
      if (options.useCustomerBehavior && result.created > 0) {
        result.warnings.push('Applicati modelli comportamentali realistici');
      }

      return result;

    } catch (error) {
      return {
        created: 0,
        errors: [`Errore generale: ${error}`],
        warnings: []
      };
    }
  }

  // Booking Generation
  async generateBookings(
    dateRange: { start: Date; end: Date },
    count: number,
    filters: {
      cityFilter?: string;
      categoryFilter?: string;
      businessIds?: string[];
    },
    options: GenerationOptions,
    onProgress?: (current: number, total: number, message: string) => void
  ): Promise<FakeDataResult> {
    // Validate inputs
    const dateValidation = DataValidationUtils.validateDateRange(dateRange.start, dateRange.end);
    if (!dateValidation.isValid) {
      return {
        created: 0,
        errors: dateValidation.errors,
        warnings: []
      };
    }

    const countValidation = DataValidationUtils.validateGenerationCounts({ bookings: count });
    if (!countValidation.isValid) {
      return {
        created: 0,
        errors: countValidation.errors,
        warnings: []
      };
    }

    try {
      onProgress?.(0, count, 'Inizializzazione generazione prenotazioni...');

      const result = await this.bookingGenerator.generateMultipleBookings(
        dateRange,
        count,
        filters.cityFilter,
        filters.categoryFilter,
        filters.businessIds,
        onProgress
      );

      // Add customer behavior information if enabled
      if (options.useCustomerBehavior && result.created > 0) {
        result.warnings.push('Applicati pattern di prenotazione realistici');
      }

      return result;

    } catch (error) {
      return {
        created: 0,
        errors: [`Errore generale: ${error}`],
        warnings: []
      };
    }
  }

  // Data Analysis and Quality
  async analyzeDataQuality(): Promise<{
    businessStats: any;
    dealStats: any;
    bookingStats: any;
    overallHealth: 'good' | 'fair' | 'poor';
    recommendations: string[];
  }> {
    try {
      // Fetch sample data for analysis
      const [businesses, deals, bookings] = await Promise.all([
        supabase.from('businesses').select('*').eq('fake', true).limit(100),
        supabase.from('deals').select('*').eq('fake', true).limit(100),
        supabase.from('bookings').select('*').eq('fake', true).limit(100)
      ]);

      const businessStats = DataQualityUtils.calculateDataStats(businesses.data || []);
      const dealStats = DataQualityUtils.calculateDataStats(deals.data || []);
      const bookingStats = DataQualityUtils.calculateDataStats(bookings.data || []);

      // Analyze data relationships
      if (businesses.data && deals.data && bookings.data) {
        DataQualityUtils.createDataRelationships(
          businesses.data,
          deals.data,
          bookings.data
        );
      }

      // Determine overall health
      const totalRecords = (businesses.data?.length || 0) + (deals.data?.length || 0) + (bookings.data?.length || 0);
      let overallHealth: 'good' | 'fair' | 'poor' = 'good';

      if (totalRecords < 50) {
        overallHealth = 'poor';
      } else if (totalRecords < 200) {
        overallHealth = 'fair';
      }

      // Generate recommendations
      const recommendations: string[] = [];
      
      if ((businesses.data?.length || 0) < 20) {
        recommendations.push('Genera più attività per una simulazione più realistica');
      }
      
      if ((deals.data?.length || 0) < (businesses.data?.length || 0) * 2) {
        recommendations.push('Considera di generare più offerte per attività');
      }
      
      if ((bookings.data?.length || 0) < (deals.data?.length || 0) * 3) {
        recommendations.push('Aumenta il numero di prenotazioni per una migliore simulazione');
      }

      return {
        businessStats,
        dealStats,
        bookingStats,
        overallHealth,
        recommendations
      };

    } catch (error) {
      return {
        businessStats: {},
        dealStats: {},
        bookingStats: {},
        overallHealth: 'poor',
        recommendations: ['Errore nell\'analisi dei dati: ' + error]
      };
    }
  }

  // Cleanup Operations
  async deleteAllFakeData(): Promise<{
    businesses: FakeDataResult;
    deals: FakeDataResult;
    bookings: FakeDataResult;
    users: FakeDataResult;
  }> {
    try {
      // Delete in correct order (bookings -> deals -> businesses -> users)
      const [bookingsResult, dealsResult, businessesResult, usersResult] = await Promise.all([
        this.bookingGenerator.deleteAllFakeBookings(),
        this.deleteAllFakeDeals(),
        this.deleteAllFakeBusinesses(),
        this.userGenerator.deleteAllFakeUsers()
      ]);

      return {
        bookings: bookingsResult,
        deals: dealsResult,
        businesses: businessesResult,
        users: usersResult
      };

    } catch (error) {
      const errorResult: FakeDataResult = {
        created: 0,
        errors: [`Errore generale: ${error}`],
        warnings: []
      };

      return {
        businesses: errorResult,
        deals: errorResult,
        bookings: errorResult,
        users: errorResult
      };
    }
  }

  private async deleteAllFakeDeals(): Promise<FakeDataResult> {
    try {
      const { data: deletedDeals, error } = await supabase
        .from('deals')
        .delete()
        .eq('fake', true)
        .select('id');

      if (error) {
        return {
          created: 0,
          errors: [`Errore eliminazione deals: ${error.message}`],
          warnings: []
        };
      }

      return {
        created: deletedDeals?.length || 0,
        errors: [],
        warnings: [`Eliminati ${deletedDeals?.length || 0} deals finti`]
      };

    } catch (error) {
      return {
        created: 0,
        errors: [`Errore generale: ${error}`],
        warnings: []
      };
    }
  }

  private async deleteAllFakeBusinesses(): Promise<FakeDataResult> {
    try {
      const { data: deletedBusinesses, error } = await supabase
        .from('businesses')
        .delete()
        .eq('fake', true)
        .select('id');

      if (error) {
        return {
          created: 0,
          errors: [`Errore eliminazione business: ${error.message}`],
          warnings: []
        };
      }

      return {
        created: deletedBusinesses?.length || 0,
        errors: [],
        warnings: [`Eliminati ${deletedBusinesses?.length || 0} business finti`]
      };

    } catch (error) {
      return {
        created: 0,
        errors: [`Errore generale: ${error}`],
        warnings: []
      };
    }
  }

  // Business clustering analysis
  async analyzeBusinessClustering(cityName: string): Promise<{
    clusters: Array<{ center: { lat: number; lng: number }; businesses: number; diversity: number }>;
    isolatedBusinesses: number;
    averageDistanceToNearest: number;
    recommendations: string[];
  }> {
    try {
      const { data: businesses } = await supabase
        .from('businesses')
        .select('latitude, longitude, category_id')
        .eq('fake', true)
        .eq('city', cityName);

      if (!businesses || businesses.length === 0) {
        return {
          clusters: [],
          isolatedBusinesses: 0,
          averageDistanceToNearest: 0,
          recommendations: ['Nessun business trovato per l\'analisi']
        };
      }

      const businessData = businesses
        .filter(b => b.latitude && b.longitude)
        .map(b => ({
          lat: b.latitude!,
          lng: b.longitude!,
          category: b.category_id || 'unknown'
        }));

      const analysis = BusinessClusteringUtils.analyzeClusteringPatterns(businessData);
      
      const recommendations: string[] = [];
      
      if (analysis.clusters.length === 0) {
        recommendations.push('Considera di creare aree commerciali più concentrate');
      } else if (analysis.clusters.length > businessData.length * 0.3) {
        recommendations.push('Troppi cluster piccoli, considera di consolidare');
      }
      
      if (analysis.isolatedBusinesses > businessData.length * 0.3) {
        recommendations.push('Molte attività isolate, potrebbe non essere realistico');
      }

      return {
        ...analysis,
        recommendations
      };

    } catch (error) {
      return {
        clusters: [],
        isolatedBusinesses: 0,
        averageDistanceToNearest: 0,
        recommendations: [`Errore nell'analisi: ${error}`]
      };
    }
  }
}

// Export singleton instance
export const fakeDataManager = new FakeDataManager();