import { useState, useEffect } from "react";
import { toast } from "sonner";
import { 
  Instagram, 
  Facebook, 
  Twitter, 
  Calendar, 
  Mail, 
  MessageSquare, 
  CreditCard, 
  BarChart3,
  Settings,
  Zap,
  Users,
  Video,
  Cloud,
  CalendarDays,
  Github
} from "lucide-react";

import MainLayout from "@/layouts/MainLayout";
import { useBusinessStore } from "@/store/businessStore";
import { IntegrationCard } from "@/components/integrations/IntegrationCard";
import { IntegrationFilters } from "@/components/integrations/IntegrationFilters";
import { IntegrationStats } from "@/components/integrations/IntegrationStats";
import { IntegrationCustomRequest } from "@/components/integrations/IntegrationCustomRequest";
import { IntegrationsEmptyState } from "@/components/integrations/IntegrationsEmptyState";
import { GitHubIntegrationCard } from "@/components/github/GitHubIntegrationCard";
import { useIntegrationAuth } from "@/hooks/useIntegrationAuth";

interface Integration {
  id: string;
  name: string;
  description: string;
  icon: React.ElementType;
  category: string;
  status: 'connected' | 'available' | 'coming-soon';
  enabled: boolean;
  premium?: boolean;
  features: string[];
  setup?: {
    apiKey?: string;
    webhook?: string;
    settings?: Record<string, any>;
  };
}

const Integrations = () => {
  const { selectedBusiness } = useBusinessStore();
  const { authorizeGoogleCalendar, revokeAuthorization, getAuthorization, loading } = useIntegrationAuth();
  
  const [integrations, setIntegrations] = useState<Integration[]>([
    // Social Media
    {
      id: 'instagram',
      name: 'Instagram',
      description: 'Connetti il tuo account Instagram per gestire i messaggi e promuovere i tuoi servizi',
      icon: Instagram,
      category: 'social',
      status: 'available',
      enabled: false,
      features: ['Messaggi diretti', 'Promozione automatica', 'Analytics dei post'],
    },
    {
      id: 'facebook',
      name: 'Facebook',
      description: 'Integra Facebook Messenger per gestire prenotazioni tramite chat',
      icon: Facebook,
      category: 'social',
      status: 'available',
      enabled: false,
      features: ['Messenger Bot', 'Prenotazioni da Facebook', 'Pubblicità automatica'],
    },
    {
      id: 'twitter',
      name: 'X (Twitter)',
      description: 'Monitora menzioni e gestisci customer service su X',
      icon: Twitter,
      category: 'social',
      status: 'coming-soon',
      enabled: false,
      features: ['Monitoraggio menzioni', 'Risposta automatica', 'Analisi sentiment'],
    },
    {
      id: 'tiktok',
      name: 'TikTok',
      description: 'Promuovi i tuoi servizi e gestisci lead da TikTok',
      icon: Video,
      category: 'social',
      status: 'coming-soon',
      enabled: false,
      premium: true,
      features: ['Lead generation', 'Pubblicità video', 'Analytics avanzate'],
    },

    // Google Services
    {
      id: 'google-calendar',
      name: 'Google Calendar',
      description: 'Sincronizza automaticamente le prenotazioni con Google Calendar',
      icon: Calendar,
      category: 'productivity',
      status: 'available',
      enabled: false,
      features: ['Sincronizzazione bidirezionale', 'Promemoria automatici', 'Condivisione calendario'],
    },
    {
      id: 'gmail',
      name: 'Gmail',
      description: 'Integra Gmail per email automatiche e gestione comunicazioni',
      icon: Mail,
      category: 'productivity',
      status: 'available',
      enabled: false,
      features: ['Email template', 'Invio automatico', 'Tracking aperture'],
    },
    {
      id: 'weather',
      name: 'Meteo',
      description: 'Integra dati meteorologici per ottimizzare prenotazioni e gestire cancellazioni automatiche',
      icon: Cloud,
      category: 'productivity',
      status: 'available',
      enabled: false,
      features: ['Previsioni meteo', 'Allerte automatiche', 'Riprogrammazione intelligente', 'Notifiche clienti'],
    },
    {
      id: 'events',
      name: 'Eventi',
      description: 'Sincronizza con piattaforme eventi per gestire prenotazioni per eventi speciali',
      icon: CalendarDays,
      category: 'productivity',
      status: 'available',
      enabled: false,
      features: ['Sync Eventbrite', 'Facebook Events', 'Gestione eventi ricorrenti', 'Biglietteria integrata'],
    },

    // Communication
    {
      id: 'whatsapp',
      name: 'WhatsApp Business',
      description: 'Gestisci prenotazioni e comunicazioni tramite WhatsApp',
      icon: MessageSquare,
      category: 'communication',
      status: 'available',
      enabled: false,
      features: ['Messaggi automatici', 'Template approvati', 'Notifiche prenotazione'],
    },
    {
      id: 'telegram',
      name: 'Telegram',
      description: 'Bot Telegram per prenotazioni e customer service',
      icon: MessageSquare,
      category: 'communication',
      status: 'available',
      enabled: false,
      features: ['Bot prenotazioni', 'Notifiche istantanee', 'Gruppi business'],
    },

    {
      id:'github',
      name:'Github',
      description:'Integra Github per gestire i tuoi repository e progetti',
      icon: Github,
      category:'productivity',
      status:'available',
      enabled:false,  
      features:['Repository', 'Progetti', 'Pull Request', 'Issues'],
    },
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  // Check authorization status on load and business change
  useEffect(() => {
    if (selectedBusiness?.id) {
      checkIntegrationStatuses();
    }
  }, [selectedBusiness?.id]);

  const checkIntegrationStatuses = async () => {
    if (!selectedBusiness?.id) return;

    const updatedIntegrations = await Promise.all(
      integrations.map(async (integration) => {
        const auth = await getAuthorization(integration.id, selectedBusiness.id);
        return {
          ...integration,
          status: auth ? 'connected' as const : integration.id === 'google-calendar' ? 'available' as const : integration.status,
          enabled: !!auth
        };
      })
    );

    setIntegrations(updatedIntegrations);
  };

  const categories = [
    { id: 'all', name: 'Tutte', icon: Settings },
    { id: 'social', name: 'Social Media', icon: Instagram },
    { id: 'productivity', name: 'Produttività', icon: Calendar },
    { id: 'communication', name: 'Comunicazione', icon: MessageSquare },
    { id: 'crm', name: 'CRM & Vendite', icon: Users },
    { id: 'payments', name: 'Pagamenti', icon: CreditCard },
    { id: 'analytics', name: 'Analytics', icon: BarChart3 },
    { id: 'automation', name: 'Automazione', icon: Zap },
  ];

  const filteredIntegrations = integrations.filter(integration => {
    const matchesSearch = integration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         integration.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || integration.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleAuthorize = async (integrationId: string) => {
    if (!selectedBusiness?.id) {
      toast.error("Seleziona un'attività per autorizzare l'integrazione");
      return;
    }

    if (integrationId === 'google-calendar') {
      const success = await authorizeGoogleCalendar(selectedBusiness.id);
      if (success) {
        await checkIntegrationStatuses();
      }
    } else {
      toast.info("Autorizzazione non ancora implementata per questa integrazione");
    }
  };

  const handleRevoke = async (integrationId: string) => {
    if (!selectedBusiness?.id) return;

    const success = await revokeAuthorization(integrationId, selectedBusiness.id);
    if (success) {
      await checkIntegrationStatuses();
    }
  };

  const toggleIntegration = async (integrationId: string) => {
    const integration = integrations.find(i => i.id === integrationId);
    if (!integration) return;
    
    if (integration.status === 'coming-soon') {
      toast.error("Questa integrazione sarà disponibile prossimamente");
      return;
    }

    // If not enabled and not connected, trigger authorization
    if (!integration.enabled && integration.status === 'available') {
      await handleAuthorize(integrationId);
      return;
    }

    // If enabled and connected, just toggle the enabled state
    try {
      const newEnabled = !integration.enabled;
      
      setIntegrations(prev => 
        prev.map(i => 
          i.id === integrationId 
            ? { ...i, enabled: newEnabled }
            : i
        )
      );

      toast.success(
        newEnabled 
          ? `${integration.name} è stata attivata con successo`
          : `${integration.name} è stata disattivata`
      );
    } catch (error) {
      console.error('Errore nel toggle dell\'integrazione:', error);
      toast.error("Si è verificato un errore durante l'operazione");
    }
  };

  const connectedCount = integrations.filter(i => i.status === 'connected').length;
  const availableCount = integrations.filter(i => i.status === 'available').length;

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="flex flex-col space-y-6">
          <div className="flex flex-col md:flex-row md:justify-between md:items-center space-y-4 md:space-y-0">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Integrazioni</h1>
              <p className="text-gray-600 mt-1">
                Connetti CatchUp con i tuoi strumenti preferiti per automatizzare il tuo business
              </p>
            </div>
            
            <IntegrationStats 
              connectedCount={connectedCount} 
              availableCount={availableCount} 
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-1">
              <IntegrationFilters
                searchTerm={searchTerm}
                setSearchTerm={setSearchTerm}
                selectedCategory={selectedCategory}
                setSelectedCategory={setSelectedCategory}
                categories={categories}
                integrations={integrations}
              />
            </div>

            <div className="md:col-span-2">
              {!selectedBusiness ? (
                <IntegrationsEmptyState type="no-business" />
              ) : filteredIntegrations.length === 0 ? (
                <IntegrationsEmptyState type="no-results" />
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {filteredIntegrations.map(integration => {
                    // Special handling for GitHub integration
                    if (integration.id === 'github') {
                      return (
                        <GitHubIntegrationCard
                          key="github"
                          businessId={selectedBusiness.id}
                          onIntegrationChange={() => {
                            // Refresh integration status if needed
                            checkIntegrationStatuses();
                          }}
                        />
                      );
                    }
                    
                    return (
                      <IntegrationCard
                        key={integration.id}
                        integration={integration}
                        onToggle={toggleIntegration}
                        onAuthorize={handleAuthorize}
                        onRevoke={handleRevoke}
                      />
                    );
                  })}
                </div>
              )}
            </div>
          </div>

          {selectedBusiness && <IntegrationCustomRequest />}
        </div>
      </div>
    </MainLayout>
  );
};

export default Integrations;
