import { faker } from '@faker-js/faker';
import { UserPersona, FakeDataResult, BusinessCategory } from '../types';
import { 
  italianCustomerData, 
  ageGroupBehavior, 
  emailProviders,
  phonePreferences
} from '../data/customers';
import { supabase } from '@/integrations/supabase/client';

// Set faker locale to Italian - Remove deprecated locale setting
// faker.locale = 'it'; // Not needed for new versions

export class UserGenerator {
  
  private generateUserPersona(): { persona: UserPersona; ageGroup: keyof typeof ageGroupBehavior } {
    // Select age group based on weights
    const rand = Math.random();
    let ageGroup: keyof typeof ageGroupBehavior;
    
    if (rand < ageGroupBehavior.young.weight) {
      ageGroup = 'young';
    } else if (rand < ageGroupBehavior.young.weight + ageGroupBehavior.adult.weight) {
      ageGroup = 'adult';
    } else {
      ageGroup = 'senior';
    }

    const behavior = ageGroupBehavior[ageGroup];
    
    return {
      ageGroup,
      persona: {
        ageGroup,
        bookingFrequency: behavior.bookingFrequency,
        preferredCategories: [...behavior.preferredCategories] as BusinessCategory[],
        locationPreference: Math.random() < 0.7 ? 'local' : 'citywide'
      }
    };
  }

  private generateItalianName(ageGroup: keyof typeof ageGroupBehavior): { firstName: string; lastName: string; gender: 'male' | 'female' } {
    const gender = Math.random() > 0.5 ? 'male' : 'female';
    
    let firstNames: string[];
    
    // Age-appropriate name selection
    if (ageGroup === 'young') {
      // Younger people more likely to have modern names
      firstNames = gender === 'male' 
        ? [...italianCustomerData.firstNames.male].slice(10) // More modern names
        : [...italianCustomerData.firstNames.female].slice(10);
    } else if (ageGroup === 'senior') {
      // Older people more likely to have traditional names  
      firstNames = gender === 'male'
        ? [...italianCustomerData.firstNames.male].slice(0, 15) // Traditional names
        : [...italianCustomerData.firstNames.female].slice(0, 15);
    } else {
      // Adults get full selection
      firstNames = gender === 'male' 
        ? italianCustomerData.firstNames.male
        : italianCustomerData.firstNames.female;
    }

    const firstName = faker.helpers.arrayElement(firstNames);
    const lastName = faker.helpers.arrayElement(italianCustomerData.lastNames);

    return { firstName, lastName, gender };
  }

  private generateEmail(firstName: string, lastName: string, ageGroup: keyof typeof ageGroupBehavior): string {
    const providers = emailProviders[ageGroup];
    const provider = faker.helpers.arrayElement(providers);
    
    // Generate email format based on age group preferences
    const formats = [
      `${firstName.toLowerCase()}.${lastName.toLowerCase()}`,
      `${firstName.toLowerCase()}${lastName.toLowerCase()}`,
      `${firstName.toLowerCase()}${faker.datatype.number({ min: 1, max: 99 })}`,
      `${firstName.toLowerCase()}_${lastName.toLowerCase()}`
    ];

    // Younger users more likely to have creative formats
    let format: string;
    if (ageGroup === 'young') {
      format = faker.helpers.arrayElement(formats);
    } else {
      format = formats[0]; // Older users prefer formal firstname.lastname
    }

    return `${format}@${provider}`;
  }

  private generatePhone(ageGroup: keyof typeof ageGroupBehavior): string {
    const preferences = phonePreferences[ageGroup];
    const isMobile = Math.random() < preferences.mobile;
    
    if (isMobile) {
      // Italian mobile numbers
      const prefixes = ['320', '321', '322', '323', '324', '325', '326', '327', '328', '329',
                       '330', '331', '333', '334', '335', '336', '337', '338', '339',
                       '340', '341', '342', '343', '345', '346', '347', '348', '349'];
      const prefix = faker.helpers.arrayElement(prefixes);
      const number = faker.datatype.number({ min: 1000000, max: 9999999 });
      return `+39 ${prefix} ${number}`;
    } else {
      // Italian landline numbers (city codes)
      const cityCodes = ['02', '06', '011', '051', '055', '081', '010', '041'];
      const cityCode = faker.helpers.arrayElement(cityCodes);
      const number = faker.datatype.number({ min: 1000000, max: 99999999 });
      return `+39 ${cityCode} ${number}`;
    }
  }

  private calculateBirthDate(ageGroup: keyof typeof ageGroupBehavior): Date {
    const currentYear = new Date().getFullYear();
    let minAge: number, maxAge: number;

    switch (ageGroup) {
      case 'young':
        minAge = 18;
        maxAge = 35;
        break;
      case 'adult':
        minAge = 36;
        maxAge = 55;
        break;
      case 'senior':
        minAge = 56;
        maxAge = 80;
        break;
    }

    const age = faker.datatype.number({ min: minAge, max: maxAge });
    const birthYear = currentYear - age;
    
    return faker.date.between(
      new Date(birthYear, 0, 1),
      new Date(birthYear, 11, 31)
    );
  }

  public async generateUser(): Promise<any> {
    const { persona, ageGroup } = this.generateUserPersona();
    const { firstName, lastName, gender } = this.generateItalianName(ageGroup);
    const email = this.generateEmail(firstName, lastName, ageGroup);
    const phone = this.generatePhone(ageGroup);
    const birthDate = this.calculateBirthDate(ageGroup);

    // Generate realistic user metadata
    const metadata = {
      first_name: firstName,
      last_name: lastName,
      full_name: `${firstName} ${lastName}`,
      phone,
      birth_date: birthDate.toISOString().split('T')[0],
      gender,
      age_group: ageGroup,
      persona: persona,
      fake: true,
      created_at: new Date().toISOString()
    };

    // Create auth user
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email,
      password: 'TempPassword123!', // Temporary password for fake users
      email_confirm: true,
      user_metadata: metadata
    });

    if (authError) {
      throw new Error(`Failed to create auth user: ${authError.message}`);
    }

    return {
      id: authUser.user.id,
      email,
      metadata,
      persona
    };
  }

  public async generateMultipleUsers(
    count: number,
    startingIndex: number = 1,
    onProgress?: (current: number, total: number, message: string) => void
  ): Promise<FakeDataResult> {
    const result: FakeDataResult = {
      created: 0,
      errors: [],
      warnings: []
    };

    for (let i = 0; i < count; i++) {
      try {
        onProgress?.(i + 1, count, `Creazione utente ${startingIndex + i}`);
        
        const user = await this.generateUser();
        result.created++;
        
        // Add delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 200));
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        result.errors.push(`User ${startingIndex + i}: ${errorMessage}`);
        
        // If it's a rate limit error, wait longer
        if (errorMessage.includes('rate') || errorMessage.includes('limit')) {
          result.warnings.push('Rate limit encountered, slowing down...');
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }

    return result;
  }

  public async deleteAllFakeUsers(): Promise<FakeDataResult> {
    const result: FakeDataResult = {
      created: 0,
      errors: [],
      warnings: []
    };

    try {
      // Get all users with fake metadata
      const { data: users, error: fetchError } = await supabase.auth.admin.listUsers();
      
      if (fetchError) {
        result.errors.push(`Failed to fetch users: ${fetchError.message}`);
        return result;
      }

      const fakeUsers = users?.users?.filter((user: any) => 
        user.user_metadata?.fake === true
      ) || [];

      result.warnings.push(`Found ${fakeUsers.length} fake users to delete`);

      for (const user of fakeUsers) {
        try {
          const { error: deleteError } = await supabase.auth.admin.deleteUser(user.id);
          
          if (deleteError) {
            result.errors.push(`Failed to delete user ${user.email}: ${deleteError.message}`);
          } else {
            result.created++; // Using created as deleted count
          }
          
          // Rate limiting
          await new Promise(resolve => setTimeout(resolve, 100));
          
        } catch (error) {
          result.errors.push(`Error deleting user ${user.email}: ${error}`);
        }
      }

    } catch (error) {
      result.errors.push(`General error: ${error}`);
    }

    return result;
  }
}