import React from 'react';
import Widget from './Widget';
import { DealsWidget as DealsWidgetType } from '@/types/widget';
import { Package, CheckCircle2, XCircle } from 'lucide-react';
import { Link } from 'react-router-dom';

interface DealsWidgetProps {
  widget: DealsWidgetType;
  className?: string;
  onRemove?: () => void;
}

const DealsWidget: React.FC<DealsWidgetProps> = React.memo(({ widget, className, onRemove }) => {
  return (
    <Widget
      widget={widget}
      className={className}
      actionLabel="Gestisci Offerte"
      actionLink="/deals"
      onRemove={onRemove}
    >
      <div className="w-full py-2">
        <div className="flex flex-col items-center justify-center text-center mb-4">
          <span className="text-4xl font-bold text-blue-600">
            {widget.isLoading ? "-" : widget.data.dealsCount}
          </span>
          <span className="text-sm text-gray-500 mt-1">
            {widget.data.dealsCount === 1 ? "offerta" : "offerte"} totali
          </span>
        </div>

        <div className="grid grid-cols-2 gap-2 mt-2">
          <div className="flex flex-col items-center p-2 rounded-md bg-green-50">
            <div className="flex items-center mb-1">
              <CheckCircle2 className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-sm font-medium text-green-700">Attive</span>
            </div>
            <span className="text-xl font-bold text-green-700">{widget.data.activeDealsCount}</span>
          </div>

          <div className="flex flex-col items-center p-2 rounded-md bg-red-50">
            <div className="flex items-center mb-1">
              <XCircle className="h-4 w-4 text-red-500 mr-1" />
              <span className="text-sm font-medium text-red-700">Scadute</span>
            </div>
            <span className="text-xl font-bold text-red-700">{widget.data.expiredDealsCount}</span>
          </div>
        </div>
      </div>
    </Widget>
  );
});

export default DealsWidget;
