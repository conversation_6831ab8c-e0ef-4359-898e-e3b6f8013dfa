#!/usr/bin/env node

import * as fs from 'fs';

// Type definitions for the leads table
interface Lead {
  Name: string | null;
  Email: string | null;
  Phone: string | null;
  Address: string | null;
  Category: string | null;
  source: string | null;
  Website: string | null;
  Rating: string | null;
  Review: string | null;
  Latitude: string | null;
  Longitude: string | null;
  Operating_Hours: string | null;
  image_url: string | null;
  mapsUrl: string | null;
  loaded: boolean;
  // Social media fields
  facebooks: string | null;
  instagrams: string | null;
  twitters: string | null;
  linkedIns: string | null;
  youtubes: string | null;
  tiktoks: string | null;
  pinterests: string | null;
  discords: string | null;
}

// Interface for the Treatwell data structure
interface TreatwellEntry {
  searchUrl?: string;
  basicInfo?: {
    id?: number;
    name?: string;
    description?: string;
    venueUrl?: string;
  };
  location?: {
    address?: string;
    coordinates?: {
      lat?: number;
      lon?: number;
    };
  };
  rating?: {
    average?: number;
    count?: number;
    dimensions?: Array<{
      name?: string;
      average?: number;
      count?: number;
    }>;
  };
  openingHours?: Array<{
    dayOfWeek?: string;
    from?: string;
    to?: string;
    open?: boolean;
  }>;
  contact?: {
    phone?: string;
    email?: string;
    website?: string;
  };
  images?: string[];
  services?: any[];
  category?: string;
}

class LeadsExtractor {
  private readonly inputFile: string;
  private readonly outputFile: string;
  private extractedLeads: Lead[] = [];
  private duplicateChecker = new Set<string>();

  constructor(inputFile: string, options: { outputFile?: string } = {}) {
    this.inputFile = inputFile;
    this.outputFile = options.outputFile || 'extracted-leads.json';
  }

  private generateLeadKey(entry: TreatwellEntry): string {
    const name = entry.basicInfo?.name?.toLowerCase().trim() || '';
    const address = entry.location?.address?.toLowerCase().trim() || '';
    return `${name}|${address}`;
  }

  private extractCategoryFromName(name: string): string {
    const nameLower = name.toLowerCase();
    
    if (nameLower.includes('barber') || nameLower.includes('sala da barba')) {
      return 'barbershop';
    } else if (nameLower.includes('parrucchie') || nameLower.includes('hair')) {
      return 'hair_salon';
    } else if (nameLower.includes('estetica') || nameLower.includes('beauty')) {
      return 'beauty_center';
    } else if (nameLower.includes('nail') || nameLower.includes('unghie')) {
      return 'nail_salon';
    } else if (nameLower.includes('spa') || nameLower.includes('wellness')) {
      return 'spa';
    } else if (nameLower.includes('massage') || nameLower.includes('massaggio')) {
      return 'massage';
    } else if (nameLower.includes('clinica') || nameLower.includes('clinic')) {
      return 'medical_aesthetics';
    }
    
    return 'beauty_wellness';
  }

  private mapTreatwellToLead(entry: TreatwellEntry): Lead | null {
    // Skip if missing essential data
    if (!entry.basicInfo?.name && !entry.location?.address) {
      return null;
    }

    // Check for duplicates
    const key = this.generateLeadKey(entry);
    if (this.duplicateChecker.has(key)) {
      return null;
    }
    this.duplicateChecker.add(key);

    const category = entry.category || 
                    (entry.basicInfo?.name ? this.extractCategoryFromName(entry.basicInfo.name) : 'beauty_wellness');

    return {
      Name: entry.basicInfo?.name || null,
      Email: entry.contact?.email || null,
      Phone: entry.contact?.phone || null,
      Address: entry.location?.address || null,
      Category: category,
      source: 'treatwell-scraper',
      Website: entry.basicInfo?.venueUrl || entry.contact?.website || null,
      Rating: entry.rating?.average ? String(entry.rating.average) : null,
      Review: entry.rating?.count ? String(entry.rating.count) : null,
      Latitude: entry.location?.coordinates?.lat ? String(entry.location.coordinates.lat) : null,
      Longitude: entry.location?.coordinates?.lon ? String(entry.location.coordinates.lon) : null,
      Operating_Hours: entry.openingHours 
        ? JSON.stringify(entry.openingHours)
        : null,
      image_url: entry.images && entry.images.length > 0 ? entry.images[0] : null,
      mapsUrl: null, // No direct mapsUrl in this structure
      loaded: false,
      // Social media
      facebooks: null, // No direct social media fields in this structure
      instagrams: null,
      twitters: null,
      linkedIns: null,
      youtubes: null,
      tiktoks: null,
      pinterests: null,
      discords: null,
    };
  }

  private async loadAndProcessJson(): Promise<void> {
    console.log('Loading JSON file...');
    
    try {
      const fileContent = await fs.promises.readFile(this.inputFile, 'utf8');
      console.log('Parsing JSON...');
      
      const data: TreatwellEntry[] = JSON.parse(fileContent);
      console.log(`Found ${data.length} entries in the dataset`);

      let processedCount = 0;
      for (const entry of data) {
        const lead = this.mapTreatwellToLead(entry);
        if (lead) {
          this.extractedLeads.push(lead);
        }
        
        processedCount++;
        if (processedCount % 100 === 0) {
          console.log(`Processed ${processedCount}/${data.length} entries...`);
        }
      }

      console.log(`\nProcessing complete:`);
      console.log(`- Total entries processed: ${processedCount}`);
      console.log(`- Total leads extracted: ${this.extractedLeads.length}`);
      console.log(`- Duplicates/invalid skipped: ${processedCount - this.extractedLeads.length}`);
      
    } catch (error) {
      if (error instanceof Error && error.message.includes('JSON')) {
        console.error('JSON parsing error:', error.message);
        console.log('The file might not be valid JSON or might be too large for memory.');
        console.log('You may need to use a streaming approach or check the file format.');
      } else {
        console.error('Error loading file:', error);
      }
      throw error;
    }
  }

  private async saveToFile(): Promise<void> {
    const output = {
      metadata: {
        source: 'treatwell-scraper',
        extracted_at: new Date().toISOString(),
        total_leads: this.extractedLeads.length,
        fields: Object.keys(this.extractedLeads[0] || {}),
      },
      leads: this.extractedLeads
    };

    await fs.promises.writeFile(this.outputFile, JSON.stringify(output, null, 2));
    console.log(`\nResults saved to: ${this.outputFile}`);
  }

  private async saveToCSV(): Promise<void> {
    if (this.extractedLeads.length === 0) return;

    const csvFile = this.outputFile.replace('.json', '.csv');
    const headers = Object.keys(this.extractedLeads[0]).join(',');
    const rows = this.extractedLeads.map(lead => 
      Object.values(lead).map(value => {
        if (value === null) return '';
        if (typeof value === 'string' && value.includes(',')) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return String(value);
      }).join(',')
    );

    const csvContent = [headers, ...rows].join('\n');
    await fs.promises.writeFile(csvFile, csvContent);
    console.log(`CSV saved to: ${csvFile}`);
  }

  private displaySample(): void {
    console.log('\n--- Sample of extracted leads ---');
    const sample = this.extractedLeads.slice(0, 3);
    sample.forEach((lead, index) => {
      console.log(`\nLead ${index + 1}:`);
      console.log(`  Name: ${lead.Name}`);
      console.log(`  Address: ${lead.Address}`);
      console.log(`  Category: ${lead.Category}`);
      console.log(`  Phone: ${lead.Phone || 'N/A'}`);
      console.log(`  Website: ${lead.Website || 'N/A'}`);
      console.log(`  Rating: ${lead.Rating || 'N/A'}`);
    });
  }

  private displayStats(): void {
    const stats = {
      total: this.extractedLeads.length,
      withPhone: this.extractedLeads.filter(l => l.Phone).length,
      withEmail: this.extractedLeads.filter(l => l.Email).length,
      withWebsite: this.extractedLeads.filter(l => l.Website).length,
      withRating: this.extractedLeads.filter(l => l.Rating).length,
      categories: {} as Record<string, number>
    };

    // Count by category
    this.extractedLeads.forEach(lead => {
      const cat = lead.Category || 'unknown';
      stats.categories[cat] = (stats.categories[cat] || 0) + 1;
    });

    console.log('\n--- Extraction Statistics ---');
    console.log(`Total leads: ${stats.total}`);
    console.log(`With phone: ${stats.withPhone} (${Math.round(stats.withPhone/stats.total*100)}%)`);
    console.log(`With email: ${stats.withEmail} (${Math.round(stats.withEmail/stats.total*100)}%)`);
    console.log(`With website: ${stats.withWebsite} (${Math.round(stats.withWebsite/stats.total*100)}%)`);
    console.log(`With rating: ${stats.withRating} (${Math.round(stats.withRating/stats.total*100)}%)`);
    
    console.log('\nCategories:');
    Object.entries(stats.categories)
      .sort(([,a], [,b]) => b - a)
      .forEach(([category, count]) => {
        console.log(`  ${category}: ${count}`);
      });
  }

  async extract(): Promise<void> {
    console.log(`Starting extraction from: ${this.inputFile}`);
    
    if (!fs.existsSync(this.inputFile)) {
      throw new Error(`Input file not found: ${this.inputFile}`);
    }

    // Load and process the JSON
    await this.loadAndProcessJson();

    if (this.extractedLeads.length === 0) {
      console.log('No leads extracted. Check the input file format.');
      return;
    }

    // Display results
    this.displayStats();
    this.displaySample();

    // Save results
    await this.saveToFile();
    await this.saveToCSV();

    console.log('\nExtraction completed successfully!');
  }
}

// CLI interface
async function main() {
  console.log('Lead Extractor starting...');
  
  const args = process.argv.slice(2);
  console.log('Arguments:', args);
  
  if (args.length === 0) {
    console.log(`
Usage: npx tsx extract-leads.ts <input-file> [options]

Options:
  --output <file>     Output file (default: extracted-leads.json)

Example:
  npx tsx extract-leads.ts dataset_treatwell-search-scraper_2025-05-10_08-44-39-630.json
  npx tsx extract-leads.ts dataset.json --output my-leads.json
    `);
    process.exit(1);
  }

  const inputFile = args[0];
  console.log('Input file:', inputFile);
  
  const options: { outputFile?: string } = {};

  // Parse command line options
  for (let i = 1; i < args.length; i++) {
    if (args[i] === '--output' && args[i + 1]) {
      options.outputFile = args[i + 1];
      i++;
    }
  }

  try {
    const extractor = new LeadsExtractor(inputFile, options);
    await extractor.extract();
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Always run main
main().catch(console.error); 