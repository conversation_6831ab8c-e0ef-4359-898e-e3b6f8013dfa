import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { Plus, Edit, Trash2, Mail, Eye, Code, Play, Palette, Settings, Send } from "lucide-react";
import MainLayout from "@/layouts/MainLayout";
import { format } from "date-fns";
import { useEmailTemplates } from "@/hooks/useEmailTemplates";

interface EmailTemplate {
  id: string;
  name: string;
  description: string | null;
  template_type: 'welcome' | 'updates' | 'custom';
  subject_template: string;
  html_template: string;
  variables: Array<{ name: string; description: string }>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const AdminEmailTemplates = () => {
  const { sendEmail } = useEmailTemplates();
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
  const [previewVariables, setPreviewVariables] = useState<Record<string, string>>({});
  const [isTestOpen, setIsTestOpen] = useState(false);
  const [testEmail, setTestEmail] = useState("");
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    template_type: "custom" as 'welcome' | 'updates' | 'custom',
    subject_template: "",
    html_template: "",
    variables: [] as Array<{ name: string; description: string }>,
    is_active: true,
  });

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      console.log("Fetching email templates...");
      const { data, error } = await supabase
        .from("email_templates")
        .select("*")
        .order("created_at", { ascending: false });

      console.log("Templates query result:", { data, error });
      
      if (error) throw error;
      
      const templatesData = (data || []) as EmailTemplate[];
      console.log("Processed templates data:", templatesData);
      setTemplates(templatesData);
    } catch (error) {
      console.error("Error fetching templates:", error);
      toast.error("Errore nel caricamento dei template");
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async () => {
    try {
      const { error } = await supabase
        .from("email_templates")
        .insert([formData]);

      if (error) throw error;

      toast.success("Template creato con successo");
      setIsCreateOpen(false);
      resetForm();
      fetchTemplates();
    } catch (error) {
      console.error("Error creating template:", error);
      toast.error("Errore nella creazione del template");
    }
  };

  const handleUpdate = async () => {
    if (!selectedTemplate) return;

    try {
      const { error } = await supabase
        .from("email_templates")
        .update(formData)
        .eq("id", selectedTemplate.id);

      if (error) throw error;

      toast.success("Template aggiornato con successo");
      setIsEditOpen(false);
      resetForm();
      fetchTemplates();
    } catch (error) {
      console.error("Error updating template:", error);
      toast.error("Errore nell'aggiornamento del template");
    }
  };

  const handleDelete = async (id: string) => {
    try {
      const { error } = await supabase
        .from("email_templates")
        .delete()
        .eq("id", id);

      if (error) throw error;

      toast.success("Template eliminato con successo");
      fetchTemplates();
    } catch (error) {
      console.error("Error deleting template:", error);
      toast.error("Errore nell'eliminazione del template");
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      template_type: "custom" as 'welcome' | 'updates' | 'custom',
      subject_template: "",
      html_template: "",
      variables: [],
      is_active: true,
    });
    setSelectedTemplate(null);
  };

  const openEditDialog = (template: EmailTemplate) => {
    setSelectedTemplate(template);
    setFormData({
      name: template.name,
      description: template.description || "",
      template_type: template.template_type,
      subject_template: template.subject_template,
      html_template: template.html_template,
      variables: template.variables || [],
      is_active: template.is_active,
    });
    setIsEditOpen(true);
  };

  const openPreviewDialog = (template: EmailTemplate) => {
    setSelectedTemplate(template);
    // Initialize preview variables with empty values
    const initialVars: Record<string, string> = {};
    template.variables?.forEach(variable => {
      initialVars[variable.name] = `[${variable.description}]`;
    });
    setPreviewVariables(initialVars);
    setIsPreviewOpen(true);
  };

  const openTestDialog = (template: EmailTemplate) => {
    setSelectedTemplate(template);
    // Initialize preview variables with sample values
    const initialVars: Record<string, string> = {};
    template.variables?.forEach(variable => {
      initialVars[variable.name] = `Test ${variable.description}`;
    });
    setPreviewVariables(initialVars);
    setIsTestOpen(true);
  };

  const handleSendTestEmail = async () => {
    if (!selectedTemplate || !testEmail) return;

    const result = await sendEmail({
      templateId: selectedTemplate.id,
      toEmail: testEmail,
      variables: previewVariables,
    });

    if (result.success) {
      setIsTestOpen(false);
      setTestEmail("");
    }
  };

  const addVariable = () => {
    setFormData({
      ...formData,
      variables: [...formData.variables, { name: "", description: "" }]
    });
  };

  const removeVariable = (index: number) => {
    setFormData({
      ...formData,
      variables: formData.variables.filter((_, i) => i !== index)
    });
  };

  const updateVariable = (index: number, field: 'name' | 'description', value: string) => {
    const newVariables = [...formData.variables];
    newVariables[index][field] = value;
    setFormData({ ...formData, variables: newVariables });
  };

  const renderPreview = () => {
    if (!selectedTemplate) return "";
    
    let html = selectedTemplate.html_template;
    Object.entries(previewVariables).forEach(([key, value]) => {
      html = html.replace(new RegExp(`{{${key}}}`, 'g'), value);
    });
    return html;
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'welcome': return 'bg-green-100 text-green-800 border-green-200';
      case 'updates': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'welcome': return 'Benvenuto';
      case 'updates': return 'Aggiornamenti';
      default: return 'Personalizzato';
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Template Email
            </h1>
            <p className="text-muted-foreground text-lg">
              Gestisci i template email professionali per le tue campagne
            </p>
          </div>
          
          <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
            <DialogTrigger asChild>
              <Button size="lg" className="shadow-lg hover:shadow-xl transition-all duration-300">
                <Plus className="h-5 w-5 mr-2" />
                Nuovo Template
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="text-2xl font-bold flex items-center gap-2">
                  <Palette className="h-6 w-6" />
                  Crea Nuovo Template
                </DialogTitle>
                <DialogDescription>
                  Crea un template email professionale con editor avanzato
                </DialogDescription>
              </DialogHeader>
              
              <Tabs defaultValue="basic" className="space-y-6">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="basic">Informazioni Base</TabsTrigger>
                  <TabsTrigger value="content">Contenuto</TabsTrigger>
                  <TabsTrigger value="variables">Variabili</TabsTrigger>
                </TabsList>
                
                <TabsContent value="basic" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="text-sm font-semibold">Nome Template</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        placeholder="Es. Email di Benvenuto"
                        className="h-12"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="type" className="text-sm font-semibold">Tipo Template</Label>
                      <Select value={formData.template_type} onValueChange={(value: any) => setFormData({ ...formData, template_type: value })}>
                        <SelectTrigger className="h-12">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="welcome">Benvenuto</SelectItem>
                          <SelectItem value="updates">Aggiornamenti</SelectItem>
                          <SelectItem value="custom">Personalizzato</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description" className="text-sm font-semibold">Descrizione</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Descrivi quando utilizzare questo template..."
                      className="min-h-[80px]"
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="active"
                      checked={formData.is_active}
                      onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                    />
                    <Label htmlFor="active" className="text-sm font-semibold">Template Attivo</Label>
                  </div>
                </TabsContent>
                
                <TabsContent value="content" className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="subject" className="text-sm font-semibold">Oggetto Email</Label>
                    <Input
                      id="subject"
                      value={formData.subject_template}
                      onChange={(e) => setFormData({ ...formData, subject_template: e.target.value })}
                      placeholder="Benvenuto {{nome}}! Usa {{variabili}} per contenuto dinamico"
                      className="h-12"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="html" className="text-sm font-semibold">Contenuto HTML</Label>
                    <Textarea
                      id="html"
                      value={formData.html_template}
                      onChange={(e) => setFormData({ ...formData, html_template: e.target.value })}
                      placeholder="Inserisci il codice HTML del template..."
                      className="min-h-[300px] font-mono text-sm"
                    />
                  </div>
                </TabsContent>
                
                <TabsContent value="variables" className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Variabili Template</h3>
                    <Button onClick={addVariable} variant="outline" size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Aggiungi Variabile
                    </Button>
                  </div>
                  <div className="space-y-3">
                    {formData.variables.map((variable, index) => (
                      <div key={index} className="grid grid-cols-5 gap-3 items-center p-3 border rounded-lg">
                        <Input
                          placeholder="nome_variabile"
                          value={variable.name}
                          onChange={(e) => updateVariable(index, 'name', e.target.value)}
                          className="col-span-2"
                        />
                        <Input
                          placeholder="Descrizione variabile"
                          value={variable.description}
                          onChange={(e) => updateVariable(index, 'description', e.target.value)}
                          className="col-span-2"
                        />
                        <Button
                          onClick={() => removeVariable(index)}
                          variant="outline"
                          size="sm"
                          className="text-destructive hover:bg-destructive/10"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                    {formData.variables.length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Nessuna variabile definita</p>
                        <p className="text-sm">Aggiungi variabili per personalizzare il template</p>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => { setIsCreateOpen(false); resetForm(); }}>
                  Annulla
                </Button>
                <Button onClick={handleCreate} disabled={!formData.name || !formData.subject_template}>
                  <Mail className="h-4 w-4 mr-2" />
                  Crea Template
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border-l-4 border-l-primary shadow-md hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-muted-foreground">Totale Template</CardTitle>
              <CardDescription className="text-3xl font-bold text-primary">
                {templates.length}
              </CardDescription>
            </CardHeader>
          </Card>
          <Card className="border-l-4 border-l-green-500 shadow-md hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-muted-foreground">Template Attivi</CardTitle>
              <CardDescription className="text-3xl font-bold text-green-600">
                {templates.filter(t => t.is_active).length}
              </CardDescription>
            </CardHeader>
          </Card>
          <Card className="border-l-4 border-l-blue-500 shadow-md hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-muted-foreground">Benvenuto</CardTitle>
              <CardDescription className="text-3xl font-bold text-blue-600">
                {templates.filter(t => t.template_type === 'welcome').length}
              </CardDescription>
            </CardHeader>
          </Card>
          <Card className="border-l-4 border-l-purple-500 shadow-md hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-muted-foreground">Aggiornamenti</CardTitle>
              <CardDescription className="text-3xl font-bold text-purple-600">
                {templates.filter(t => t.template_type === 'updates').length}
              </CardDescription>
            </CardHeader>
          </Card>
        </div>

        {/* Templates Table */}
        <Card className="shadow-lg">
          <CardHeader className="border-b">
            <CardTitle className="text-xl font-bold flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Template Email
            </CardTitle>
            <CardDescription>
              Gestisci tutti i template email per le tue campagne
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="bg-muted/50">
                  <TableHead className="font-semibold">Nome</TableHead>
                  <TableHead className="font-semibold">Tipo</TableHead>
                  <TableHead className="font-semibold">Oggetto</TableHead>
                  <TableHead className="font-semibold">Variabili</TableHead>
                  <TableHead className="font-semibold">Status</TableHead>
                  <TableHead className="font-semibold">Data Creazione</TableHead>
                  <TableHead className="text-right font-semibold">Azioni</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {templates.map((template) => (
                  <TableRow key={template.id} className="hover:bg-muted/30 transition-colors">
                    <TableCell className="font-medium">{template.name}</TableCell>
                    <TableCell>
                      <Badge className={getTypeColor(template.template_type)}>
                        {getTypeLabel(template.template_type)}
                      </Badge>
                    </TableCell>
                    <TableCell className="max-w-xs truncate">
                      {template.subject_template}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {template.variables?.length || 0} variabili
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={template.is_active ? "default" : "secondary"}>
                        {template.is_active ? "Attivo" : "Inattivo"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {format(new Date(template.created_at), "dd/MM/yyyy HH:mm")}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openTestDialog(template)}
                          className="hover:bg-green-500/10 text-green-600"
                        >
                          <Send className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openPreviewDialog(template)}
                          className="hover:bg-primary/10"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openEditDialog(template)}
                          className="hover:bg-secondary/10"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="ghost" size="sm" className="hover:bg-destructive/10 text-destructive">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Elimina Template</AlertDialogTitle>
                              <AlertDialogDescription>
                                Sei sicuro di voler eliminare il template "{template.name}"? 
                                Questa azione non può essere annullata.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Annulla</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDelete(template.id)}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                              >
                                Elimina
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {templates.length === 0 && (
              <div className="text-center py-12">
                <Mail className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-lg font-medium text-muted-foreground">Nessun template trovato</p>
                <p className="text-sm text-muted-foreground">Crea il tuo primo template email per iniziare</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Edit Dialog */}
        <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
          <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-2xl font-bold flex items-center gap-2">
                <Edit className="h-6 w-6" />
                Modifica Template
              </DialogTitle>
              <DialogDescription>
                Aggiorna il template email
              </DialogDescription>
            </DialogHeader>
            
            <Tabs defaultValue="basic" className="space-y-6">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic">Informazioni Base</TabsTrigger>
                <TabsTrigger value="content">Contenuto</TabsTrigger>
                <TabsTrigger value="variables">Variabili</TabsTrigger>
              </TabsList>
              
              <TabsContent value="basic" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-name" className="text-sm font-semibold">Nome Template</Label>
                    <Input
                      id="edit-name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="h-12"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-type" className="text-sm font-semibold">Tipo Template</Label>
                    <Select value={formData.template_type} onValueChange={(value: any) => setFormData({ ...formData, template_type: value })}>
                      <SelectTrigger className="h-12">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="welcome">Benvenuto</SelectItem>
                        <SelectItem value="updates">Aggiornamenti</SelectItem>
                        <SelectItem value="custom">Personalizzato</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-description" className="text-sm font-semibold">Descrizione</Label>
                  <Textarea
                    id="edit-description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="min-h-[80px]"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="edit-active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                  />
                  <Label htmlFor="edit-active" className="text-sm font-semibold">Template Attivo</Label>
                </div>
              </TabsContent>
              
              <TabsContent value="content" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-subject" className="text-sm font-semibold">Oggetto Email</Label>
                  <Input
                    id="edit-subject"
                    value={formData.subject_template}
                    onChange={(e) => setFormData({ ...formData, subject_template: e.target.value })}
                    className="h-12"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-html" className="text-sm font-semibold">Contenuto HTML</Label>
                  <Textarea
                    id="edit-html"
                    value={formData.html_template}
                    onChange={(e) => setFormData({ ...formData, html_template: e.target.value })}
                    className="min-h-[300px] font-mono text-sm"
                  />
                </div>
              </TabsContent>
              
              <TabsContent value="variables" className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Variabili Template</h3>
                  <Button onClick={addVariable} variant="outline" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Aggiungi Variabile
                  </Button>
                </div>
                <div className="space-y-3">
                  {formData.variables.map((variable, index) => (
                    <div key={index} className="grid grid-cols-5 gap-3 items-center p-3 border rounded-lg">
                      <Input
                        placeholder="nome_variabile"
                        value={variable.name}
                        onChange={(e) => updateVariable(index, 'name', e.target.value)}
                        className="col-span-2"
                      />
                      <Input
                        placeholder="Descrizione variabile"
                        value={variable.description}
                        onChange={(e) => updateVariable(index, 'description', e.target.value)}
                        className="col-span-2"
                      />
                      <Button
                        onClick={() => removeVariable(index)}
                        variant="outline"
                        size="sm"
                        className="text-destructive hover:bg-destructive/10"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => { setIsEditOpen(false); resetForm(); }}>
                Annulla
              </Button>
              <Button onClick={handleUpdate}>
                <Edit className="h-4 w-4 mr-2" />
                Aggiorna Template
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Preview Dialog */}
        <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
          <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-2xl font-bold flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Anteprima Template
              </DialogTitle>
            </DialogHeader>
            {selectedTemplate && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm font-semibold text-muted-foreground">Nome Template</Label>
                      <p className="text-lg font-medium">{selectedTemplate.name}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-semibold text-muted-foreground">Tipo</Label>
                      <Badge className={getTypeColor(selectedTemplate.template_type)}>
                        {getTypeLabel(selectedTemplate.template_type)}
                      </Badge>
                    </div>
                    <div>
                      <Label className="text-sm font-semibold text-muted-foreground">Oggetto</Label>
                      <p className="text-base font-medium">{selectedTemplate.subject_template}</p>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Variabili di Test</h3>
                    {selectedTemplate.variables?.map((variable) => (
                      <div key={variable.name} className="space-y-2">
                        <Label className="text-sm font-medium">{variable.name}</Label>
                        <Input
                          placeholder={variable.description}
                          value={previewVariables[variable.name] || ""}
                          onChange={(e) => setPreviewVariables({
                            ...previewVariables,
                            [variable.name]: e.target.value
                          })}
                        />
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-lg font-semibold">Anteprima Email</Label>
                    <Button
                      onClick={() => {
                        const newWindow = window.open();
                        if (newWindow) {
                          newWindow.document.write(renderPreview());
                          newWindow.document.close();
                        }
                      }}
                      variant="outline"
                      size="sm"
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Apri in Nuova Finestra
                    </Button>
                  </div>
                  <div className="border rounded-lg p-4 bg-white max-h-[400px] overflow-y-auto">
                    <div dangerouslySetInnerHTML={{ __html: renderPreview() }} />
                  </div>
                </div>
              </div>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsPreviewOpen(false)}>
                Chiudi
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Test Email Dialog */}
        <Dialog open={isTestOpen} onOpenChange={setIsTestOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle className="text-2xl font-bold flex items-center gap-2">
                <Send className="h-5 w-5" />
                Invia Email di Test
              </DialogTitle>
              <DialogDescription>
                Invia una email di test per verificare il template
              </DialogDescription>
            </DialogHeader>
            {selectedTemplate && (
              <div className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-semibold text-muted-foreground">Template</Label>
                    <p className="text-lg font-medium">{selectedTemplate.name}</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="test-email" className="text-sm font-semibold">Email di Test</Label>
                    <Input
                      id="test-email"
                      type="email"
                      value={testEmail}
                      onChange={(e) => setTestEmail(e.target.value)}
                      placeholder="<EMAIL>"
                      className="h-12"
                    />
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Variabili di Test</h3>
                  {selectedTemplate.variables?.map((variable) => (
                    <div key={variable.name} className="space-y-2">
                      <Label className="text-sm font-medium">{variable.name}</Label>
                      <Input
                        placeholder={variable.description}
                        value={previewVariables[variable.name] || ""}
                        onChange={(e) => setPreviewVariables({
                          ...previewVariables,
                          [variable.name]: e.target.value
                        })}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsTestOpen(false)}>
                Annulla
              </Button>
              <Button 
                onClick={handleSendTestEmail} 
                disabled={!testEmail}
                className="bg-green-600 hover:bg-green-700"
              >
                <Send className="h-4 w-4 mr-2" />
                Invia Test
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  );
};

export default AdminEmailTemplates;