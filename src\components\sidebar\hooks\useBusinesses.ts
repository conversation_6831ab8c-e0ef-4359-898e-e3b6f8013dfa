
import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { Business } from "@/store/businessStore";
import { useToast } from "@/components/ui/use-toast";
import { Database } from "@/integrations/supabase/types";
import { useBusinessStore } from "@/store/businessStore";

// Tipo che rappresenta i dati restituiti dalla vista businesses_with_counts
type BusinessWithCounts = Database['public']['Views']['businesses_with_counts']['Row'];

export const useBusinesses = () => {
  const auth = useAuth();
  const { user, isLoading: authLoading } = auth;
  const businessStore = useBusinessStore();
  const { businesses, selectedBusiness, isLoading, fetchBusinesses, selectBusiness } = businessStore;
  const { toast } = useToast();

  // Carica le attività quando l'utente è autenticato
  useEffect(() => {
    // Skip if auth is still loading or no user
    if (authLoading || !user) {
      return;
    }

    // Carica le attività dell'utente
    fetchBusinesses(user.id);
  }, [authLoading, user, fetchBusinesses]);

  // Handle business selection with localStorage
  const handleSelectBusiness = (business: Business) => {
    selectBusiness(business);
    
    toast({
      title: "Attività Selezionata",
      description: `Hai selezionato ${business.name}`,
      duration: 2000,
    });
  };

  return {
    businesses,
    selectedBusiness,
    isLoading,
    handleSelectBusiness
  };
};
