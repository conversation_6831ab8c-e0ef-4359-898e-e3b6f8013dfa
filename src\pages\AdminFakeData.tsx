import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { faker } from "@faker-js/faker";
import { useAuth } from "@/contexts/AuthContext";

import { v4 as uuidv4 } from "uuid";
import { daysNames } from "@/features/fakedata/data/daysNames";
import { WeeklySchedule } from "@/types/deals";
import { italianData } from "@/features/fakedata/data/data";
import { Booking } from "@/features/fakedata/types/";
import { cities } from "@/features/fakedata/data/cities";
// import { useBooking } from "@/hooks/useBooking";
import {
  BusinessSelectionList,
  DateRangeSelector,
} from "@/components/datageneration";
import { useNavigate } from "react-router-dom";
import { BarChart2, Palette } from "lucide-react";
import { Input } from "@/components/ui/input";
import MainLayout from "@/layouts/MainLayout";
import { DealEmbeddingsManager } from "@/components/DealEmbeddingsManager";
// Define types for category, business, and deals
type Category = {
  id: string;
  name: string;
  icon?: string;
  description?: string;
};

/**
 * AdminDataPage component for generating synthetic data
 *
 * This page allows administrators to generate random synthetic data for:
 * 1. Businesses for selected categories
 * 2. Deals for selected businesses
 *
 * Uses Italian-specific data formats for more realistic data generation.
 */
const AdminDataPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [categories, setCategories] = useState<Category[]>([]);
  const [businesses, setBusinesses] = useState<any[]>([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("");
  const [selectedBusinesses, setSelectedBusinesses] = useState<string[]>([]);
  const [numberOfBusinesses, setNumberOfBusinesses] = useState<number>(1);
  const [numberOfDeals, setNumberOfDeals] = useState<number>(1);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedCity, setSelectedCity] = useState<string>("Milano");
  const [selectedDealCity, setSelectedDealCity] = useState<string>("");
  const [selectedDealCategory, setSelectedDealCategory] = useState<string>("");
  const [filteredBusinesses, setFilteredBusinesses] = useState<any[]>([]);
  const [progress, setProgress] = useState<number>(0);
  const [showProgress, setShowProgress] = useState<boolean>(false);
  const [progressMessage, setProgressMessage] = useState<string>("");
  const [numberOfUsers, setNumberOfUsers] = useState<number>(1);
  const [isLoadingUsers, setIsLoadingUsers] = useState<boolean>(false);
  const [userProgress, setUserProgress] = useState<number>(0);
  const [showUserProgress, setShowUserProgress] = useState<boolean>(false);
  const [userProgressMessage, setUserProgressMessage] = useState<string>("");
  const [startingUserIndex, setStartingUserIndex] = useState<number>(1);
  const [userIds, setUserIds] = useState<string[]>([]);
  const [businessProgress, setBusinessProgress] = useState<number>(0);
  const [showBusinessProgress, setShowBusinessProgress] =
    useState<boolean>(false);
  const [businessProgressMessage, setBusinessProgressMessage] =
    useState<string>("");
  const [expandedSections, setExpandedSections] = useState<
    Record<string, boolean>
  >({
    users: false,
    businesses: false,
    deals: false,
    bookings: false,
  });
  const [bookingProgress, setBookingProgress] = useState<number>(0);
  const [showBookingProgress, setShowBookingProgress] =
    useState<boolean>(false);
  const [bookingProgressMessage, setBookingProgressMessage] =
    useState<string>("");
  const [numberOfBookings, setNumberOfBookings] = useState<number>(10);
  const [selectedBookingCity, setSelectedBookingCity] = useState<string>("");
  const [selectedBookingCategory, setSelectedBookingCategory] =
    useState<string>("");
  const [selectedBookingBusinesses, setSelectedBookingBusinesses] = useState<
    string[]
  >([]);
  const [filteredBookingBusinesses, setFilteredBookingBusinesses] = useState<
    any[]
  >([]);

  // Date range state for deals
  const [dealStartDate, setDealStartDate] = useState<string>(
    new Date().toISOString().split("T")[0]
  );
  const [dealEndDate, setDealEndDate] = useState<string>(() => {
    const date = new Date();
    date.setDate(date.getDate() + 30); // Default to 30 days in the future
    return date.toISOString().split("T")[0];
  });

  // Random days state for deals
  const [randomStartDays, setRandomStartDays] = useState<number>(0);
  const [randomEndDays, setRandomEndDays] = useState<number>(0);

  // Date range state for bookings
  const [bookingStartDate, setBookingStartDate] = useState<string>(() => {
    const date = new Date();
    date.setDate(date.getDate() - 30); // Default to 30 days in the past
    return date.toISOString().split("T")[0];
  });
  const [bookingEndDate, setBookingEndDate] = useState<string>(
    new Date().toISOString().split("T")[0]
  );

  // State for delete operations
  const [isDeletingDeals, setIsDeletingDeals] = useState<boolean>(false);
  const [isDeletingBookings, setIsDeletingBookings] = useState<boolean>(false);
  const [isDeletingBusinesses, setIsDeletingBusinesses] =
    useState<boolean>(false);
  const [deleteProgress, setDeleteProgress] = useState<number>(0);
  const [deleteProgressMessage, setDeleteProgressMessage] =
    useState<string>("");
  const [showDeleteProgress, setShowDeleteProgress] = useState<boolean>(false);

  // Business counters state
  const [totalBusinessesCount, setTotalBusinessesCount] = useState<number>(0);
  const [fakeBusinessesCount, setFakeBusinessesCount] = useState<number>(0);

  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      users: false,
      businesses: false,
      deals: false,
      bookings: false,
      [section]: !prev[section],
    }));
  };
  // Mappa le categorie del database alle nostre categorie di nomi
  const mapCategoryToNameType = async (categoryId: string): Promise<string> => {
    try {
      // Otteniamo il nome della categoria dal database
      const { data, error } = await supabase
        .from("categories")
        .select("name")
        .eq("id", categoryId)
        .single();

      if (error || !data) {
        return "default";
      }

      // Normalizza il nome della categoria in minuscolo e senza spazi
      const categoryName = (data as any).name.toLowerCase();

      // Mappa il nome della categoria a uno dei nostri tipi
      if (
        categoryName.includes("bar") ||
        categoryName.includes("caffè") ||
        categoryName.includes("café") ||
        categoryName.includes("pub")
      ) {
        return "bar";
      }

      if (
        categoryName.includes("ristorante") ||
        categoryName.includes("trattoria") ||
        categoryName.includes("pizzeria") ||
        categoryName.includes("osteria") ||
        categoryName.includes("cibo")
      ) {
        return "ristorante";
      }

      if (
        categoryName.includes("aliment") ||
        categoryName.includes("super") ||
        categoryName.includes("market") ||
        categoryName.includes("gastronom") ||
        categoryName.includes("panificio") ||
        categoryName.includes("macelleria")
      ) {
        return "alimentari";
      }

      if (
      
        categoryName.includes("estet") ||
        categoryName.includes("bellezza") ||
        categoryName.includes("salon") ||
      
        categoryName.includes("beauty")
      ) {
        return "bellezza";
      }

      if (
     
        categoryName.includes("barbier")
    
      ) {
        return "capelli_uomo";
      }
if (
     
        categoryName.includes("parrucc")
    
      ) {
        return  Math.random() > 0.5 ? "capelli_donna" : "capelli_uomo";
      }


      if (
        categoryName.includes("abbigliam") ||
        categoryName.includes("moda") ||
        categoryName.includes("boutique") ||
        categoryName.includes("sartoria") ||
        categoryName.includes("calzature")
      ) {
        return "abbigliamento";
      }

      if (
        categoryName.includes("palestra") ||
        categoryName.includes("fitness") ||
        categoryName.includes("gym")
      ) {
        return "palestra";
      }

      if (
        categoryName.includes("spa") ||
        categoryName.includes("benessere") ||
        categoryName.includes("relax")
      ) {
        return "spa";
      }

      if (
        categoryName.includes("cinema") ||
        categoryName.includes("multisala") ||
        categoryName.includes("film")
      ) {
        return "cinema";
      }

      if (
        categoryName.includes("fioraio") ||
        categoryName.includes("fiori") ||
        categoryName.includes("floreale") ||
        categoryName.includes("fioreria")
      ) {
        return "fioraio";
      }

      if (
        categoryName.includes("hotel") ||
        categoryName.includes("albergo") ||
        categoryName.includes("alloggi") ||
        categoryName.includes("bed") ||
        categoryName.includes("breakfast")
      ) {
        return "hotel";
      }

      if (
        categoryName.includes("teatro") ||
        categoryName.includes("spettacolo") ||
        categoryName.includes("teatrale")
      ) {
        return "teatro";
      }

      if (
        categoryName.includes("aperitiv") ||
        categoryName.includes("cocktail") ||
        categoryName.includes("spritz") ||
        categoryName.includes("mixology")
      ) {
        return "aperitivo";
      }

      // Se non troviamo corrispondenze, usiamo il default
      return "default";
    } catch (error) {
      console.error("Errore nel determinare il tipo di categoria:", error);
      return "default";
    }
  };

  // Generate an Italian company name based on category
  const generateItalianCompanyName = async (categoryId: string) => {
    const categoryType = await mapCategoryToNameType(categoryId);
   // console.log("Category type:", categoryType);
    const names =
      italianData.companyNamesByCategory[
        categoryType as keyof typeof italianData.companyNamesByCategory
      ] || italianData.companyNamesByCategory.default;

    const useSuffix = Math.random() > 0.7; // 30% di probabilità di aggiungere un suffisso
    const name = faker.helpers.arrayElement(names);
    return useSuffix
      ? `${name} ${faker.helpers.arrayElement(italianData.companySuffixes)}`
      : name;
  };

  // Generate an Italian phone number
  const generateItalianPhone = () => {
    const format = faker.helpers.arrayElement(italianData.phoneFormats);
    return faker.phone.number(format);
  };

  // Generate an Italian email
  const generateItalianEmail = (companyName: string) => {
    const simplifiedName = companyName
      .replace(/[^\w\s]/gi, "") // remove special chars
      .replace(/\s+/g, ".") // replace spaces with dots
      .toLowerCase();

    const domain = faker.helpers.arrayElement(italianData.domains);
    return `info@${simplifiedName}.${domain}`;
  };

  // Generate an Italian website URL
  const generateItalianWebsite = (companyName: string) => {
    const simplifiedName = companyName
      .replace(/[^\w\s]/gi, "") // remove special chars
      .replace(/\s+/g, "-") // replace spaces with hyphens
      .toLowerCase();

    const domain = faker.helpers.arrayElement(italianData.domains);
    return `https://www.${simplifiedName}.${domain}`;
  };
  const filterBusinessByCity = (business: any, cityName: string): boolean => {
    if (!business.city) return false;
    return business.city.toLowerCase().trim() === cityName.toLowerCase();
  };

  // Fetch all categories when the component mounts
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const { data, error } = await supabase
          .from("categories")
          .select("*")
          .order("name");

        if (error) {
          toast.error("Errore nel caricamento delle categorie");
          return;
        }

        if (data) {
          // Type assertion to ensure type safety
          setCategories(data as unknown as Category[]);
        }
      } catch (error) {
        toast.error("Si è verificato un errore");
      }
    };

    fetchCategories();
  }, []);

  // Fetch all businesses when the component mounts or city filter changes
  useEffect(() => {
    fetchBusinesses();
    fetchBusinessCounts();
    fetchUserIds();
  }, []);

  // Fetch businesses from database
  const fetchBusinesses = async () => {
    try {
      const { data, error } = await supabase
        .from("businesses")
        .select("*")
        .eq("fake", true)
        .order("name");

      if (error) {
        toast.error("Errore nel caricamento delle attività");
        return;
      }

      setBusinesses(data || []);
      setFilteredBusinesses(data || []);
      setFilteredBookingBusinesses(data || []);

      // Update business counts
      await fetchBusinessCounts();
    } catch (error) {
      toast.error("Si è verificato un errore");
    }
  };

  // Fetch business counts
  const fetchBusinessCounts = async () => {
    try {
      // Get total businesses count
      const { count: totalCount, error: totalError } = await supabase
        .from("businesses")
        .select("*", { count: "exact", head: true });

      // Get fake businesses count
      const { count: fakeCount, error: fakeError } = await supabase
        .from("businesses")
        .select("*", { count: "exact", head: true })
        .eq("fake", true);

      if (totalError) {
        console.error("Error fetching total businesses count:", totalError);
      } else {
        setTotalBusinessesCount(totalCount || 0);
      }

      if (fakeError) {
        console.error("Error fetching fake businesses count:", fakeError);
      } else {
        setFakeBusinessesCount(fakeCount || 0);
      }
    } catch (error) {
      console.error("Error fetching business counts:", error);
    }
  };

  // Filter businesses by city
  const filterBusinessesByCity = (_city: string) => {
    if (_city === "") {
      setFilteredBusinesses(businesses);
    } else {
      const filtered = businesses.filter((business) =>
        filterBusinessByCity(business, _city)
      );
      setFilteredBusinesses(filtered);
    }
  };
  // Filter businesses by city for bookings
  const filterBookingBusinessesByCity = (_city: string) => {
    if (_city === "") {
      setFilteredBookingBusinesses(businesses);
    } else {
      const filtered = businesses.filter((business) =>
        filterBusinessByCity(business, _city)
      );

      setFilteredBookingBusinesses(filtered);
    }
  };

  // Handle city selection change for deals
  const handleDealCityChange = (city: string) => {
    setSelectedDealCity(city);
    filterBusinessesByCity(city);
    //setSelectedBusinesses([]); // Reset selected businesses when changing city
    //setSelectedDealCategory(""); // Reset category filter when changing city
  };

  // Handle category selection change for deals
  const handleDealCategoryChange = async (category: string) => {
    console.log("Categoria selezionata:", category);
    setSelectedDealCategory(category);
    setSelectedBusinesses([]); // Reset selected businesses when changing category

    if (!category) {
      // Se nessuna categoria è selezionata, filtra solo per città
      filterBusinessesByCity(selectedDealCity);
      return;
    }

    try {
      // Inizia con tutti i business o quelli filtrati per città
      let baseBusinesses = [];
      if (selectedDealCity) {
        // Applica prima il filtro per città
        const { data, error } = await supabase
          .from("businesses")
          .select("*")
          .order("name");

        if (error) {
          toast.error("Errore nel recupero delle attività");
          return;
        }

        baseBusinesses = data || [];

        // Filtra per città
        baseBusinesses = baseBusinesses.filter((business) =>
          filterBusinessByCity(business, selectedDealCity)
        );
      } else {
        // Se nessuna città è selezionata, usa tutti i business
        const { data, error } = await supabase
          .from("businesses")
          .select("*")
          .order("name");

        if (error) {
          toast.error("Errore nel recupero delle attività");
          return;
        }

        baseBusinesses = data || [];
      }

      // Ottieni l'ID della categoria selezionata
      const { data: categoryData, error: categoryError } = await supabase
        .from("categories")
        .select("id")
        .eq("name", category)
        .maybeSingle();

      if (categoryError || !categoryData) {
        toast.error("Errore nel recupero della categoria selezionata");
        return;
      }

      const categoryId = (categoryData as any).id;
      console.log("ID categoria:", categoryId);

      // Filtra i business per la categoria selezionata
      const filteredByCategory = baseBusinesses.filter((business) => {
        return business.category_id === categoryId;
      });

      console.log(
        "Business filtrati per categoria:",
        filteredByCategory.length
      );

      // Aggiorna lo state con i business filtrati
      setFilteredBusinesses(filteredByCategory);

      if (filteredByCategory.length === 0) {
        console.warn(`Nessuna attività trovata per la categoria ${category}`);
      }
    } catch (error) {
      console.error("Errore durante il filtraggio per categoria:", error);
      //     toast.error("Errore durante il filtraggio per categoria");
    }
  };

  // Handle business selection toggle
  const handleBusinessToggle = (businessId: string) => {
    setSelectedBusinesses((prevSelected) => {
      if (prevSelected.includes(businessId)) {
        return prevSelected.filter((id) => id !== businessId);
      } else {
        return [...prevSelected, businessId];
      }
    });
  };

  // Handle select all businesses
  const handleSelectAllBusinesses = () => {
    if (selectedBusinesses.length === filteredBusinesses.length) {
      // Se tutte le attività sono già selezionate, deseleziona tutte
      setSelectedBusinesses([]);
    } else {
      // Altrimenti seleziona tutte le attività filtrate
      setSelectedBusinesses(filteredBusinesses.map((business) => business.id));
    }
  };

  // Handle business selection for bookings
  const handleBookingBusinessToggle = (businessId: string) => {
    setSelectedBookingBusinesses((prevSelected) => {
      if (prevSelected.includes(businessId)) {
        return prevSelected.filter((id) => id !== businessId);
      } else {
        return [...prevSelected, businessId];
      }
    });
  };

  // Handle select all businesses for bookings
  const handleSelectAllBookingBusinesses = () => {
    if (selectedBookingBusinesses.length === filteredBookingBusinesses.length) {
      // Se tutte le attività sono già selezionate, deseleziona tutte
      setSelectedBookingBusinesses([]);
    } else {
      // Altrimenti seleziona tutte le attività filtrate
      setSelectedBookingBusinesses(
        filteredBookingBusinesses.map((business) => business.id)
      );
    }
  };

  // Filter booking businesses by category
  const handleBookingBusinessCategoryChange = async (category: string) => {
    setSelectedBookingCategory(category);
    setSelectedBookingBusinesses([]); // Reset selected businesses when changing category

    if (!category) {
      // Se nessuna categoria è selezionata, filtra solo per città
      filterBookingBusinessesByCity(selectedBookingCity);
      return;
    }

    try {
      // Inizia con tutti i business o quelli filtrati per città
      let baseBusinesses = [];
      if (selectedBookingCity) {
        // Applica prima il filtro per città
        const { data, error } = await supabase
          .from("businesses")
          .select("*")
          .order("name");

        if (error) {
          console.error("Errore nel recupero delle attività");
          return;
        }

        baseBusinesses = data || [];

        // Filtra per città
        baseBusinesses = baseBusinesses.filter((business) =>
          filterBusinessByCity(business, selectedBookingCity)
        );
      } else {
        // Se nessuna città è selezionata, usa tutti i business
        const { data, error } = await supabase
          .from("businesses")
          .select("*")
          .order("name");

        if (error) {
          toast.error("Errore nel recupero delle attività");
          return;
        }

        baseBusinesses = data || [];
      }

      // Ottieni l'ID della categoria selezionata
      const { data: categoryData, error: categoryError } = await supabase
        .from("categories")
        .select("id")
        .eq("name", category)
        .maybeSingle();

      if (categoryError || !categoryData) {
        toast.error("Errore nel recupero della categoria selezionata");
        return;
      }

      const categoryId = (categoryData as any).id;

      // Filtra i business per la categoria selezionata
      const filteredByCategory = baseBusinesses.filter((business) => {
        return business.category_id === categoryId;
      });

      // Aggiorna lo state con i business filtrati
      setFilteredBookingBusinesses(filteredByCategory);

      if (filteredByCategory.length === 0) {
        toast.warning(`Nessuna attività trovata per la categoria ${category}`);
      }
    } catch (error) {
      console.error("Errore durante il filtraggio per categoria:", error);
      toast.error("Errore durante il filtraggio per categoria");
    }
  };

  function getRandomInt0(max: number) {
    return Math.floor(Math.random() * max);
  }
  // Generate a random weekly schedule for deals
  const generateRandomSchedule = (dayNames: string[]): WeeklySchedule => {
    // Create a schedule for every day in the week
    const schedule = dayNames.map((dayName, index) => {
      // Decide randomly whether this day will have any time slots.
      // For example, 30% chance to have time slots.
      const addSlots = Math.random() < 0.3;

      let time_slots = [];
      if (addSlots) {
        // Generate 1-3 time slots for this day (at least one slot)
        const numSlots = getRandomInt0(3) + 1;

        // Choose a random duration for the day: 30, 60, or 90 minutes.
        const durations = [30, 60, 90];
        const duration = durations[getRandomInt0(durations.length)];

        // Generate a random start time between 8:00 and 19:00 (on the hour)
        const startHour = Math.floor(Math.random() * 12) + 8;
        const startMinutes = 0;
        let currentStartTime = startHour * 60 + startMinutes;

        time_slots = Array.from({ length: numSlots }, () => {
          const slotStart = currentStartTime;
          const slotEnd = slotStart + duration;
          currentStartTime = slotEnd;

          const start_time = `${Math.floor(slotStart / 60)
            .toString()
            .padStart(2, "0")}:${(slotStart % 60).toString().padStart(2, "0")}`;
          const end_time = `${Math.floor(slotEnd / 60)
            .toString()
            .padStart(2, "0")}:${(slotEnd % 60).toString().padStart(2, "0")}`;

          // Generate a random number of available seats between 1 and 10
          const availableSeats = Math.floor(Math.random() * 9) + 1;

          // Generate a random number of booked seats between 0 and availableSeats
          const bookedSeats = 0;

          return {
            start_time,
            end_time,
            available_seats: availableSeats,
          };
        });
      }

      return {
        day: index + 1, // 1-indexed day number
        day_name: dayName,
        time_slots,
      };
    });

    // Check if at least one day has time slots
    const hasActiveDay = schedule.some((day) => day.time_slots.length > 0);

    // If no day has time slots, add time slots to a random day
    if (!hasActiveDay) {
      // Select a random day to add time slots
      const randomDayIndex = Math.floor(Math.random() * schedule.length);
      const randomDay = schedule[randomDayIndex];

      // Generate 1-2 time slots for this day
      const numSlots = getRandomInt0(2) + 1;

      // Choose a random duration: 60 or 90 minutes
      const duration = Math.random() < 0.5 ? 60 : 90;

      // Generate a random start time between 9:00 and 18:00
      const startHour = Math.floor(Math.random() * 9) + 9;
      const startMinutes = 0;
      let currentStartTime = startHour * 60 + startMinutes;

      randomDay.time_slots = Array.from({ length: numSlots }, () => {
        const slotStart = currentStartTime;
        const slotEnd = slotStart + duration;
        currentStartTime = slotEnd;

        const start_time = `${Math.floor(slotStart / 60)
          .toString()
          .padStart(2, "0")}:${(slotStart % 60).toString().padStart(2, "0")}`;
        const end_time = `${Math.floor(slotEnd / 60)
          .toString()
          .padStart(2, "0")}:${(slotEnd % 60).toString().padStart(2, "0")}`;

        // Generate a random number of available seats between 1 and 10
        const availableSeats = Math.floor(Math.random() * 9) + 1;

        return {
          start_time,
          end_time,
          available_seats: availableSeats,
        };
      });

      console.log(
        `Added time slots to day ${randomDay.day_name} because no days had time slots`
      );
    }

    return {
      schedule,
      exceptions: [],
    };
  };

  // Generate coordinates for a specific city
  const generateCityCoordinates = (cityName: string): [number, number] => {
    const city = cities.find((c) => c.name === cityName) || cities[0];

    const latitude = faker.number.float({
      min: city.latitude.min,
      max: city.latitude.max,
      precision: 0.000001,
    });

    const longitude = faker.number.float({
      min: city.longitude.min,
      max: city.longitude.max,
      precision: 0.000001,
    });

    return [latitude, longitude];
  };

  // Generate a street address for a selected city
  const generateCityAddress = (
    cityName: string
  ): {
    address: string;
    zip_code: string;
    city: string;
    state: string;
    country: string;
  } => {
    // Genera un tipo di strada (Via, Corso, Piazza, ecc.) e un nome
    let streetNameType: string;
    let streetNameMain: string;

    if (Math.random() > 0.5) {
      // Usa un nome di via esistente dalla lista
      const streetFullName = faker.helpers.arrayElement(
        italianData.streetNames
      );
      const parts = streetFullName.split(" ");
      streetNameType = parts[0];
      streetNameMain = parts.slice(1).join(" ");
    } else {
      // Genera un nome di via basato su un nome e cognome italiano
      streetNameType = "Via";
      if (Math.random() > 0.7) streetNameType = "Corso";
      if (Math.random() > 0.9) streetNameType = "Piazza";

      if (Math.random() > 0.5) {
        // Nome e cognome
        streetNameMain = `${faker.helpers.arrayElement(
          italianData.firstNames
        )} ${faker.helpers.arrayElement(italianData.lastNames)}`;
      } else {
        // Solo cognome
        streetNameMain = faker.helpers.arrayElement(italianData.lastNames);
      }
    }

    // Genera un numero civico realistico
    const streetNumber = faker.number.int({ min: 1, max: 200 });

    // Ottieni un CAP valido per la città
    const zipCodes = italianData.zipCodes[
      cityName as keyof typeof italianData.zipCodes
    ] || ["00000"];
    const zipCode = faker.helpers.arrayElement(zipCodes);

    // Ottieni la provincia
    const provincia =
      italianData.provinces[cityName as keyof typeof italianData.provinces] ||
      "";

    const address = `${streetNameType} ${streetNameMain}, ${streetNumber}`;

    // Formatta l'indirizzo completo
    return {
      address,
      zip_code: zipCode,
      city: cityName,
      state: provincia,
      country: "Italia",
    };
  };

  // Generate random businesses for the selected category
  const generateBusinesses = async () => {
    if (!selectedCategoryId) {
      toast.error("Seleziona una categoria");
      return;
    }

    setIsLoading(true);
    setBusinessProgress(0);
    setShowBusinessProgress(true);
    setBusinessProgressMessage("Inizializzazione creazione attività...");

    try {
      // Create an array of random business data
      const businessesToCreate = [];

      // Creiamo le attività una alla volta (per consentire l'uso di await con generateItalianCompanyName)
      for (let i = 0; i < numberOfBusinesses; i++) {
        // Update progress
        const progressPercentage = Math.min(
          Math.round((i / numberOfBusinesses) * 100),
          100
        );
        setBusinessProgress(progressPercentage);
        setBusinessProgressMessage(
          `Creazione attività ${i + 1} di ${numberOfBusinesses}...`
        );

        // Generate coordinates within the selected city
        const [latitude, longitude] = generateCityCoordinates(selectedCity);
        const companyName = await generateItalianCompanyName(selectedCategoryId);

        // Get a random user ID from the fetched user IDs, or use current user ID as fallback
        const randomOwnerUserId =
          userIds.length > 0
            ? userIds[Math.floor(Math.random() * userIds.length)]
            : user?.id;

        const { address, city, state, zip_code, country } =
          generateCityAddress(selectedCity);

        businessesToCreate.push({
          name: companyName,
          address,
          description: faker.helpers.arrayElement(italianData.catchPhrases),
          latitude: latitude,
          longitude: longitude,
          photos: [faker.image.url()],
          owner_id: randomOwnerUserId,
          category_id: selectedCategoryId,
          email: generateItalianEmail(companyName),
          phone: generateItalianPhone(),
          website: generateItalianWebsite(companyName),
          fake: true,
          city,
          state,
          zip_code,
          country,
        });
      }

      // Update progress to indicate database insertion
      setBusinessProgress(90);
      setBusinessProgressMessage("Inserimento attività nel database...");

      // Insert businesses into the database
      const { error: businessError } = await supabase
        .from("businesses")
        .insert(businessesToCreate)
        .select();

      if (businessError) {
        toast.error(
          `Errore nella creazione delle attività: ${businessError.message}`
        );
        console.error(businessError);
        setShowBusinessProgress(false);
        setIsLoading(false);
        return;
      }

      // Complete progress
      setBusinessProgress(100);
      setBusinessProgressMessage("Creazione attività completata!");

      toast.success(`${numberOfBusinesses} attività generate con successo!`);

      // Refresh business list
      await fetchBusinesses();

      // Hide progress bar after a delay
      setTimeout(() => {
        setShowBusinessProgress(false);
        setIsLoading(false);
      }, 1500);
    } catch (error: any) {
      toast.error(`Si è verificato un errore: ${error.message}`);
      console.error(error);
      setShowBusinessProgress(false);
      setIsLoading(false);
    }
  };

  // Funzione per generare un numero intero casuale tra 1 e max
  function getRandomInt(max: number) {
    return Math.floor(Math.random() * max) + 1; // +1 per assicurarsi che sia almeno 1
  }

  // Delete fake deals
  const deleteFakeDeals = async () => {
    // Show confirmation dialog
    const isConfirmed = window.confirm(
      "Danger Zone: Sei sicuro di voler eliminare tutte le offerte generate? Questa azione non può essere annullata."
    );

    if (!isConfirmed) {
      return; // User cancelled the operation
    }
    try {
      setIsDeletingDeals(true);
      setShowDeleteProgress(true);
      setDeleteProgress(0);
      setDeleteProgressMessage("Eliminazione offerte in corso...");

      // First, get all fake deals
      const { data: fakeDeals, error: fetchError } = await supabase
        .from("deals")
        .select("id")
        .eq("fake", true);

      if (fetchError) {
        toast.error(`Errore nel recupero delle offerte: ${fetchError.message}`);
        return;
      }

      if (!fakeDeals || fakeDeals.length === 0) {
        toast.info("Nessuna offerta generata da eliminare");
        setIsDeletingDeals(false);
        setShowDeleteProgress(false);
        return;
      }

      // Delete deals in batches to avoid timeouts
      const batchSize = 50;
      const totalDeals = fakeDeals.length;
      let deletedDeals = 0;

      for (let i = 0; i < totalDeals; i += batchSize) {
        const batch = fakeDeals.slice(i, i + batchSize);
        const dealIds = batch.map((deal) => deal.id);

        // Delete the deals
        const { error: deleteError } = await supabase
          .from("deals")
          .delete()
          .in("id", dealIds);

        if (deleteError) {
          toast.error(
            `Errore nell'eliminazione delle offerte: ${deleteError.message}`
          );
          break;
        }

        deletedDeals += batch.length;
        const progress = Math.floor((deletedDeals / totalDeals) * 100);
        setDeleteProgress(progress);
        setDeleteProgressMessage(
          `Eliminazione offerte in corso... ${deletedDeals}/${totalDeals}`
        );
      }

      toast.success(`${deletedDeals} offerte eliminate con successo`);
    } catch (error: any) {
      console.error("Error deleting fake deals:", error);
      toast.error(
        `Si è verificato un errore durante l'eliminazione delle offerte: ${error.message}`
      );
    } finally {
      setIsDeletingDeals(false);
      setShowDeleteProgress(false);
    }
  };

  // Delete fake businesses
  const deleteFakeBusinesses = async () => {
    // Show confirmation dialog
    const isConfirmed = window.confirm(
      "Danger Zone: Sei sicuro di voler eliminare tutte le attività generate? Questa azione eliminerà anche tutte le offerte e prenotazioni associate. Questa azione non può essere annullata."
    );

    if (!isConfirmed) {
      return; // User cancelled the operation
    }
    try {
      setIsDeletingBusinesses(true);
      setShowDeleteProgress(true);
      setDeleteProgress(0);
      setDeleteProgressMessage("Eliminazione attività in corso...");

      // First, get all fake businesses
      const { data: fakeBusinesses, error: fetchError } = await supabase
        .from("businesses")
        .select("id")
        .eq("fake", true);

      if (fetchError) {
        toast.error(
          `Errore nel recupero delle attività: ${fetchError.message}`
        );
        return;
      }

      if (!fakeBusinesses || fakeBusinesses.length === 0) {
        toast.info("Nessuna attività generata da eliminare");
        setIsDeletingBusinesses(false);
        setShowDeleteProgress(false);
        return;
      }

      // Delete businesses in batches to avoid timeouts
      const batchSize = 10; // Smaller batch size since we need to delete related data
      const totalBusinesses = fakeBusinesses.length;
      let deletedBusinesses = 0;

      for (let i = 0; i < totalBusinesses; i += batchSize) {
        const batch = fakeBusinesses.slice(i, i + batchSize);
        const businessIds = batch.map((business) => business.id);

        // First delete related bookings and time_slot_bookings
        setDeleteProgressMessage(
          `Eliminazione prenotazioni correlate... ${deletedBusinesses}/${totalBusinesses}`
        );

        // Get deals for these businesses to delete related bookings
        const { data: businessDeals, error: dealsError } = await supabase
          .from("deals")
          .select("id")
          .in("business_id", businessIds);

        if (!dealsError && businessDeals && businessDeals.length > 0) {
          const dealIds = businessDeals.map((deal) => deal.id);

          // Get bookings for these deals
          const { data: dealBookings, error: bookingsError } = await supabase
            .from("bookings")
            .select("id")
            .in("deal_id", dealIds);

          if (!bookingsError && dealBookings && dealBookings.length > 0) {
            const bookingIds = dealBookings.map((booking) => booking.id);

            // Delete time_slot_bookings first
            await supabase
              .from("time_slot_bookings")
              .delete()
              .in("booking_id", bookingIds);

            // Delete bookings
            await supabase.from("bookings").delete().in("deal_id", dealIds);
          }

          // Delete deals
          setDeleteProgressMessage(
            `Eliminazione offerte correlate... ${deletedBusinesses}/${totalBusinesses}`
          );
          await supabase.from("deals").delete().in("business_id", businessIds);
        }

        // Finally delete the businesses
        setDeleteProgressMessage(
          `Eliminazione attività... ${deletedBusinesses}/${totalBusinesses}`
        );
        const { error: deleteError } = await supabase
          .from("businesses")
          .delete()
          .in("id", businessIds);

        if (deleteError) {
          toast.error(
            `Errore nell'eliminazione delle attività: ${deleteError.message}`
          );
          break;
        }

        deletedBusinesses += batch.length;
        const progress = Math.floor(
          (deletedBusinesses / totalBusinesses) * 100
        );
        setDeleteProgress(progress);
        setDeleteProgressMessage(
          `Eliminazione attività in corso... ${deletedBusinesses}/${totalBusinesses}`
        );
      }

      toast.success(`${deletedBusinesses} attività eliminate con successo`);

      // Refresh business list
      await fetchBusinesses();
    } catch (error: any) {
      console.error("Error deleting fake businesses:", error);
      toast.error(
        `Si è verificato un errore durante l'eliminazione delle attività: ${error.message}`
      );
    } finally {
      setIsDeletingBusinesses(false);
      setShowDeleteProgress(false);
    }
  };

  // Generate random deals for the selected businesses
  const generateDeals = async () => {
    if (selectedBusinesses.length === 0) {
      toast.error("Seleziona almeno un'attività");
      return;
    }

    setIsLoading(true);
    setProgress(0);
    setShowProgress(true);
    setProgressMessage("Inizializzazione...");

    try {
      let totalDealsCreated = 0;

      // Filtra le attività per città se è selezionata una città
      let businessesToProcess = [...selectedBusinesses];

      // Ottiene tutte le informazioni delle attività selezionate, necessarie per i filtri
      const { data: businessesData, error: businessesError } = await supabase
        .from("businesses")
        .select("*, categories(name)")
        .in("id", businessesToProcess);

      if (businessesError) {
        toast.error("Errore nel recupero dei dati delle attività");
        setIsLoading(false);
        setShowProgress(false);
        return;
      }
      // Aggiorna la lista finale delle attività da processare
      businessesToProcess = filteredBusinesses.map((b) => (b as any).id);

      if (businessesToProcess.length === 0) {
        toast.warning("Nessuna attività trovata con i filtri selezionati.");
        setIsLoading(false);
        setShowProgress(false);
        return;
      }

      setProgressMessage(
        //  `Elaborate ${businessesToProcess.length} attività${filterDescription}...`
        `Elaborate ${businessesToProcess.length} ...`
      );

      // Genera offerte per ciascun business selezionato
      for (let index = 0; index < businessesToProcess.length; index++) {
        const businessId = businessesToProcess[index];

        // Aggiorna il progresso
        const currentProgress = Math.round(
          (index / businessesToProcess.length) * 100
        );
        setProgress(currentProgress);
        setProgressMessage(
          `Elaborazione attività ${index + 1} di ${
            businessesToProcess.length
          }...`
        );

        // Otteniamo la categoria dell'attività selezionata
        const { data: businessData, error: businessError } = await supabase
          .from("businesses")
          .select("category_id, name")
          .eq("id", businessId)
          .single();

        if (businessError || !businessData) {
          toast.error(
            `Errore nel recuperare i dati dell'attività: ${businessId}`
          );
          continue;
        }

        setProgressMessage(
          `Generazione offerte per ${(businessData as any).name}...`
        );

        // Determiniamo il tipo di categoria per la generazione dei prodotti
        const categoryType = await mapCategoryToNameType(
          (businessData as any).category_id
        );
        const productList =
          italianData.productNames[
            categoryType as keyof typeof italianData.productNames
          ] || italianData.productNames.default;

        // Get category-specific pricing range
        const categoryPricing =
          italianData.categoryPricing[
            categoryType as keyof typeof italianData.categoryPricing
          ] || italianData.categoryPricing.default;

        // Create an array of random deal data
        const numDealsToCreate = getRandomInt(numberOfDeals);
        const dealsToCreate = Array.from({ length: numDealsToCreate }, () => {
          // Use category-specific pricing instead of fixed range
          const originalPrice = parseFloat(
            faker.commerce.price({
              min: categoryPricing.min,
              max: categoryPricing.max,
            })
          );
          const discountPercentage = getRandomInt(50) + 10;
          const discountedPrice =
            originalPrice * (1 - discountPercentage / 100);

          // Use the selected date range from "Periodo storico delle offerte"
          const dealRangeStart = new Date(dealStartDate);
          const dealRangeEnd = new Date(dealEndDate);

          // Add random +/- days based on user settings
          if (randomStartDays !== 0) {
            const randomDaysStartOffset =
              Math.floor(Math.random() * (randomStartDays * 2 + 1)) -
              randomStartDays; // -randomStartDays to +randomStartDays
            dealRangeStart.setDate(
              dealRangeStart.getDate() + randomDaysStartOffset
            );
          }

          if (randomEndDays !== 0) {
            const randomDaysEndOffset =
              Math.floor(Math.random() * (randomEndDays * 2 + 1)) -
              randomEndDays; // -randomEndDays to +randomEndDays
            dealRangeEnd.setDate(dealRangeEnd.getDate() + randomDaysEndOffset);
          }

          // Calculate the day range between start and end dates
          const dayRange = Math.floor(
            (dealRangeEnd.getTime() - dealRangeStart.getTime()) /
              (1000 * 60 * 60 * 24)
          );

          // Generate random start date within the selected range
          const randomStartOffset = Math.floor(
            Math.random() * Math.max(1, dayRange - 7)
          ); // Leave at least 7 days for end date
          const startDate = new Date(dealRangeStart);
          startDate.setDate(dealRangeStart.getDate() + randomStartOffset);

          // Generate random end date between start date + 7 days and the end of the range
          const minEndDate = new Date(startDate);
          minEndDate.setDate(startDate.getDate() + 7); // Minimum 7 days validity

          const maxEndDate = new Date(
            Math.min(
              dealRangeEnd.getTime(),
              minEndDate.getTime() + 30 * 24 * 60 * 60 * 1000
            )
          ); // Max 30 days after start or range end

          // Calculate remaining days between min end date and max end date
          const remainingDays = Math.floor(
            (maxEndDate.getTime() - minEndDate.getTime()) /
              (1000 * 60 * 60 * 24)
          );

          // Set end date randomly between min and max
          const endDate = new Date(minEndDate);
          if (remainingDays > 0) {
            endDate.setDate(minEndDate.getDate() + getRandomInt(remainingDays));
          }

          return {
            id: uuidv4(),
            title: faker.helpers.arrayElement(productList),
            description: faker.helpers.arrayElement(italianData.catchPhrases),
            original_price: originalPrice.toString(),
            discount_percentage: discountPercentage.toString(),
            discounted_price: discountedPrice.toFixed(2),
            start_date: startDate.toISOString(),
            end_date: endDate.toISOString(),
            time_slots: generateRandomSchedule(Object.values(daysNames)),
            business_id: businessId,
            status: "published" as const,
            images: [faker.image.url()],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            fake: true,
          };
        });

        setProgressMessage(
          `Salvataggio offerte per ${(businessData as any).name}...`
        );

        // Insert deals into the database
        const { error } = await supabase
          .from("deals")
          .insert(dealsToCreate as any);

        if (error) {
          toast.error(
            `Errore nella creazione delle offerte per ${
              (businessData as any).name
            }: ${error.message}`
          );
          console.error(error);
          continue;
        }

        totalDealsCreated += numberOfDeals;
      }

      // Completamento
      setProgress(100);
      setProgressMessage("Completato!");

      if (totalDealsCreated > 0) {
        toast.success(
          `${totalDealsCreated} offerte generate con successo per ${selectedBusinesses.length} attività!`
        );
      }

      // Nascondi la barra di progresso dopo un breve ritardo
      setTimeout(() => {
        setShowProgress(false);
        setIsLoading(false);
      }, 1500);
    } catch (error: any) {
      toast.error(`Si è verificato un errore: ${error.message}`);
      console.error(error);
      setShowProgress(false);
      setIsLoading(false);
    }
  };

  // Generate random users
  const generateUsers = async () => {
    if (numberOfUsers <= 0) {
      toast.error("Inserisci un numero valido di utenti");
      return;
    }

    setIsLoadingUsers(true);
    setUserProgress(0);
    setShowUserProgress(true);
    setUserProgressMessage("Inizializzazione creazione utenti...");

    try {
      let createdUsers = 0;
      let attempts = 0;

      let userIndex = 0;
      // Continua finché non abbiamo creato il numero richiesto di utenti
      // o finché non raggiungiamo il numero massimo di tentativi
      while (createdUsers < numberOfUsers) {
        attempts++;

        // Aggiorna la barra di progresso
        const progressPercentage = Math.min(
          Math.round((createdUsers / numberOfUsers) * 100),
          100
        );
        setUserProgress(progressPercentage);
        setUserProgressMessage(
          `Creazione utente ${
            createdUsers + 1
          } di ${numberOfUsers}... (Tentativo ${attempts})`
        );

        // Generate random user data
        const firstName = faker.helpers.arrayElement(italianData.firstNames);
        const lastName = faker.helpers.arrayElement(italianData.lastNames);
        const email = `test${startingUserIndex + createdUsers}@antonioripa.com`;
        // const password = `Password${Math.floor(Math.random() * 10000)}!`;
        const password = "password.1234";
        const phoneNumber = faker.phone.number("+39 ### ### ####");
        const displayName = `${firstName} ${lastName}`;

        try {
          await new Promise((resolve) => setTimeout(resolve, 100));
          // Create user in Supabase Auth
          const { data, error } = await supabase.auth.signUp({
            email,
            password,
            options: {
              data: {
                first_name: firstName,
                last_name: lastName,
                display_name: displayName,
                phone_number: phoneNumber || null,
                email: email,
              },
            },
          });

          if (error) {
            console.error(
              `Tentativo ${attempts}: Errore nella creazione dell'utente ${email}:`,
              error
            );
            userIndex++;
          } else if (data.user) {
            createdUsers++;
            console.log(
              `Utente ${createdUsers}/${numberOfUsers} creato: ${email}`
            );
            userIndex++;
          }
        } catch (userError) {
          console.error(
            `Tentativo ${attempts}: Errore nella creazione dell'utente:`,
            userError
          );
        }

        // Piccola pausa per evitare di sovraccaricare l'API
        if (attempts % 10 === 0) {
          await new Promise((resolve) => setTimeout(resolve, 10000));
        }
      }

      // Completamento
      setUserProgress(100);
      setUserProgressMessage("Creazione utenti completata!");

      if (createdUsers > 0) {
        toast.success(`${createdUsers} utenti creati con successo!`);

        // Se non siamo riusciti a creare tutti gli utenti richiesti
        if (createdUsers < numberOfUsers) {
          toast.warning(
            `Solo ${createdUsers} dei ${numberOfUsers} utenti richiesti sono stati creati. Alcuni potrebbero già esistere.`
          );
        }
      } else {
        toast.error(
          "Nessun utente creato. Potrebbe esserci un problema di permessi o gli utenti potrebbero già esistere."
        );
      }

      // Nascondi la barra di progresso dopo un breve ritardo
      setTimeout(() => {
        setShowUserProgress(false);
        setIsLoadingUsers(false);
      }, 1500);
    } catch (error: any) {
      toast.error(`Si è verificato un errore: ${error.message}`);
      console.error(error);
      setShowUserProgress(false);
      setIsLoadingUsers(false);
    }
  };

  // Fetch all user IDs from user_details
  const fetchUserIds = async () => {
    try {
      // Use the any type to bypass TypeScript's type checking for Supabase query
      const { data, error } = (await supabase
        .from("user_details")
        .select()) as { data: any[]; error: any };

      if (error) {
        console.error("Error fetching user IDs:", error);
        return;
      }

      if (data && Array.isArray(data)) {
        // Extract IDs from the data
        const extractedIds = data
          .filter((user) => user && typeof user === "object" && "id" in user)
          .map((user) => String(user.id));

        setUserIds(extractedIds);
        console.log(
          `Fetched ${extractedIds.length} user IDs for random assignment`
        );
      }
    } catch (error) {
      console.error("Error in fetchUserIds:", error);
    }
  };

  // Add filter handlers (after the other handler functions)
  const handleBookingCityChange = (city: string) => {
    setSelectedBookingCity(city);
    filterBookingBusinessesByCity(city);
    //  setSelectedBookingBusinesses([]); // Reset selected businesses when changing city
    //  setSelectedBookingCategory(""); // Reset category filter when changing city
  };

  // Delete fake bookings
  const deleteFakeBookings = async () => {
    // Show confirmation dialog
    const isConfirmed = window.confirm(
      "Danger Zone: Sei sicuro di voler eliminare tutte le prenotazioni generate? Questa azione non può essere annullata."
    );

    if (!isConfirmed) {
      return; // User cancelled the operation
    }
    try {
      setIsDeletingBookings(true);
      setShowDeleteProgress(true);
      setDeleteProgress(0);
      setDeleteProgressMessage("Eliminazione prenotazioni in corso...");

      // First, get all fake bookings
      const { data: fakeBookings, error: fetchError } = await supabase
        .from("bookings")
        .select("id")
        .eq("fake", true);

      if (fetchError) {
        toast.error(
          `Errore nel recupero delle prenotazioni: ${fetchError.message}`
        );
        return;
      }

      if (!fakeBookings || fakeBookings.length === 0) {
        toast.info("Nessuna prenotazione generata da eliminare");
        setIsDeletingBookings(false);
        setShowDeleteProgress(false);
        return;
      }

      // Delete bookings in batches to avoid timeouts
      const batchSize = 50;
      const totalBookings = fakeBookings.length;
      let deletedBookings = 0;

      for (let i = 0; i < totalBookings; i += batchSize) {
        const batch = fakeBookings.slice(i, i + batchSize);
        const bookingIds = batch.map((booking) => booking.id);

        // First delete related time_slot_bookings
        const { error: timeSlotError } = await supabase
          .from("time_slot_bookings")
          .delete()
          .in("booking_id", bookingIds);

        if (timeSlotError) {
          toast.error(
            `Errore nell'eliminazione dei time slots: ${timeSlotError.message}`
          );
          break;
        }

        // Then delete the bookings
        const { error: deleteError } = await supabase
          .from("bookings")
          .delete()
          .in("id", bookingIds);

        if (deleteError) {
          toast.error(
            `Errore nell'eliminazione delle prenotazioni: ${deleteError.message}`
          );
          break;
        }

        deletedBookings += batch.length;
        const progress = Math.floor((deletedBookings / totalBookings) * 100);
        setDeleteProgress(progress);
        setDeleteProgressMessage(
          `Eliminazione prenotazioni in corso... ${deletedBookings}/${totalBookings}`
        );
      }

      toast.success(`${deletedBookings} prenotazioni eliminate con successo`);
    } catch (error: any) {
      console.error("Error deleting fake bookings:", error);
      toast.error(
        `Si è verificato un errore durante l'eliminazione delle prenotazioni: ${error.message}`
      );
    } finally {
      setIsDeletingBookings(false);
      setShowDeleteProgress(false);
    }
  };

  // Add the new function for generating random bookings - place this after other generation functions
  const generateRandomBookings = async () => {
    try {
      setShowBookingProgress(true);
      setBookingProgress(0);
      setBookingProgressMessage(
        "Preparazione per la generazione delle prenotazioni..."
      );

      // Fetch all deals first
      let dealsQuery = supabase.from("deals").select("*, businesses(*)");

      // If we have selected businesses, filter the deals by those businesses
      if (selectedBookingBusinesses.length > 0) {
        dealsQuery = dealsQuery.in("business_id", selectedBookingBusinesses);
      }

      const { data: allDeals, error: dealsError } = await dealsQuery;

      // Check for errors
      if (dealsError) throw dealsError;
      if (!allDeals || allDeals.length === 0) {
        setBookingProgressMessage(
          "Nessun deal trovato per creare prenotazioni"
        );
        return;
      }

      // Start with all deals and filter them
      let filteredDeals = [...allDeals];
      let filterDescription = "";

      // Filter by city if selected
      if (selectedBookingCity) {
        setBookingProgressMessage(
          `Filtro offerte per città: ${selectedBookingCity}...`
        );
        filterDescription += ` in ${selectedBookingCity}`;

        filteredDeals = filteredDeals.filter((deal) => {
          const business = deal.businesses;
          if (!business || !business.address) return false;

          // Parse address to extract city - same approach as in deal generator
          const addressParts = business.address.split(",");
          if (addressParts.length < 3) return false;

          // The part containing the city should be the second-to-last (before ", Italia")
          const cityProvincePart = addressParts[addressParts.length - 2].trim();

          // Extract city from format "CAP Città Provincia"
          const cityMatch = cityProvincePart.match(/\d{5}\s+([^\s]+)/);
          if (!cityMatch || !cityMatch[1]) return false;

          // Compare extracted city with selected city
          return cityMatch[1] === selectedBookingCity;
        });

        if (filteredDeals.length === 0) {
          setBookingProgressMessage(
            `Nessuna offerta trovata in ${selectedBookingCity}.`
          );
          return;
        }
      }

      // Filter by category if selected
      if (selectedBookingCategory) {
        setBookingProgressMessage(
          `Filtro offerte per categoria: ${selectedBookingCategory}...`
        );
        filterDescription += ` nella categoria ${selectedBookingCategory}`;

        // Get all categories
        const { data: categoriesData, error: categoriesError } = await supabase
          .from("categories")
          .select("*");

        if (categoriesError) {
          console.error("Errore categorie:", categoriesError);
          setBookingProgressMessage("Errore nel recupero delle categorie");
          return;
        }

        // Convert for safety
        const categories = categoriesData || [];

        // Find the selected category
        const categoryObj = categories.find(
          (c) => c.name === selectedBookingCategory
        );

        if (!categoryObj) {
          setBookingProgressMessage(
            `Categoria ${selectedBookingCategory} non trovata.`
          );
          return;
        }

        // Filter deals by category_id
        filteredDeals = filteredDeals.filter((deal) => {
          const business = deal.businesses;
          return business && business.category_id === categoryObj.id;
        });

        if (filteredDeals.length === 0) {
          setBookingProgressMessage(
            `Nessuna offerta trovata nella categoria ${selectedBookingCategory}${
              selectedBookingCity ? ` in ${selectedBookingCity}` : ""
            }.`
          );
          return;
        }
      }

      // Add business selection filter description
      if (selectedBookingBusinesses.length > 0) {
        if (selectedBookingBusinesses.length === 1) {
          const businessName =
            filteredBookingBusinesses.find(
              (b) => b.id === selectedBookingBusinesses[0]
            )?.name || "selezionata";
          filterDescription += ` per l'attività ${businessName}`;
        } else {
          filterDescription += ` per ${selectedBookingBusinesses.length} attività selezionate`;
        }

        // Further filter deals by the selected businesses
        filteredDeals = filteredDeals.filter((deal) =>
          selectedBookingBusinesses.includes(deal.business_id)
        );
      }

      // If we have no deals after filtering, show an error
      if (filteredDeals.length === 0) {
        setBookingProgressMessage(
          `Nessuna offerta trovata${filterDescription}.`
        );
        return;
      }

      setBookingProgressMessage(
        `Trovate ${filteredDeals.length} offerte${filterDescription}. Generazione di ${numberOfBookings} prenotazioni casuali...`
      );

      // Fetch all users who will make bookings
      const { data: users, error: usersError } = await supabase
        .from("user_details")
        .select("*");

      if (usersError) throw usersError;
      if (!users || users.length === 0) {
        setBookingProgressMessage(
          "Nessun utente cliente trovato per creare prenotazioni"
        );
        return;
      }

      const totalBookings = numberOfBookings;
      let createdBookings = 0;

      for (let i = 0; i < totalBookings; i++) {
        // Select a random deal from filtered deals
        const randomDeal =
          filteredDeals[Math.floor(Math.random() * filteredDeals.length)];

        // Get the business owner of this deal
        const { data: business, error: businessError } = await supabase
          .from("businesses")
          .select("owner_id")
          .eq("id", randomDeal.business_id)
          .single();

        if (businessError) {
          console.error("Error fetching business:", businessError);
          continue;
        }

        // Filter users to exclude the business owner
        const eligibleUsers = users.filter(
          (user) => user.id !== business.owner_id
        );

        if (eligibleUsers.length === 0) {
          console.warn("No eligible users found for this deal");
          continue;
        }

        // Select a random eligible user
        const randomUser =
          eligibleUsers[Math.floor(Math.random() * eligibleUsers.length)];

        const { bookingDate, bookingTime, timeSlot } = await getBookingTimeSlot(
          randomDeal.id
        );

        if (!timeSlot) {
          console.warn("Error generating booking time slot");
          continue;
        }

        if (!bookingDate || !bookingTime) {
          console.error("Error generating booking date/time");
          continue;
        }

        // Random status with probabilities:
        // 60% confirmed, 15% completed, 15% pending, 10% cancelled
        const statusRandom = Math.random();
        let status;
        if (statusRandom < 0.6) {
          status = "confirmed";
        } else if (statusRandom < 0.75) {
          status = "completed";
        } else if (statusRandom < 0.9) {
          status = "pending";
        } else {
          status = "cancelled";
        }

        // Prepara i dati del QR code
        const qrData = {
          bookingId: crypto.randomUUID(),
          dealTitle: randomDeal.title,
          businessName: randomDeal.businesses.name,
          date: bookingDate,
          time: bookingTime,
          status: status,
        };

        // Insert the booking
        const { data: booking, error: insertError } = (await supabase
          .from("bookings")
          .insert({
            user_id: randomUser.id,
            deal_id: randomDeal.id,
            booking_date: bookingDate, // Formatted as YYYY-MM-DD
            booking_time: bookingTime,
            status: status,
            cancellation_note: status === "cancelled" ? "Autocancellato" : null,
            created_at: new Date().toISOString(),
            original_price: randomDeal.original_price,
            discount_percentage: randomDeal.discount_percentage,
            discounted_price: randomDeal.discounted_price,
            qr_data: qrData,
            fake: true,
          })
          .select()
          .single()) as { data: Booking | null; error: any };

        if (insertError) {
          console.error("Error inserting booking:", insertError);
          continue;
        }
        if (!booking) throw new Error("Booking not found");

        const getDayOfWeek = (dateString: string) => {
          const date = new Date(dateString);
          return date.getDay() === 0 ? 7 : date.getDay(); // Convert Sunday from 0 to 7
        };

        const { data: timeSlotData, error: timeSlotError } = await supabase
          .from("time_slot_bookings")
          .insert({
            deal_id: randomDeal.id,
            booking_id: booking.id,
            booking_date: bookingDate,
            day_of_week: getDayOfWeek(bookingDate),
            start_time: timeSlot.start_time,
            end_time: timeSlot.end_time,
            booked_seats: 1,
            fake: true,
          });

        if (timeSlotError) throw timeSlotError;
        //Update the qr_data with the booking id
        const qrDataId = booking.qr_data as {
          bookingId: string;
          dealTitle: string;
          businessName: string;
          date: string;
          time: string;
          status: string;
        };
        qrDataId.bookingId = booking.id;

        const { error: qrDataError } = await supabase
          .from("bookings")
          .update({
            qr_data: qrDataId,
          })
          .eq("id", booking.id);

        if (qrDataError) throw qrDataError;

        createdBookings++;
        setBookingProgress(Math.floor((createdBookings / totalBookings) * 100));
        setBookingProgressMessage(
          `Generazione prenotazioni in corso... ${createdBookings}/${totalBookings}`
        );
      }

      setBookingProgressMessage(
        `Generazione completata! ${createdBookings} prenotazioni create.`
      );
    } catch (error) {
      console.error("Error generating bookings:", error);
      setBookingProgressMessage(
        `Errore nella generazione delle prenotazioni: ${error.message}`
      );
    } finally {
      setBookingProgress(100);
      setTimeout(() => {
        setShowBookingProgress(false);
      }, 3000);
    }

    async function getBookingTimeSlot(dealId: string) {
      // // Generate a random date for the booking (between now and 30 days in the future)
      //   const bookingDate = new Date();
      //   bookingDate.setDate(bookingDate.getDate() + Math.floor(Math.random() * 30));

      //   // Generate a random time (9:00 to 20:00)
      //   const hour = 9 + Math.floor(Math.random() * 11);
      //   const minute = Math.floor(Math.random() * 4) * 15; // 0, 15, 30, or 45

      //   // Format time as HH:MM
      //   const bookingTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;

      //   return { bookingDate, bookingTime };

      const { data: deal, error: dealError } = await supabase
        .from("deals")
        .select("*")
        .eq("id", dealId)
        .single();

      if (dealError) throw dealError;

      if (!deal) throw new Error("Deal not found");

      try {
        // Parse the time_slots JSON if it's a string
        const timeSlotData =
          typeof deal.time_slots === "string"
            ? JSON.parse(deal.time_slots)
            : deal.time_slots;

        // Parse start and end dates
        let startDate = new Date(deal.start_date);
        let endDate = new Date(deal.end_date);

        // Ensure we have valid dates, otherwise use fallback
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          console.warn("Invalid deal dates, using fallback date range");
          const today = new Date();
          startDate = today;
          endDate = new Date(today);
          endDate.setDate(today.getDate() + 30);
        }

        // Calculate the date range in days (not used anymore but kept for reference)
        // const dayRange = Math.floor(
        //   (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
        // );

        // Try up to 15 times to find a valid date/time slot
        for (let attempt = 0; attempt < 15; attempt++) {
          // Generate a random date within the selected booking date range
          const bookingRangeStart = new Date(bookingStartDate);
          const bookingRangeEnd = new Date(bookingEndDate);

          // Calculate the day range between start and end dates of the booking period
          const bookingDayRange = Math.floor(
            (bookingRangeEnd.getTime() - bookingRangeStart.getTime()) /
              (1000 * 60 * 60 * 24)
          );

          // Generate a random date within the booking date range
          const randomDayOffset = Math.floor(
            Math.random() * (bookingDayRange + 1)
          );
          const bookingDate = new Date(bookingRangeStart);
          bookingDate.setDate(bookingRangeStart.getDate() + randomDayOffset);

          // Check if the generated date is within the deal's validity period
          if (bookingDate < startDate || bookingDate > endDate) {
            console.log(
              `Generated date ${
                bookingDate.toISOString().split("T")[0]
              } is outside deal validity period ${
                startDate.toISOString().split("T")[0]
              } - ${endDate.toISOString().split("T")[0]}`
            );
            continue; // Try again if the date is outside the deal's validity period
          }

          // Get the day of week (1-7, where 1 is Monday)
          const dayOfWeek =
            bookingDate.getDay() === 0 ? 7 : bookingDate.getDay();

          // Find the schedule for this day of week
          const daySchedule = timeSlotData?.schedule?.find(
            (day: any) => day.day === dayOfWeek
          );

          // Check if this day has any time slots
          if (
            daySchedule &&
            daySchedule.time_slots &&
            daySchedule.time_slots.length > 0
          ) {
            // Select a random time slot
            const randomSlotIndex = Math.floor(
              Math.random() * daySchedule.time_slots.length
            );
            const selectedSlot = daySchedule.time_slots[randomSlotIndex];

            // Format date as YYYY-MM-DD
            const formattedDate = bookingDate.toISOString().split("T")[0];

            // Return the selected date and time
            return {
              bookingDate: formattedDate,
              bookingTime: selectedSlot.start_time,
              timeSlot: selectedSlot,
            };
          }

          // If we get here, the selected day doesn't have time slots, try again
        }

        // If we couldn't find a valid slot after multiple attempts, return a fallback
        console.warn(
          `Could not find valid time slot for deal ${deal.title} - ${deal.id}  using fallback time`
        );
        return {
          bookingDate: new Date().toISOString().split("T")[0],
          bookingTime: "12:00",
          timeSlot: null,
        };
      } catch (error) {
        console.error("Error getting booking time slot:", error);
        // Return a fallback in case of error
        return {
          bookingDate: new Date().toISOString().split("T")[0],
          bookingTime: "12:00",
          timeSlot: null,
        };
      }
    }
  };

  return (
    <MainLayout>
      <main className="p-4 pb-24 space-y-8">
        {/* Users Generator Section */}

        <section className="bg-white rounded-xl p-6 shadow-sm space-y-4">
          <div
            className="flex justify-between items-center cursor-pointer"
            onClick={() => toggleSection("users")}
          >
            <h2 className="text-xl font-semibold text-gray-800">
              Genera Utenti Casuali
            </h2>
            <span className="text-gray-600">
              {expandedSections.users ? "▼" : "►"}
            </span>
          </div>

          {expandedSections.users && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Numero di Utenti da Generare
                </label>
                <input
                  type="number"
                  min="1"
                  max="20"
                  value={numberOfUsers}
                  onChange={(e) => setNumberOfUsers(parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Indice Iniziale
                </label>
                <input
                  type="number"
                  min="0"
                  value={startingUserIndex}
                  onChange={(e) =>
                    setStartingUserIndex(parseInt(e.target.value))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Il primo utente avrà email: test{startingUserIndex}
                  @antonioripa.com
                </p>
              </div>

              <div className="text-sm text-gray-500 mt-2">
                <p>Gli utenti saranno creati con:</p>
                <ul className="list-disc pl-5 mt-1">
                  <li>Nomi e cognomi italiani casuali</li>
                  <li>Email nel formato nome.cognome###@example.com</li>
                  <li>Password sicure generate automaticamente</li>
                  <li>Numeri di telefono italiani</li>
                </ul>
              </div>

              {showUserProgress && (
                <div className="mt-4">
                  <div className="flex justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700">
                      {userProgressMessage}
                    </span>
                    <span className="text-sm font-medium text-gray-700">
                      {userProgress}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="bg-brand-primary h-2.5 rounded-full transition-all duration-300 ease-in-out"
                      style={{ width: `${userProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              <button
                onClick={generateUsers}
                disabled={isLoadingUsers}
                className={`w-full py-2 rounded-lg font-medium transition-colors ${
                  isLoadingUsers
                    ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                    : "bg-brand-primary text-white hover:bg-brand-primary-dark"
                }`}
              >
                {isLoadingUsers
                  ? "Creazione utenti in corso..."
                  : "Genera Utenti"}
              </button>
            </>
          )}
        </section>

        {/* Business Generator Section */}
        <section className="bg-white rounded-xl p-6 shadow-sm space-y-4">
          <div
            className="flex justify-between items-center cursor-pointer"
            onClick={() => toggleSection("businesses")}
          >
            <h2 className="text-xl font-semibold text-gray-800">
              Genera Attività Casuali
            </h2>
            <span className="text-gray-600">
              {expandedSections.businesses ? "▼" : "►"}
            </span>
          </div>

          {expandedSections.businesses ? (
            <>
              {/* Business Counters */}
              <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {totalBusinessesCount}
                  </div>
                  <div className="text-sm text-gray-600">Attività Totali</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {fakeBusinessesCount}
                  </div>
                  <div className="text-sm text-gray-600">Attività Generate</div>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Città
                </label>
                <select
                  value={selectedCity}
                  onChange={(e) => setSelectedCity(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
                >
                  {cities.map((city) => (
                    <option key={city.name} value={city.name}>
                      {city.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Categoria
                </label>
                <select
                  value={selectedCategoryId}
                  onChange={(e) => setSelectedCategoryId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
                >
                  <option value="">Seleziona una categoria</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Numero di Attività (1-20)
                </label>
                <input
                  type="number"
                  min="1"
                  max="20"
                  value={numberOfBusinesses}
                  onChange={(e) =>
                    setNumberOfBusinesses(parseInt(e.target.value))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
                />
              </div>

              {showBusinessProgress && (
                <div className="mt-4 border border-gray-200 bg-gray-50 p-3 rounded-lg">
                  <div className="flex justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700">
                      {businessProgressMessage}
                    </span>
                    <span className="text-sm font-medium text-gray-700">
                      {businessProgress}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="bg-brand-primary h-2.5 rounded-full transition-all duration-300 ease-in-out"
                      style={{ width: `${businessProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                <button
                  onClick={generateBusinesses}
                  disabled={isLoading}
                  className={`w-full py-2 rounded-lg font-medium transition-colors ${
                    isLoading
                      ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                      : "bg-brand-primary text-white hover:bg-brand-primary-dark"
                  }`}
                >
                  {isLoading ? "Creazione in corso..." : "Genera Attività"}
                </button>

                <button
                  onClick={deleteFakeBusinesses}
                  disabled={isDeletingBusinesses}
                  className={`w-full py-2 rounded-lg font-medium transition-colors ${
                    isDeletingBusinesses
                      ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                      : "bg-red-500 text-white hover:bg-red-600"
                  }`}
                >
                  {isDeletingBusinesses
                    ? "Eliminazione in corso..."
                    : "Elimina Attività Generate (Richiede Conferma)"}
                </button>
              </div>

              {showDeleteProgress && (
                <div className="mt-4">
                  <div className="flex justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700">
                      {deleteProgressMessage}
                    </span>
                    <span className="text-sm font-medium text-gray-700">
                      {deleteProgress}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="bg-red-500 h-2.5 rounded-full transition-all duration-300 ease-in-out"
                      style={{ width: `${deleteProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </>
          ) : null}
        </section>

        {/* Deal Generator Section */}
        <section className="bg-white rounded-xl p-6 shadow-sm space-y-4">
          <div
            className="flex justify-between items-center cursor-pointer"
            onClick={() => toggleSection("deals")}
          >
            <h2 className="text-xl font-semibold text-gray-800">
              Genera Offerte Casuali
            </h2>
            <span className="text-gray-600">
              {expandedSections.deals ? "▼" : "►"}
            </span>
          </div>

          {expandedSections.deals && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Filtra Attività per Città
                </label>
                <select
                  value={selectedDealCity}
                  onChange={(e) => handleDealCityChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
                >
                  <option value="">Tutte le città</option>
                  {cities.map((city) => (
                    <option key={city.name} value={city.name}>
                      {city.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Filtra Attività per Categoria
                </label>
                <select
                  value={selectedDealCategory}
                  onChange={(e) => handleDealCategoryChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
                >
                  <option value="">Tutte le categorie</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.name}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <BusinessSelectionList
                  businesses={filteredBusinesses}
                  selectedBusinesses={selectedBusinesses}
                  onToggleBusiness={handleBusinessToggle}
                  onSelectAll={handleSelectAllBusinesses}
                  title={`Seleziona Fake Attività (${selectedBusinesses.length} selezionate)`}
                  idPrefix="deal-business"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Numero di Offerte da Generare per Attività
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={numberOfDeals}
                  onChange={(e) => setNumberOfDeals(parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
                />
              </div>

              <DateRangeSelector
                startDate={dealStartDate}
                endDate={dealEndDate}
                onStartDateChange={(date) => setDealStartDate(date)}
                onEndDateChange={(date) => setDealEndDate(date)}
                title="Periodo storico delle offerte"
                description="Seleziona un intervallo di date per generare offerte storiche"
              />

              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Aggiungi Casualità Start/End Date
                  </label>
                  <p className="text-xs text-gray-500 mb-3">
                    Aggiungi variazione casuale alle date di inizio e fine delle
                    offerte (0 = nessuna variazione)
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      Giorni casuali data inizio (±)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="30"
                      value={randomStartDays}
                      onChange={(e) =>
                        setRandomStartDays(parseInt(e.target.value) || 0)
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary text-sm"
                      placeholder="0"
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      Giorni casuali data fine (±)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="30"
                      value={randomEndDays}
                      onChange={(e) =>
                        setRandomEndDays(parseInt(e.target.value) || 0)
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary text-sm"
                      placeholder="0"
                    />
                  </div>
                </div>

                {(randomStartDays > 0 || randomEndDays > 0) && (
                  <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded">
                    <strong>Anteprima:</strong>{" "}
                    {randomStartDays > 0 &&
                      `Data inizio: ±${randomStartDays} giorni`}
                    {randomStartDays > 0 && randomEndDays > 0 && ", "}
                    {randomEndDays > 0 && `Data fine: ±${randomEndDays} giorni`}
                  </div>
                )}
              </div>

              {showProgress && (
                <div className="mt-4">
                  <div className="flex justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700">
                      {progressMessage}
                    </span>
                    <span className="text-sm font-medium text-gray-700">
                      {progress}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="bg-brand-primary h-2.5 rounded-full transition-all duration-300 ease-in-out"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                <button
                  onClick={generateDeals}
                  disabled={isLoading || selectedBusinesses.length === 0}
                  className={`w-full py-2 rounded-lg font-medium transition-colors ${
                    isLoading || selectedBusinesses.length === 0
                      ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                      : "bg-brand-primary text-white hover:bg-brand-primary-dark"
                  }`}
                >
                  {isLoading
                    ? "Generazione in corso..."
                    : `Genera Offerte per ${selectedBusinesses.length} Attività`}
                </button>

                <button
                  onClick={deleteFakeDeals}
                  disabled={isDeletingDeals}
                  className={`w-full py-2 rounded-lg font-medium transition-colors ${
                    isDeletingDeals
                      ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                      : "bg-red-500 text-white hover:bg-red-600"
                  }`}
                >
                  {isDeletingDeals
                    ? "Eliminazione in corso..."
                    : "Elimina Offerte Generate (Richiede Conferma)"}
                </button>
              </div>

              {showDeleteProgress && (
                <div className="mt-4">
                  <div className="flex justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700">
                      {deleteProgressMessage}
                    </span>
                    <span className="text-sm font-medium text-gray-700">
                      {deleteProgress}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="bg-red-500 h-2.5 rounded-full transition-all duration-300 ease-in-out"
                      style={{ width: `${deleteProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </>
          )}
        </section>

        {/* Bookings Generator Section */}
        <section className="bg-white rounded-xl p-6 shadow-sm space-y-4">
          <div
            className="flex justify-between items-center cursor-pointer"
            onClick={() => toggleSection("bookings")}
          >
            <h2 className="text-xl font-semibold text-gray-800">
              Genera Prenotazioni Casuali
            </h2>
            <span className="text-gray-600">
              {expandedSections.bookings ? "▼" : "►"}
            </span>
          </div>

          {expandedSections.bookings && (
            <>
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Filtra Offerte per Città
                </label>
                <select
                  value={selectedBookingCity}
                  onChange={(e) => handleBookingCityChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
                >
                  <option value="">Tutte le città</option>
                  {cities.map((city) => (
                    <option key={city.name} value={city.name}>
                      {city.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Filtra Offerte per Categoria
                </label>
                <select
                  value={selectedBookingCategory}
                  onChange={(e) =>
                    handleBookingBusinessCategoryChange(e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
                >
                  <option value="">Tutte le categorie</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.name}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="mt-4">
                <BusinessSelectionList
                  businesses={filteredBookingBusinesses}
                  selectedBusinesses={selectedBookingBusinesses}
                  onToggleBusiness={handleBookingBusinessToggle}
                  onSelectAll={handleSelectAllBookingBusinesses}
                  title={`Seleziona Fake Attività (${selectedBookingBusinesses.length} selezionate)`}
                  idPrefix="booking-business"
                />
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Numero di Prenotazioni da Generare
                </label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  value={numberOfBookings}
                  onChange={(e) =>
                    setNumberOfBookings(parseInt(e.target.value))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
                />
              </div>

              <DateRangeSelector
                startDate={bookingStartDate}
                endDate={bookingEndDate}
                onStartDateChange={(date) => setBookingStartDate(date)}
                onEndDateChange={(date) => setBookingEndDate(date)}
                title="Periodo Storico delle Prenotazioni"
                description="Seleziona un intervallo di date per generare prenotazioni storiche"
              />

              {showBookingProgress && (
                <div className="mt-4">
                  <div className="flex justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700">
                      {bookingProgressMessage}
                    </span>
                    <span className="text-sm font-medium text-gray-700">
                      {bookingProgress}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="bg-brand-primary h-2.5 rounded-full transition-all duration-300 ease-in-out"
                      style={{ width: `${bookingProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4">
                <button
                  onClick={generateRandomBookings}
                  disabled={
                    showBookingProgress ||
                    selectedBookingBusinesses.length === 0
                  }
                  className={`w-full py-2 rounded-lg font-medium transition-colors ${
                    showBookingProgress ||
                    selectedBookingBusinesses.length === 0
                      ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                      : "bg-brand-primary text-white hover:bg-brand-primary-dark"
                  }`}
                >
                  {showBookingProgress
                    ? "Generazione in corso..."
                    : `Genera Prenotazioni Casuali`}
                </button>

                <button
                  onClick={deleteFakeBookings}
                  disabled={isDeletingBookings}
                  className={`w-full py-2 rounded-lg font-medium transition-colors ${
                    isDeletingBookings
                      ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                      : "bg-red-500 text-white hover:bg-red-600"
                  }`}
                >
                  {isDeletingBookings
                    ? "Eliminazione in corso..."
                    : "Elimina Prenotazioni Generate (Richiede Conferma)"}
                </button>
              </div>

              {showDeleteProgress && (
                <div className="mt-4">
                  <div className="flex justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700">
                      {deleteProgressMessage}
                    </span>
                    <span className="text-sm font-medium text-gray-700">
                      {deleteProgress}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="bg-red-500 h-2.5 rounded-full transition-all duration-300 ease-in-out"
                      style={{ width: `${deleteProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </>
          )}
        </section>

        {/* Embeddings Management Section */}
        <section className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-800">
            Gestione Embeddings AI
          </h2>
          <DealEmbeddingsManager />
        </section>
      </main>
    </MainLayout>
  );
};

export default AdminDataPage;
