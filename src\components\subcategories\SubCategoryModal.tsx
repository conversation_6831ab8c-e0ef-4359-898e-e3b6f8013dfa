import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { SubCategory, Category } from "@/types/types";

interface SubCategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  subcategory?: SubCategory | null;
  onSuccess: () => void;
}

interface FormData {
  name: string;
  description: string;
  icon: string;
  category_id: string;
}

export const SubCategoryModal = ({ isOpen, onClose, subcategory, onSuccess }: SubCategoryModalProps) => {
  const { toast } = useToast();
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const { register, handleSubmit, reset, setValue, watch, formState: { errors } } = useForm<FormData>({
    defaultValues: {
      name: "",
      description: "",
      icon: "",
      category_id: ""
    }
  });

  const categoryId = watch("category_id");

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    if (subcategory) {
      setValue("name", subcategory.name);
      setValue("description", subcategory.description || "");
      setValue("icon", subcategory.icon || "");
      setValue("category_id", subcategory.category_id);
    } else {
      reset();
    }
  }, [subcategory, setValue, reset]);

  const fetchCategories = async () => {
    const { data, error } = await supabase
      .from("categories")
      .select("*")
      .order("name");

    if (error) {
      toast({
        title: "Errore",
        description: "Impossibile caricare le categorie",
        variant: "destructive",
      });
      return;
    }

    setCategories(data || []);
  };

  const onSubmit = async (data: FormData) => {
    setIsLoading(true);
    
    try {
      if (subcategory) {
        // Update existing subcategory
        const { error } = await supabase
          .from("categories_sub")
          .update({
            name: data.name,
            description: data.description || null,
            icon: data.icon || null,
            category_id: data.category_id,
          })
          .eq("id", subcategory.id);

        if (error) throw error;

        toast({
          title: "Successo",
          description: "Sottocategoria aggiornata con successo",
        });
      } else {
        // Create new subcategory
        const { error } = await supabase
          .from("categories_sub")
          .insert({
            name: data.name,
            description: data.description || null,
            icon: data.icon || null,
            category_id: data.category_id,
          });

        if (error) throw error;

        toast({
          title: "Successo",
          description: "Sottocategoria creata con successo",
        });
      }

      onSuccess();
      onClose();
      reset();
    } catch (error: any) {
      toast({
        title: "Errore",
        description: error.message || "Si è verificato un errore",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {subcategory ? "Modifica Sottocategoria" : "Nuova Sottocategoria"}
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="category_id">Categoria Principale *</Label>
            <Select value={categoryId} onValueChange={(value) => setValue("category_id", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Seleziona una categoria" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.category_id && (
              <p className="text-sm text-destructive">La categoria è obbligatoria</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="name">Nome *</Label>
            <Input
              id="name"
              {...register("name", { required: "Il nome è obbligatorio" })}
              placeholder="Nome della sottocategoria"
            />
            {errors.name && (
              <p className="text-sm text-destructive">{errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Descrizione</Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Descrizione della sottocategoria"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="icon">Icona</Label>
            <Input
              id="icon"
              {...register("icon")}
              placeholder="Nome icona (es. tag, folder, etc.)"
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
              Annulla
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Salvando..." : subcategory ? "Aggiorna" : "Crea"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};