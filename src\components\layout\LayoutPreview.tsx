import { useState, useEffect, useCallback, useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { DoorClosed, Table, Armchair, Sofa, Bed, Move, ZoomIn, ZoomOut, Maximize } from "lucide-react";
import { LayoutElement } from "@/types/types";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";

interface LayoutPreviewProps {
  elements: LayoutElement[];
  activeRoomId?: string | null;
  onLayoutUpdate?: (elements: LayoutElement[], activeRoomId: string | null) => void;
}

interface ElementPosition {
  [key: string]: { x: number; y: number };
}

interface CanvasPosition {
  x: number;
  y: number;
  zoom: number;
}

const MIN_ZOOM = 0.5;
const MAX_ZOOM = 2;
const ZOOM_STEP = 0.1;

const LayoutPreview = ({ elements, activeRoomId, onLayoutUpdate }: LayoutPreviewProps) => {
  const [rooms, setRooms] = useState<LayoutElement[]>([]);
  const [currentRoomElements, setCurrentRoomElements] = useState<LayoutElement[]>([]);
  const [selectedRoom, setSelectedRoom] = useState<LayoutElement | null>(null);
  const [elementPositions, setElementPositions] = useState<ElementPosition>({});
  const [draggedElement, setDraggedElement] = useState<string | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  
  const [canvasPosition, setCanvasPosition] = useState<CanvasPosition>({
    x: 0,
    y: 0,
    zoom: 1
  });
  const [isPanning, setIsPanning] = useState(false);
  const [startPanPosition, setStartPanPosition] = useState({ x: 0, y: 0 });
  
  const elementsRef = useRef(elements);
  const activeRoomIdRef = useRef(activeRoomId);
  const initializedRef = useRef(false);
  const positionsInitializedRef = useRef<{[key: string]: boolean}>({});

  const checkRoomInitialized = (roomId: string): boolean => {
    return !!positionsInitializedRef.current[roomId];
  };

  const setRoomInitialized = (roomId: string) => {
    positionsInitializedRef.current[roomId] = true;
  };

  useEffect(() => {
    elementsRef.current = elements;
    activeRoomIdRef.current = activeRoomId;
    
    const roomElements = elements.filter(
      (element) => element.type === "sala" || element.type === "piano"
    );
    
    if (JSON.stringify(roomElements) !== JSON.stringify(rooms)) {
      setRooms(roomElements);
    }

    if (activeRoomId) {
      const room = elements.find((e) => e.id === activeRoomId);
      if (room) {
        if (!selectedRoom || selectedRoom.id !== room.id) {
          setSelectedRoom(room);
        }
        
        const childElements = elements.filter((e) => e.parentId === activeRoomId);
        
        if (JSON.stringify(childElements) !== JSON.stringify(currentRoomElements)) {
          setCurrentRoomElements(childElements);
        }
        
        if (!checkRoomInitialized(activeRoomId) && !roomMatchesPositions(activeRoomId)) {
          resetElementPositions(childElements, room);
          setRoomInitialized(activeRoomId);
        }
      }
    } else if (roomElements.length > 0) {
      const firstRoom = roomElements[0];
      
      if (!selectedRoom || !roomElements.some(r => r.id === selectedRoom.id)) {
        setSelectedRoom(firstRoom);
      }
      
      const childElements = elements.filter(
        (e) => e.parentId === firstRoom.id
      );
      
      if (JSON.stringify(childElements) !== JSON.stringify(currentRoomElements)) {
        setCurrentRoomElements(childElements);
      }
      
      if (!checkRoomInitialized(firstRoom.id) && !roomMatchesPositions(firstRoom.id)) {
        resetElementPositions(childElements, firstRoom);
        setRoomInitialized(firstRoom.id);
      }
    }
  }, [elements, activeRoomId]);

  const roomMatchesPositions = useCallback((roomId: string): boolean => {
    const elementsInRoom = elementsRef.current.filter(element => 
      element.parentId === roomId);
    
    return elementsInRoom.some(element => 
      elementPositions[element.id] !== undefined
    );
  }, [elementPositions]);

  const resetElementPositions = useCallback((elements: LayoutElement[], room: LayoutElement) => {
    if (elements.length === 0) return;
    
    const newPositions: ElementPosition = { ...elementPositions };
    let hasChanges = false;
    
    elements.forEach((element, index) => {
      const position = getInitialElementPosition(index, elements.length, room);
      
      if (!newPositions[element.id] || 
          newPositions[element.id].x !== position.left || 
          newPositions[element.id].y !== position.top) {
        
        newPositions[element.id] = { 
          x: position.left, 
          y: position.top 
        };
        hasChanges = true;
      }
    });
    
    if (hasChanges) {
      setElementPositions(newPositions);
    }
    
    if (canvasPosition.x !== 0 || canvasPosition.y !== 0 || canvasPosition.zoom !== 1) {
      setCanvasPosition({
        x: 0,
        y: 0,
        zoom: 1
      });
    }
  }, [elementPositions, canvasPosition]);

  const handleRoomSelect = useCallback((room: LayoutElement) => {
    if (selectedRoom && selectedRoom.id === room.id) return;
    
    setSelectedRoom(room);
    const childElements = elementsRef.current.filter((e) => e.parentId === room.id);
    setCurrentRoomElements(childElements);
    
    if (!checkRoomInitialized(room.id) && !roomMatchesPositions(room.id)) {
      resetElementPositions(childElements, room);
      setRoomInitialized(room.id);
    }
  }, [selectedRoom, resetElementPositions, roomMatchesPositions]);

  const getInitialElementPosition = (index: number, totalElements: number, room: LayoutElement) => {
    const roomWidth = room.width || 10;
    const roomHeight = room.height || 10;
    
    const cols = Math.ceil(Math.sqrt(totalElements));
    const rows = Math.ceil(totalElements / cols);
    
    const col = index % cols;
    const row = Math.floor(index / cols);
    
    const cellWidth = roomWidth / cols;
    const cellHeight = roomHeight / rows;
    
    const randomOffsetX = (Math.random() - 0.5) * (cellWidth * 0.3);
    const randomOffsetY = (Math.random() - 0.5) * (cellHeight * 0.3);
    
    return {
      left: (col * cellWidth + cellWidth / 2 + randomOffsetX) / roomWidth * 100,
      top: (row * cellHeight + cellHeight / 2 + randomOffsetY) / roomHeight * 100,
    };
  };

  const getElementSize = (element: LayoutElement) => {
    // eslint-disable-next-line prefer-const
    let baseSize = 40;
    
    switch (element.type) {
      case "tavolo":
        return {
          width: `${baseSize + (element.capacity || 1) * 5}px`,
          height: `${baseSize + (element.capacity || 1) * 5}px`,
        };
      case "camera":
        return {
          width: `${baseSize + 20}px`,
          height: `${baseSize + 20}px`,
        };
      case "cabina":
        return {
          width: `${baseSize + 10}px`,
          height: `${baseSize + 10}px`, 
        };
      default:
        return {
          width: `${baseSize}px`,
          height: `${baseSize}px`,
        };
    }
  };

  const getElementIcon = (type: string) => {
    switch (type) {
      case "sala":
      case "piano":
        return <DoorClosed className="h-full w-full" />;
      case "tavolo":
        return <Table className="h-full w-full" />;
      case "postazione":
      case "attrezzo":
        return <Armchair className="h-full w-full" />;
      case "camera":
        return <Bed className="h-full w-full" />;
      case "cabina":
        return <Sofa className="h-full w-full" />;
      default:
        return <DoorClosed className="h-full w-full" />;
    }
  };

  const getElementColor = (type: string) => {
    switch (type) {
      case "tavolo":
        return "bg-amber-100 border-amber-400";
      case "postazione":
        return "bg-blue-100 border-blue-400";
      case "attrezzo":
        return "bg-green-100 border-green-400";
      case "camera":
        return "bg-purple-100 border-purple-400";
      case "cabina":
        return "bg-rose-100 border-rose-400";
      default:
        return "bg-gray-100 border-gray-400";
    }
  };

  const handleElementClick = useCallback((e: React.MouseEvent, elementId: string) => {
    if (isDragging) return;
    
    e.stopPropagation();
    
    setSelectedElement(prevSelected => 
      prevSelected === elementId ? null : elementId
    );
    
    const element = elementsRef.current.find(el => el.id === elementId);
    if (element) {
      toast.info(`Selezionato: ${element.name}`);
    }
  }, [isDragging]);

  const handleCanvasClick = useCallback((e: React.MouseEvent) => {
    if (!isDragging && !isPanning) {
      setSelectedElement(null);
    }
  }, [isDragging, isPanning]);

  const handleDragStart = useCallback((e: React.MouseEvent, elementId: string) => {
    e.stopPropagation();
    
    if (!containerRef.current) return;
    
    const element = e.currentTarget as HTMLElement;
    const rect = element.getBoundingClientRect();
    
    const offsetX = e.clientX - rect.left;
    const offsetY = e.clientY - rect.top;
    
    setDragOffset({ x: offsetX, y: offsetY });
    setDraggedElement(elementId);
    setIsDragging(true);
    
    document.body.style.cursor = "move";
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!containerRef.current) return;
    
    if (isDragging && draggedElement) {
      const containerRect = containerRef.current.getBoundingClientRect();
      
      const x = ((e.clientX - containerRect.left - dragOffset.x) / canvasPosition.zoom - canvasPosition.x) / containerRect.width * 100;
      const y = ((e.clientY - containerRect.top - dragOffset.y) / canvasPosition.zoom - canvasPosition.y) / containerRect.height * 100;
      
      const boundedX = Math.max(-30, Math.min(130, x));
      const boundedY = Math.max(-30, Math.min(130, y));
      
      setElementPositions(prev => {
        const currentPos = prev[draggedElement];
        if (!currentPos || 
            Math.abs(currentPos.x - boundedX) > 0.1 || 
            Math.abs(currentPos.y - boundedY) > 0.1) {
          return {
            ...prev,
            [draggedElement]: { x: boundedX, y: boundedY }
          };
        }
        return prev;
      });
    }
    
    if (isPanning && !isDragging) {
      const dx = e.clientX - startPanPosition.x;
      const dy = e.clientY - startPanPosition.y;
      
      if (Math.abs(dx) > 0.5 || Math.abs(dy) > 0.5) {
        setCanvasPosition(prev => ({
          ...prev,
          x: prev.x + dx / prev.zoom,
          y: prev.y + dy / prev.zoom
        }));
        
        setStartPanPosition({
          x: e.clientX,
          y: e.clientY
        });
      }
    }
  }, [isDragging, draggedElement, dragOffset, isPanning, startPanPosition, canvasPosition]);

  const handleMouseUp = useCallback(() => {
    if (isDragging && draggedElement && onLayoutUpdate) {
      // Aggiorna l'elemento nel layout con la nuova posizione
      const updatedElements = elements.map(element => {
        if (element.id === draggedElement && elementPositions[draggedElement]) {
          const position = elementPositions[draggedElement];
          return {
            ...element,
            x: position.x,
            y: position.y
          };
        }
        return element;
      });
      
      onLayoutUpdate(updatedElements, activeRoomId);
      toast.success("Posizione salvata");
    }
    
    if (isDragging) {
      setIsDragging(false);
      setDraggedElement(null);
    }
    
    if (isPanning) {
      setIsPanning(false);
    }
    
    document.body.style.cursor = "default";
  }, [isDragging, isPanning, draggedElement, onLayoutUpdate, elements, elementPositions, activeRoomId]);

  const handleCanvasDragStart = useCallback((e: React.MouseEvent) => {
    if (e.button === 1 || e.button === 2) {
      e.preventDefault();
      setIsPanning(true);
      setStartPanPosition({
        x: e.clientX,
        y: e.clientY
      });
      document.body.style.cursor = "grab";
    }
  }, []);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.code === 'Space' && !isPanning) {
        document.body.style.cursor = "grab";
        const handler = (mouseEvent: MouseEvent) => {
          setIsPanning(true);
          setStartPanPosition({
            x: mouseEvent.clientX,
            y: mouseEvent.clientY
          });
          document.removeEventListener('mousedown', handler);
        };
        document.addEventListener('mousedown', handler);
      }
    };
    
    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.code === 'Space') {
        document.body.style.cursor = "default";
        document.removeEventListener('mousedown', () => {});
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [isPanning]);

  const handleWheel = useCallback((e: WheelEvent) => {
    if (!containerRef.current || isPanning || isDragging) return;
    
    if (e.ctrlKey || e.metaKey) {
      e.preventDefault();
      
      const containerRect = containerRef.current.getBoundingClientRect();
      
      const mouseX = (e.clientX - containerRect.left) / containerRect.width;
      const mouseY = (e.clientY - containerRect.top) / containerRect.height;
      
      const delta = e.deltaY < 0 ? ZOOM_STEP : -ZOOM_STEP;
      const newZoom = Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, canvasPosition.zoom + delta));
      
      if (Math.abs(newZoom - canvasPosition.zoom) < 0.001) return;
      
      const zoomFactor = newZoom / canvasPosition.zoom;
      
      const newX = canvasPosition.x - (mouseX * containerRect.width) * (zoomFactor - 1) / newZoom;
      const newY = canvasPosition.y - (mouseY * containerRect.height) * (zoomFactor - 1) / newZoom;
      
      setCanvasPosition({
        x: newX,
        y: newY,
        zoom: newZoom
      });
    }
  }, [canvasPosition, isPanning, isDragging]);

  useEffect(() => {
    const preventDefaultZoom = (e: WheelEvent) => {
      if (e.ctrlKey || e.metaKey) {
        e.preventDefault();
      }
    };
    
    document.addEventListener('wheel', preventDefaultZoom, { passive: false });
    
    return () => {
      document.removeEventListener('wheel', preventDefaultZoom);
    };
  }, []);

  useEffect(() => {
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
    containerRef.current?.addEventListener("wheel", handleWheel, { passive: false });
    
    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
      containerRef.current?.removeEventListener("wheel", handleWheel);
    };
  }, [handleMouseMove, handleMouseUp, handleWheel]);

  const handleResetPositions = useCallback(() => {
    if (selectedRoom) {
      if (positionsInitializedRef.current[selectedRoom.id]) {
        delete positionsInitializedRef.current[selectedRoom.id];
      }
      resetElementPositions(currentRoomElements, selectedRoom);
      setRoomInitialized(selectedRoom.id);
      toast.success("Posizioni degli elementi resettate");
    }
  }, [selectedRoom, currentRoomElements, resetElementPositions]);

  const handleResetView = useCallback(() => {
    setCanvasPosition({
      x: 0,
      y: 0,
      zoom: 1
    });
    toast.success("Vista resettata");
  }, []);

  const handleZoomIn = useCallback(() => {
    setCanvasPosition(prev => ({
      ...prev,
      zoom: Math.min(MAX_ZOOM, prev.zoom + ZOOM_STEP)
    }));
  }, []);

  const handleZoomOut = useCallback(() => {
    setCanvasPosition(prev => ({
      ...prev,
      zoom: Math.max(MIN_ZOOM, prev.zoom - ZOOM_STEP)
    }));
  }, []);

  return (
    <div className="space-y-4">
      {rooms.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-8 text-center">
          <p className="text-sm text-gray-500">
            Nessuna sala o piano configurato. Configura prima il layout nella scheda "Configurazione".
          </p>
        </div>
      ) : (
        <>
          {!activeRoomId && rooms.length > 1 && (
            <div className="flex flex-wrap gap-2 mb-4">
              {rooms.map((room) => (
                <button
                  key={room.id}
                  onClick={() => handleRoomSelect(room)}
                  className={`px-3 py-1 text-sm rounded-md border transition-colors ${
                    selectedRoom?.id === room.id
                      ? "bg-primary/10 border-primary text-primary"
                      : "bg-gray-50 border-gray-200 hover:bg-gray-100"
                  }`}
                >
                  {room.name}
                </button>
              ))}
            </div>
          )}

          {selectedRoom && (
            <Card>
              <CardContent className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <div className="text-xs text-gray-500">
                    <span className="font-semibold">Suggerimento:</span> Usa Spazio+Trascina o tasto centrale mouse per la panoramica, Ctrl+Rotellina per lo zoom
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleZoomOut}
                      className="h-7 w-7 p-0"
                      title="Riduci zoom"
                    >
                      <ZoomOut className="h-3.5 w-3.5" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleZoomIn}
                      className="h-7 w-7 p-0"
                      title="Aumenta zoom"
                    >
                      <ZoomIn className="h-3.5 w-3.5" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleResetView}
                      className="h-7 w-7 p-0"
                      title="Reset vista"
                    >
                      <Maximize className="h-3.5 w-3.5" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleResetPositions}
                      className="h-7 pl-2 pr-2"
                      title="Reset posizioni"
                    >
                      <Move className="h-3.5 w-3.5 mr-1" /> Reset
                    </Button>
                  </div>
                </div>
                <div 
                  ref={containerRef}
                  className="relative border border-dashed border-gray-300 rounded-lg w-full overflow-hidden" 
                  style={{ height: "500px", cursor: isPanning ? "grabbing" : "default" }}
                  onMouseDown={handleCanvasDragStart}
                  onContextMenu={(e) => e.preventDefault()}
                  onClick={handleCanvasClick}
                >
                  <div className="absolute top-2 left-2 z-10 bg-white/80 px-2 py-1 text-xs rounded-md border">
                    {selectedRoom.name} ({selectedRoom.width}m × {selectedRoom.height}m) - Zoom: {Math.round(canvasPosition.zoom * 100)}%
                  </div>
                  
                  <div
                    className="absolute w-full h-full origin-top-left"
                    style={{
                      transform: `scale(${canvasPosition.zoom}) translate(${canvasPosition.x}px, ${canvasPosition.y}px)`,
                      transition: isDragging || isPanning ? 'none' : 'transform 0.1s ease-out',
                    }}
                  >
                    <div className="absolute inset-0">
                      <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                          <pattern id="smallGrid" width="10" height="10" patternUnits="userSpaceOnUse">
                            <path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(128,128,128,0.1)" strokeWidth="0.5" />
                          </pattern>
                          <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse">
                            <rect width="100" height="100" fill="url(#smallGrid)" />
                            <path d="M 100 0 L 0 0 0 100" fill="none" stroke="rgba(128,128,128,0.2)" strokeWidth="1" />
                          </pattern>
                        </defs>
                        <rect width="100%" height="100%" fill="url(#grid)" />
                      </svg>
                    </div>
                  
                    {currentRoomElements.map((element) => {
                      const position = elementPositions[element.id] || { x: 50, y: 50 };
                      const size = getElementSize(element);
                      const colorClass = getElementColor(element.type);
                      const isDraggingThis = draggedElement === element.id;
                      const isSelected = selectedElement === element.id;
                      
                      return (
                        <div
                          key={element.id}
                          className={`absolute rounded-md border-2 flex items-center justify-center shadow-sm hover:shadow-md hover:z-10 cursor-move 
                          ${colorClass} 
                          ${isDraggingThis ? "z-20 opacity-90 scale-105" : ""}
                          ${isSelected ? "ring-2 ring-primary ring-offset-1 shadow-lg z-10 scale-105" : ""}`}
                          style={{
                            left: `${position.x}%`,
                            top: `${position.y}%`,
                            ...size,
                            transform: "translate(-50%, -50%)",
                            transition: isDraggingThis ? 'none' : 'all 0.1s ease-out',
                          }}
                          title={`${element.name}${element.capacity ? ` (${element.capacity} ${element.capacity === 1 ? "posto" : "posti"})` : ""}`}
                          onMouseDown={(e) => handleDragStart(e, element.id)}
                          onClick={(e) => handleElementClick(e, element.id)}
                        >
                          <div className="p-1 w-full h-full flex flex-col items-center justify-center">
                            <div className="flex-shrink-0 w-full h-full flex items-center justify-center">
                              {getElementIcon(element.type)}
                            </div>
                            <div className="text-[8px] font-medium text-center leading-tight overflow-hidden whitespace-nowrap text-ellipsis w-full">
                              {element.name}
                            </div>
                            {element.capacity && (
                              <div className="text-[7px] text-gray-600">
                                {element.capacity} {element.capacity === 1 ? "p" : "p"}
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}

                    {currentRoomElements.length === 0 && (
                      <div className="absolute inset-0 flex items-center justify-center text-sm text-gray-500">
                        Nessun elemento in questa sala
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
          
          <div className="flex flex-wrap gap-4 mt-4 p-2 border rounded-md bg-gray-50">
            <div className="text-sm font-medium">Legenda:</div>
            <div className="flex items-center gap-1 text-xs">
              <div className={`w-3 h-3 rounded-sm bg-amber-100 border border-amber-400`}></div>
              <span>Tavoli</span>
            </div>
            <div className="flex items-center gap-1 text-xs">
              <div className={`w-3 h-3 rounded-sm bg-blue-100 border border-blue-400`}></div>
              <span>Postazioni</span>
            </div>
            <div className="flex items-center gap-1 text-xs">
              <div className={`w-3 h-3 rounded-sm bg-green-100 border border-green-400`}></div>
              <span>Attrezzi</span>
            </div>
            <div className="flex items-center gap-1 text-xs">
              <div className={`w-3 h-3 rounded-sm bg-purple-100 border border-purple-400`}></div>
              <span>Camere</span>
            </div>
            <div className="flex items-center gap-1 text-xs">
              <div className={`w-3 h-3 rounded-sm bg-rose-100 border border-rose-400`}></div>
              <span>Cabine</span>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default LayoutPreview;
