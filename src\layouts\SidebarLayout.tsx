import { useEffect } from "react";
import { useBusinessStore } from "@/store/businessStore";
import { useAuth } from "@/contexts/AuthContext";

import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";

import { Separator } from "@/components/ui/separator";
import CustomBreadcrumb from "@/components/CustomBreadcrumb";
import { ChatBotToggle } from "@/components/ChatBotToggle";
import { MainLayoutProps } from "./MainLayout";
import { AppSidebar } from "./AppSidebar";

export const SidebarLayout = ({ children }: MainLayoutProps) => {
  const fetchBusinesses = useBusinessStore((state) => state.fetchBusinesses);
  const { user } = useAuth();

  // Fetch businesses when component mounts
  useEffect(() => {
    if (user?.id) {
      fetchBusinesses(user.id);
    }
  }, [user, fetchBusinesses]);

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <CustomBreadcrumb />
          </div>
        </header>
        <main className="flex flex-1 flex-col gap-4 p-4 pt-0">
          {children}
          <ChatBotToggle />
          {/* <div className="grid auto-rows-min gap-4 md:grid-cols-3">
              <div className="aspect-video rounded-xl bg-muted/50" />
              <div className="aspect-video rounded-xl bg-muted/50" />
              <div className="aspect-video rounded-xl bg-muted/50" />
            </div>
            <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min" /> */}
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
};
