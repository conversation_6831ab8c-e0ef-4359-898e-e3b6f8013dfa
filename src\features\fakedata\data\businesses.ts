import { BusinessCategory } from '../types';

// Enhanced business data with more realistic categorization
export const businessNames = {
  bar: [
    "Bar Sport", "Caffè Italia", "Bar Centrale", "Caffetteria Milano",
    "Bar Roma", "American Bar", "Irish Pub", "Cocktail Bar",
    "Wine Bar", "Lounge Bar", "Caffè Letterario", "Pasticceria & Caffè",
    "Bar del Corso", "Bar Moderno", "Bar di Moda", "Caffè Sognante",
    "Bar Urbano", "Caffè d'Autore", "Bar Classico", "Caffè del Duomo"
  ],
  ristorante: [
    "Ristorante Da Luigi", "Pizzeria Napoletana", "Trattoria Il Gusto",
    "Osteria La Cucina", "Ristorante Bella Napoli", "Pizzeria Margherita",
    "Ristorante Al Duomo", "Trattoria da Mario", "Ristorante La Pergola",
    "Pizzeria Il Forno", "Taverna del Corso", "Ristorante La Terrazza"
  ],
  alimentari: [
    "Alimentari Da Mario", "Macelleria Rossi", "Panificio Il Forno",
    "Gastronomia Italiana", "Pastificio Artigianale", "Pescheria Del Mare",
    "Salumeria Tradizionale", "Fruttivendolo Il Raccolto", "Enoteca Vini & Sapori"
  ],
  bellezza: [
    "Parrucchiere Trend", "Salone Bellezza", "Centro Estetico Aurora",
    "Barbiere Classico", "Hair Stylist Milano", "Beauty Center",
    "Parrucchiere Donna & Uomo", "Barber Shop", "Estetica Eleganza"
  ],
  abbigliamento: [
    "Boutique Eleganza", "Sartoria Italiana", "Atelier Moda",
    "Fashion Store", "Abbigliamento Uomo", "Negozio Donna",
    "Calzature Europa", "Accessori & Borse", "Moda d'Autore"
  ],
  palestra: [
    "Palestra Energia", "Centro Fitness Italia", "Palestra Vitalità",
    "Gym Evolution", "Palestra Forma", "Elite Fitness Club",
    "Palestra Dinamica", "Atelier Benessere", "Palestra Performance"
  ],
  spa: [
    "SPA Serenità", "Oasi del Relax", "Centro Benessere",
    "SPA Armonia", "Rifugio del Relax", "Spa Incanto",
    "Oasi Spa", "Benessere & Bellezza", "SPA Eterna"
  ],
  cinema: [
    "Cinema Odeon", "Multisala Centrale", "Cinema Ariston",
    "Cinema Capitol", "Cinema Rossini", "Cinema Verdi",
    "Cinema Italia", "Cinema Modernissimo", "Cinema Apollo"
  ],
  fioraio: [
    "Fioraio Il Giardino", "Fiori & Composizioni", "Fioreria Bella Vista",
    "Il Petalo Rosa", "Fiori di Campo", "Fioraio del Centro",
    "Giardino Incantato", "Fioreria Primavera", "Il Bouquet Perfetto"
  ],
  hotel: [
    "Hotel Centrale", "Grand Hotel Europa", "Hotel Excelsior",
    "Hotel Plaza", "Hotel Majestic", "Hotel Bellavista",
    "Hotel Villa Rosa", "Hotel Savoy", "Hotel Royale"
  ],
  teatro: [
    "Teatro Comunale", "Teatro Sociale", "Teatro San Carlo",
    "Teatro dell'Opera", "Teatro Verdi", "Teatro Rossini",
    "Teatro Puccini", "Teatro Bellini", "Teatro Donizetti"
  ],
  aperitivo: [
    "Aperitivo Centrale", "Cocktail Lounge", "Spritz Bar",
    "Aperitivo Italiano", "Cocktail & Co", "Aperitivo Moderno",
    "Spritz & Stuzzichini", "Cocktail Experience", "Aperitivo Elegante"
  ],
  default: [
    "Attività Italiana", "Negozio Centro", "Emporio Milano",
    "Azienda Rossi", "Ditta Bianchi", "Servizi Generali"
  ]
} as const;

export const businessSuffixes = [
  "S.r.l.", "S.p.A.", "& Figli", "di Mario Rossi", "Group",
  "S.n.c.", "e Figli", "Holding", "International", "Industries"
];

// Products/Services by category
export const categoryProducts = {
  bar: [
    "Caffè Espresso", "Cappuccino", "Caffè Macchiato", "Brioche",
    "Cornetto alla Crema", "Aperitivo Spritz", "Panino Gourmet",
    "Cocktail Signature", "Croissant Artigianale", "Tramezzino Classico"
  ],
  ristorante: [
    "Pizza Margherita", "Spaghetti alla Carbonara", "Bistecca Fiorentina",
    "Risotto ai Funghi", "Lasagne al Forno", "Tagliata di Manzo",
    "Penne all'Arrabbiata", "Pizza Quattro Stagioni", "Tiramisù"
  ],
  alimentari: [
    "Prosciutto di Parma", "Parmigiano Reggiano", "Pasta Fresca",
    "Olio Extra Vergine", "Vino Rosso Toscano", "Salame Artigianale",
    "Mozzarella di Bufala", "Pane Casereccio", "Conserve Artigianali"
  ],
  bellezza: [
    "Taglio & Piega", "Colorazione Professionale", "Trattamento Viso",
    "Manicure Deluxe", "Pacchetto Benessere", "Massaggio Relax",
    "Cura della Barba", "Extension Ciglia", "Acconciatura Sposa"
  ],
  abbigliamento: [
    "Abito Sartoriale", "Camicia su Misura", "Collezione Estate",
    "Accessori Fashion", "Borsa in Pelle", "Scarpe Artigianali",
    "Cappotto Elegante", "Jeans Premium", "Abito da Sera"
  ],
  palestra: [
    "Abbonamento Mensile", "Personal Training", "Corso Fitness",
    "Yoga e Pilates", "Sala Pesi", "Cardio Training",
    "Nuoto", "Acqua Fitness", "Preparazione Atletica"
  ],
  spa: [
    "Massaggio Rilassante", "Trattamento Viso", "Sauna e Bagno Turco",
    "Idromassaggio", "Trattamento Corpo", "Percorso Benessere",
    "Massaggio Hot Stone", "Trattamento Anti-Stress", "Day SPA"
  ],
  cinema: [
    "Biglietto Intero", "Biglietto Ridotto", "Biglietto Studenti",
    "Abbonamento Cinema", "Serata Premiere", "Film in Anteprima",
    "Sala VIP", "Proiezione 3D", "Cinema per Bambini"
  ],
  fioraio: [
    "Bouquet di Rose", "Composizione Floreale", "Bouquet Sposa",
    "Centrotavola", "Bouquet San Valentino", "Composizione Lutto",
    "Pianta Appartamento", "Orchidea in Vaso", "Decorazione Matrimonio"
  ],
  hotel: [
    "Camera Standard", "Camera Deluxe", "Suite Junior",
    "Weekend Romantico", "Pacchetto Benessere", "Soggiorno Business",
    "Pernottamento B&B", "Mezza Pensione", "Servizio Spa"
  ],
  teatro: [
    "Biglietto Platea", "Biglietto Palchi", "Biglietto Galleria",
    "Abbonamento Teatrale", "Spettacolo di Prosa", "Opera Lirica",
    "Balletto", "Teatro Contemporaneo", "Teatro per Bambini"
  ],
  aperitivo: [
    "Aperitivo Classico", "Spritz Veneziano", "Negroni",
    "Cocktail Signature", "Aperitivo Buffet", "Happy Hour",
    "Tagliere Salumi", "Stuzzichini Misti", "Degustazione Vini"
  ],
  default: [
    "Servizio Premium", "Pacchetto Base", "Offerta Speciale",
    "Prodotto Signature", "Consulenza Specializzata", "Kit Completo"
  ]
} as const;

// Business lifecycle patterns
export const businessAgeDistribution = {
  new: { weight: 0.2, yearRange: [2020, 2024] },      // 20% new businesses (0-4 years)  
  established: { weight: 0.5, yearRange: [2000, 2019] }, // 50% established (5-24 years)
  mature: { weight: 0.3, yearRange: [1970, 1999] }       // 30% mature (25+ years)
} as const;

// Success level characteristics
export const successLevelCharacteristics = {
  low: {
    dealsPerMonth: [1, 3],
    photoCount: [0, 2],
    descriptionLength: 'short',
    priceReduction: [0.4, 0.7] // More aggressive discounts
  },
  medium: {
    dealsPerMonth: [2, 5], 
    photoCount: [2, 4],
    descriptionLength: 'medium',
    priceReduction: [0.2, 0.4]
  },
  high: {
    dealsPerMonth: [1, 2], // Fewer but premium deals
    photoCount: [3, 6],
    descriptionLength: 'long',
    priceReduction: [0.1, 0.25] // Smaller discounts, premium positioning
  }
} as const;

export const italianFirstNames = [
  "Andrea", "Marco", "Giuseppe", "Antonio", "Giovanni", "Mario",
  "Luigi", "Francesco", "Roberto", "Paolo", "Alessio", "Stefano",
  "Davide", "Michele", "Carlo", "Alessandro", "Leonardo", "Massimo"
];

export const italianLastNames = [
  "Rossi", "Bianchi", "Ferrari", "Esposito", "Romano", "Colombo",
  "Ricci", "Marino", "Greco", "Bruno", "Gallo", "Conti",
  "De Luca", "Costa", "Giordano", "Mancini", "Rizzo", "Lombardi"
];

export const emailDomains = ["it", "com", "net", "org"];
export const phoneFormats = ["+39 ### ### ####", "+39 ## ## ## ##", "### ### ####"];

// Category-specific pricing ranges (min, max in euros)
export const categoryPricing = {
  bar: { min: 8, max: 35 },              // Caffè, aperitivi, snack
  ristorante: { min: 25, max: 120 },     // Menu, pizze, piatti  
  alimentari: { min: 15, max: 80 },      // Prodotti alimentari, spesa
  bellezza: { min: 20, max: 150 },       // Servizi parrucchiere/estetica
  abbigliamento: { min: 30, max: 250 },  // Vestiti, accessori
  palestra: { min: 25, max: 100 },       // Abbonamenti, corsi
  spa: { min: 40, max: 180 },            // Trattamenti benessere
  cinema: { min: 8, max: 18 },           // Biglietti cinema
  fioraio: { min: 15, max: 85 },         // Bouquet, composizioni
  hotel: { min: 60, max: 350 },          // Camere, servizi hotel
  teatro: { min: 20, max: 120 },         // Biglietti spettacoli
  aperitivo: { min: 12, max: 35 },       // Cocktail, aperitivi
  default: { min: 20, max: 100 }         // Fallback generico
} as const;

export const businessDescriptions = {
  bar: [
    "Locale accogliente nel cuore della città, perfetto per la colazione e l'aperitivo",
    "Bar storico con tradizione familiare, specializzato in caffè di qualità",
    "Ambiente moderno e rilassante, ideale per una pausa durante la giornata"
  ],
  ristorante: [
    "Cucina tradizionale italiana con ingredienti freschi e genuini",
    "Ristorante familiare con specialità della casa e pasta fatta in casa",
    "Ambiente elegante e raffinato per cene romantiche e occasioni speciali"
  ],
  alimentari: [
    "Prodotti freschi e di qualità selezionati dal territorio",
    "Gastronomia artigianale con specialità locali e nazionali",
    "Il meglio della tradizione alimentare italiana"
  ],
  bellezza: [
    "Centro estetico con trattamenti personalizzati e prodotti di qualità",
    "Team di professionisti per il vostro benessere e la vostra bellezza",
    "Ambiente rilassante e servizi all'avanguardia"
  ],
  abbigliamento: [
    "Moda italiana e internazionale per ogni occasione",
    "Abbigliamento di qualità con stile contemporaneo",
    "Boutique elegante con collezioni esclusive"
  ],
  palestra: [
    "Centro fitness completo con attrezzature moderne e corsi di gruppo",
    "Palestra attrezzata con personal trainer qualificati",
    "Spazio dedicato al benessere fisico e al fitness"
  ],
  spa: [
    "Oasi di relax e benessere nel cuore della città",
    "Centro benessere con trattamenti rigeneranti e rilassanti",
    "Percorsi spa personalizzati per il vostro relax"
  ],
  cinema: [
    "Sala cinematografica con le ultime novità del grande schermo",
    "Multisala moderna con tecnologia avanzata e comfort garantito",
    "Cinema d'autore e blockbuster in un ambiente accogliente"
  ],
  fioraio: [
    "Creazioni floreali uniche per ogni occasione speciale",
    "Fiori freschi e composizioni artistiche della tradizione italiana",
    "Arte floreale e piante selezionate con cura e passione"
  ],
  hotel: [
    "Ospitalità italiana con comfort moderni e servizio impeccabile",
    "Hotel elegante nel cuore della città con vista panoramica",
    "Soggiorno indimenticabile tra tradizione e innovazione"
  ],
  teatro: [
    "Stagione teatrale ricca di spettacoli di prosa e musica",
    "Teatro storico con programmazione di qualità e tradizione",
    "Palcoscenico per grandi interpreti e nuovi talenti emergenti"
  ],
  aperitivo: [
    "L'arte dell'aperitivo italiano in un ambiente sofisticato",
    "Cocktail creativi e stuzzichini gourmet per il vostro relax",
    "Mixology d'eccellenza e atmosfera unica per l'happy hour"
  ],
  default: [
    "Attività commerciale di qualità al servizio dei clienti",
    "Professionalità e competenza per le vostre esigenze",
    "Servizio clienti di eccellenza"
  ]
} as const;