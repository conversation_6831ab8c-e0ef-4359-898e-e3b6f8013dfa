import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Plus,
  Bot,
  Phone,
  MessageSquare,
  User,
  TrendingUp,
  BarChart3,
  Lock,
} from "lucide-react";
import { useAgentLimits } from "@/hooks/useAgentLimits";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { getAgentTypeLabel } from "@/queries/usePricingTiers";
import { Skeleton } from "./ui/skeleton";
import MainLayout from "@/layouts/MainLayout";
import { useQueryClient } from "@tanstack/react-query";

interface CreateAgentDialogProps {
  businessId: string;
  onAgentCreated: () => void;
}

export const CreateAgentDialog = ({
  businessId,
  onAgentCreated,
}: CreateAgentDialogProps) => {
  const [open, setOpen] = useState(false);
  const [selectedDefinition, setSelectedDefinition] = useState<string | null>(
    null
  );

  const [creating, setCreating] = useState(false);

  const { data: limits, isLoading } = useAgentLimits(businessId);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const getAgentIcon = (type: string) => {
    switch (type) {
      case "booking":
        return Phone;
      case "customer_support":
        return MessageSquare;
      case "sales":
        return User;
      case "marketing":
        return TrendingUp;
      case "data_analysis":
        return BarChart3;
      default:
        return Bot;
    }
  };

  const handleCreateAgent = async () => {
    if (!selectedDefinition ) {
      toast({
        title: "Errore",
        description: "Seleziona un tipo di agente e inserisci un nome",
        variant: "destructive",
      });
      return;
    }

    const definition = limits?.allowedAgentDefinitions.find(
      (def) => def.id === selectedDefinition
    );
    if (!definition) return;

    setCreating(true);
    try {
      const { error } = await supabase.from("ai_business_agents").insert({
        business_id: businessId,
        agent_type: definition.agent_type,
        name:definition.name.trim(),
        description: definition.description.trim() || null,
        voice_id: definition.voice_id,
        avatar_url: definition.avatar_url,


        is_active: false,
      });

      if (error) throw error;

      toast({
        title: "Successo",
        description: "Agente creato con successo",
      });

      // Invalidate agent limits cache to reflect changes immediately
      queryClient.invalidateQueries({ 
        queryKey: ["agentLimits", businessId] 
      });

      setOpen(false);
      setSelectedDefinition(null);
    
      onAgentCreated();
    } catch (error) {
      console.error("Error creating agent:", error);
      toast({
        title: "Errore",
        description: "Impossibile creare l'agente",
        variant: "destructive",
      });
    } finally {
      setCreating(false);
    }
  };
  console.log("isLoading", limits);
  if (isLoading)
    return (
      <div className="flex items-center">
      <Skeleton className="h-10 w-32 rounded-md" />
    </div>
    );

  return (
    <Dialog open={open} onOpenChange={(status)=>{setOpen(status); setSelectedDefinition(null);}}>
      <DialogTrigger asChild>
        <Button disabled={!limits?.canCreateMore}>
          <Plus className="h-4 w-4 mr-2" />
          Nuovo Agente
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Crea Nuovo Agente AI</DialogTitle>
          <DialogDescription>
            Seleziona il tipo di agente che vuoi creare per la tua attività
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Subscription info */}
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-blue-900">
                  Piano: {limits?.tierType}
                </h4>
                <p className="text-sm text-blue-700">
                  {limits?.currentActiveAgents} di {limits?.maxAgents} agenti
                  attivi
                </p>
              </div>
              <Badge variant={limits?.canCreateMore ? "default" : "secondary"}>
                {limits?.canCreateMore
                  ? "Puoi creare agenti"
                  : "Limite raggiunto"}
              </Badge>
            </div>
          </div>

          {/* Agent type selection */}
          <div>
            <Label className="text-base font-medium">
              Seleziona Tipo di Agente
            </Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
              {limits?.allowedAgentDefinitions.map((definition) => {
                const IconComponent = getAgentIcon(definition.agent_type);
                const canCreate = limits?.canCreateType(
                  definition.agent_type as any
                );
                const hasActiveOfType =
                  (limits?.currentAgentsByType[definition.agent_type] || 0) > 0;
                
                // Disable if already has active agent of this type OR if can't create due to limits
                const isDisabled = hasActiveOfType || !canCreate;

                return (
                  <Card
                    key={definition.id}
                    className={`cursor-pointer transition-all ${
                      selectedDefinition === definition.id
                        ? "ring-2 ring-primary"
                        : !isDisabled
                        ? "hover:shadow-md"
                        : "opacity-50 cursor-not-allowed"
                    }`}
                    onClick={() =>
                      !isDisabled && setSelectedDefinition(definition.id)
                    }
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage
                            src={definition.avatar_url || undefined}
                          />
                          <AvatarFallback>
                            <IconComponent className="h-5 w-5" />
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <CardTitle className="text-lg flex items-center gap-2">
                            {definition.name}
                            {isDisabled && (
                              <Lock className="h-4 w-4 text-gray-400" />
                            )}
                          </CardTitle>
                          <CardDescription>
                            {getAgentTypeLabel(definition.agent_type)}
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      {definition.description && (
                        <p className="text-sm text-gray-600 mb-2">
                          {definition.description}
                        </p>
                      )}
                      {hasActiveOfType && (
                        <Badge variant="secondary" className="text-xs">
                          Già attivo per questo tipo
                        </Badge>
                      )}
                      {!canCreate && !hasActiveOfType && (
                        <Badge variant="secondary" className="text-xs">
                          Non disponibile nel tuo piano
                        </Badge>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Agent configuration */}
        
              <div className="flex justify-end gap-2 pt-4">
                <Button variant="outline" onClick={() => setOpen(false)}>
                  Annulla
                </Button>
                <Button
                  onClick={handleCreateAgent}
                  disabled={creating || !selectedDefinition}
                >
                  {creating ? "Creando..." : "Crea Agente"}
                </Button>
              </div>
          
          </div>
      </DialogContent>
    </Dialog>
  );
};
