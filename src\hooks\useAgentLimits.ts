import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";

type AgentDefinition =
  Database["public"]["Tables"]["ai_business_agents_profile"]["Row"];


export interface AgentLimits {
  maxAgents: number;
  currentActiveAgents: number;
  currentAgentsByType: Record<string, number>;
  allowedAgentTypes: Database["public"]["Enums"]["agent_type"][];
  allowedAgentDefinitions: AgentDefinition[];
  canCreateMore: boolean;
  canCreateType: (
    agentType: Database["public"]["Enums"]["agent_type"]
  ) => boolean;
  tierType: string;
}

/**
 * Hook per ottenere i limiti di creazione agenti per l'utente corrente
 */
export const useAgentLimits = (businessId: string | undefined) => {
  return useQuery({
    queryKey: ["agentLimits", businessId],
    queryFn: async (): Promise<AgentLimits> => {
      if (!businessId) {
        throw new Error("Business ID is required");
      }

      // Get current user
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        throw new Error("User not authenticated");
      }

      // Get user's subscription
      const { data: subscription, error: subscriptionError } = await supabase
        .from("business_subscriptions")
        .select("tier_type")
        .eq("user_id", user.id)
        .eq("status", "active")
        .maybeSingle();

      if (subscriptionError) throw subscriptionError;

      const tierType = subscription?.tier_type || "basic";

      // Get tier limits and allowed agents
      const { data: tierData, error: tierError } = await supabase
        .from("pricing_tiers")
        .select("max_agents, allowed_agents")
        .eq("tier_type", tierType)
        .single();

      if (tierError) throw tierError;

      const maxAgents = tierData?.max_agents || 2;
      const allowedAgentId = (tierData?.allowed_agents ||
        []);
     
      // Get agent definitions for allowed types
      const { data: agentDefinitions, error: definitionsError } = await supabase
        .from("ai_business_agents_profile")
        .select("*")
        .in("id", allowedAgentId);
  
      if (definitionsError) throw definitionsError;
    
      const allowedAgentTypes = agentDefinitions?.map((agent) => agent.agent_type);
    
      // Get current business agents
      const { data: currentAgents, error: agentsError } = await supabase
        .from("ai_business_agents")
        .select("*")
        .eq("business_id", businessId);

      if (agentsError) throw agentsError;

      const currentActiveAgents =
        currentAgents?.filter((agent) => agent.is_active).length || 0;

      // Count agents by type
      const currentAgentsByType: Record<string, number> = {};
      currentAgents?.forEach((agent) => {
        if (agent.is_active) {
          currentAgentsByType[agent.agent_type] =
            (currentAgentsByType[agent.agent_type] || 0) + 1;
        }
      });
 
      const canCreateMore = currentActiveAgents < maxAgents;
     
      const canCreateType = (
        agentType: Database["public"]["Enums"]["agent_type"]
      ): boolean => {
        // Check if agent type is allowed for current tier
        const isTypeAllowed = allowedAgentTypes.includes(agentType);

        if (!isTypeAllowed) return false;

        // Check if we can create more agents overall
        if (!canCreateMore) return false;

        // Check if we already have an active agent of this type (only one per type allowed)
        return (currentAgentsByType[agentType] || 0) === 0;
      };

      return {
        maxAgents,
        currentActiveAgents,
        currentAgentsByType,
        allowedAgentTypes,
        allowedAgentDefinitions: agentDefinitions || [],
        canCreateMore,
        canCreateType,
        tierType,
      };
    },
    enabled: !!businessId,
    //staleTime: 2 * 60 * 1000, // 2 minuti
    refetchOnWindowFocus: false,
  });
};
