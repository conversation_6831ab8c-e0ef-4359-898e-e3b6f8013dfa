-- Rename the ai_business_agents table to ai_business_agents_definition
ALTER TABLE public.ai_business_agents RENAME TO ai_business_agents_definition;

-- Update the constraint name to match the new table name
ALTER TABLE public.ai_business_agents_definition RENAME CONSTRAINT ai_business_agents_pkey TO ai_business_agents_definition_pkey;

-- Update the trigger name to match the new table name
DROP TRIGGER IF EXISTS set_updated_at ON public.ai_business_agents_definition;
CREATE TRIGGER set_updated_at 
  BEFORE UPDATE ON public.ai_business_agents_definition 
  FOR EACH ROW 
  EXECUTE FUNCTION handle_updated_at();

-- Update RLS policy names to match the new table name
DROP POLICY IF EXISTS "Enable read access for all users" ON public.ai_business_agents_definition;
CREATE POLICY "Enable read access for all users" 
ON public.ai_business_agents_definition 
FOR SELECT 
USING (true);

-- Update the comment on pricing_tiers.allowed_agents column
COMMENT ON COLUMN public.pricing_tiers.allowed_agents IS 'Array of UUIDs referencing ai_business_agents_definition.id';