import React from "react";
import { motion } from "framer-motion";
import TeamMemberCard from "./TeamMemberCard";

interface TeamMember {
  name: string;
  role: string;
  description: string;
  avatar: string;
  videoPlaceholder: string;
  videoId?: string;
}

interface TeamGridProps {
  members: TeamMember[];
}

const TeamGrid: React.FC<TeamGridProps> = ({ members }) => {
  if (members.length !== 5) {
    console.warn("TeamGrid is optimized for exactly 5 team members");
  }

  // First 3 members go in the top row
  const topRowMembers = members.slice(0, 3);
  // Last 2 members go in the bottom row
  const bottomRowMembers = members.slice(3, 5);

  return (
    <div className="space-y-8">
      {/* Top row - 3 members */}
      <div className="grid md:grid-cols-3 gap-6 lg:gap-8">
        {topRowMembers.map((member, index) => (
          <TeamMemberCard
            key={index}
            name={member.name}
            role={member.role}
            description={member.description}
            avatar={member.avatar}
            videoPlaceholder={member.videoPlaceholder}
            videoId={member.videoId}
            delay={0.1 + index * 0.1}
          />
        ))}
      </div>

      {/* Bottom row - 2 members centered */}
      <div className="grid md:grid-cols-2 gap-6 lg:gap-8 max-w-3xl mx-auto">
        {bottomRowMembers.map((member, index) => (
          <TeamMemberCard
            key={index}
            name={member.name}
            role={member.role}
            description={member.description}
            avatar={member.avatar}
            videoPlaceholder={member.videoPlaceholder}
            videoId={member.videoId}
            delay={0.4 + index * 0.1}
          />
        ))}
      </div>
    </div>
  );
};

export default TeamGrid;
