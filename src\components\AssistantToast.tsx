import React, { useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Mic } from "lucide-react";

const AssistantToast = () => {
  const { toast } = useToast();

  useEffect(() => {
    const handleShowToast = () => {
      toast({
        title: "Prova l'Assistente Vocale!",
        description: "Parla con il nostro assistente AI per scoprire come può aiutare la tua attività",
        action: (
          <Button 
            variant="outline" 
            size="sm" 
            className="border-primary-200 bg-primary-50 text-primary-700 hover:bg-primary-100"
            onClick={() => {
              // Scorri fino al componente Agent
              const agentElement = document.getElementById('agent-component');
              if (agentElement) {
                agentElement.scrollIntoView({ behavior: 'smooth' });
                // Aggiungi un effetto di highlight
                agentElement.classList.add('highlight-pulse');
                setTimeout(() => {
                  agentElement.classList.remove('highlight-pulse');
                }, 2000);
              }
            }}
          >
            <Mic className="mr-1 h-4 w-4" />
            Prova ora
          </Button>
        ),
      });
    };

    // Ascolta l'evento personalizzato dal componente Agent
    window.addEventListener('showAssistantToast', handleShowToast);

    return () => {
      window.removeEventListener('showAssistantToast', handleShowToast);
    };
  }, [toast]);

  return null; // Questo componente non renderizza nulla direttamente
};

export default AssistantToast;
