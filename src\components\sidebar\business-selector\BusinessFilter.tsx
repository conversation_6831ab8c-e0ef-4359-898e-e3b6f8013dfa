
import { Checkbox } from "@/components/ui/checkbox";
import { Filter, Tag } from "lucide-react";

interface BusinessFilterProps {
  showWithDeals: boolean;
  toggleShowWithDeals: () => void;
}

export const BusinessFilter = ({ showWithDeals, toggleShowWithDeals }: BusinessFilterProps) => {
  return (
    <div className="px-2 py-1.5">
      <div className="flex items-center mb-1">
        <Filter className="h-4 w-4 mr-1 text-muted-foreground" />
        <span className="text-xs font-medium text-muted-foreground">Filtri</span>
      </div>
      <div className="flex items-center space-x-2 px-1 py-1">
        <Checkbox 
          id="with-deals" 
          checked={showWithDeals}
          onCheckedChange={toggleShowWithDeals}
        />
        <label
          htmlFor="with-deals"
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
        >
          Con offerte
          <Tag className="h-3.5 w-3.5 ml-1 text-blue-600" />
        </label>
      </div>
    </div>
  );
};
