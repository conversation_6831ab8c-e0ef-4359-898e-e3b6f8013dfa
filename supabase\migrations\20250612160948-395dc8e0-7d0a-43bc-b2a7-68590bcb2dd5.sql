
-- Create business_product_categories table
CREATE TABLE public.business_product_categories (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  icon TEXT,
  business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT timezone('utc'::text, now())
);

-- Add unique constraint to prevent duplicate category names per business
ALTER TABLE public.business_product_categories 
ADD CONSTRAINT unique_category_name_per_business 
UNIQUE (business_id, name);

-- Update the business_products table to use UUID for category
-- First, add a new UUID column
ALTER TABLE public.business_products ADD COLUMN category_id UUID;

-- Rename the old text category column to preserve data
ALTER TABLE public.business_products RENAME COLUMN category TO category_text_old;

-- Rename the new UUID column to category
ALTER TABLE public.business_products RENAME COLUMN category_id TO category;

-- Add the foreign key constraint
ALTER TABLE public.business_products 
ADD CONSTRAINT fk_business_products_category 
FOREIGN KEY (category) REFERENCES public.business_product_categories(id);

-- Enable RLS on the new table
ALTER TABLE public.business_product_categories ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for business_product_categories
CREATE POLICY "Users can view categories for their businesses" 
  ON public.business_product_categories 
  FOR SELECT 
  USING (
    business_id IN (
      SELECT id FROM public.businesses WHERE owner_id = auth.uid()
    )
  );

CREATE POLICY "Users can create categories for their businesses" 
  ON public.business_product_categories 
  FOR INSERT 
  WITH CHECK (
    business_id IN (
      SELECT id FROM public.businesses WHERE owner_id = auth.uid()
    )
  );

CREATE POLICY "Users can update categories for their businesses" 
  ON public.business_product_categories 
  FOR UPDATE 
  USING (
    business_id IN (
      SELECT id FROM public.businesses WHERE owner_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete categories for their businesses" 
  ON public.business_product_categories 
  FOR DELETE 
  USING (
    business_id IN (
      SELECT id FROM public.businesses WHERE owner_id = auth.uid()
    )
  );

-- Create index for the new table (only the one that doesn't exist yet)
CREATE INDEX idx_business_product_categories_business_id ON public.business_product_categories(business_id);
