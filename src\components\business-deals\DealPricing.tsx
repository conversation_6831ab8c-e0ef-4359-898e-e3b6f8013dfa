import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useState, useEffect, useMemo } from "react";
import { formatCurrency } from "@/utils/formatters";
import { cn } from "@/lib/utils";

interface DealPricingProps {
  originalPrice: string;
  discountPercentage: string;
  discountedPrice: string;
  onOriginalPriceChange: (value: string) => void;
  onDiscountPercentageChange: (value: string) => void;
  onDiscountedPriceChange?: (value: string) => void;
}

interface ValidationState {
  originalPrice: { isValid: boolean; message?: string };
  discountPercentage: { isValid: boolean; message?: string };
  discountedPrice: { isValid: boolean; message?: string };
}

const DealPricing = ({
  originalPrice,
  discountPercentage,
  discountedPrice,
  onOriginalPriceChange,
  onDiscountPercentageChange,
  onDiscountedPriceChange,
}: DealPricingProps) => {
  const [inputMode, setInputMode] = useState<"discount" | "price">("discount");

  // Validation logic
  const validation = useMemo((): ValidationState => {
    const original = parseFloat(originalPrice);
    const discount = parseFloat(discountPercentage);
    const discounted = parseFloat(discountedPrice);

    const result: ValidationState = {
      originalPrice: { isValid: true },
      discountPercentage: { isValid: true },
      discountedPrice: { isValid: true },
    };

    // Validate original price
    if (!originalPrice || originalPrice.trim() === "") {
      result.originalPrice = { isValid: false, message: "Prezzo originale richiesto" };
    } else if (isNaN(original) || original <= 0) {
      result.originalPrice = { isValid: false, message: "Inserisci un prezzo valido" };
    }

    // Validate based on input mode
    if (inputMode === "discount") {
      // Discount percentage mode
      if (!discountPercentage || discountPercentage.trim() === "") {
        result.discountPercentage = { isValid: false, message: "Sconto richiesto" };
      } else if (isNaN(discount) || discount <= 0 || discount >= 100) {
        result.discountPercentage = { isValid: false, message: "Sconto deve essere tra 1% e 99%" };
      }
    } else {
      // Discounted price mode
      if (!discountedPrice || discountedPrice.trim() === "") {
        result.discountedPrice = { isValid: false, message: "Prezzo scontato richiesto" };
      } else if (isNaN(discounted) || discounted <= 0) {
        result.discountedPrice = { isValid: false, message: "Inserisci un prezzo valido" };
      } else if (!isNaN(original) && original > 0) {
        if (discounted >= original) {
          result.discountedPrice = { isValid: false, message: "Deve essere minore del prezzo originale" };
        } else if (Math.abs(discounted - original) < 0.01) {
          result.discountedPrice = { isValid: false, message: "Non può essere uguale al prezzo originale" };
        }
      }
    }

    return result;
  }, [originalPrice, discountPercentage, discountedPrice, inputMode]);

  // Calculate discounted price from percentage
  const calculateDiscountedPrice = useMemo(() => {
    if (originalPrice && discountPercentage) {
      const original = parseFloat(originalPrice);
      const discount = parseFloat(discountPercentage);
      if (!isNaN(original) && !isNaN(discount) && original > 0) {
        return (original - (original * discount) / 100).toFixed(2);
      }
    }
    return "0.00";
  }, [originalPrice, discountPercentage]);

  // Calculate discount percentage from prices
  const calculateDiscountPercentage = useMemo(() => {
    if (originalPrice && discountedPrice) {
      const original = parseFloat(originalPrice);
      const discounted = parseFloat(discountedPrice);
      if (!isNaN(original) && !isNaN(discounted) && original > 0) {
        return (((original - discounted) / original) * 100).toFixed(2);
      }
    }
    return "0.00";
  }, [originalPrice, discountedPrice]);

  // Handle discount percentage change
  const handleDiscountPercentageChange = (value: string) => {
    onDiscountPercentageChange(value);
    // Auto-calculate discounted price if we have original price
    if (originalPrice && value) {
      const original = parseFloat(originalPrice);
      const discount = parseFloat(value);
      if (!isNaN(original) && !isNaN(discount) && original > 0) {
        const calculated = (original - (original * discount) / 100).toFixed(2);
        onDiscountedPriceChange?.(calculated);
      }
    }
  };

  // Handle discounted price change
  const handleDiscountedPriceChange = (value: string) => {
    onDiscountedPriceChange?.(value);
    // Auto-calculate discount percentage if we have original price
    if (originalPrice && value) {
      const original = parseFloat(originalPrice);
      const discounted = parseFloat(value);
      if (!isNaN(original) && !isNaN(discounted) && original > 0 && discounted < original) {
        const calculated = (((original - discounted) / original) * 100).toFixed(2);
        // Only update if the calculated discount is greater than 0
        if (parseFloat(calculated) > 0) {
          onDiscountPercentageChange(calculated);
        }
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Step 1: Original Price - Always visible */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">
          1. Prezzo Originale <span className="text-destructive">*</span>
        </Label>
        <Input
          type="number"
          step="0.01"
          min="0"
          value={originalPrice}
          onChange={(e) => onOriginalPriceChange(e.target.value)}
          placeholder="€0.00"
          className={cn(
            "transition-colors",
            !validation.originalPrice.isValid && "border-destructive focus-visible:ring-destructive"
          )}
        />
        {!validation.originalPrice.isValid && (
          <p className="text-sm text-destructive">{validation.originalPrice.message}</p>
        )}
      </div>

      {/* Step 2: Input Mode Selection */}
      <div className="space-y-4">
        <Label className="text-sm font-medium">
          2. Scegli come inserire lo sconto
        </Label>
        <ToggleGroup
          className="grid grid-cols-2 w-full"
          variant="outline"
          type="single"
          value={inputMode}
          onValueChange={(value) => value && setInputMode(value as "discount" | "price")}
        >
          <ToggleGroupItem
            value="discount"
            aria-label="Inserisci percentuale sconto"
            className="data-[state=on]:bg-primary data-[state=on]:text-primary-foreground"
          >
            Percentuale Sconto
          </ToggleGroupItem>
          <ToggleGroupItem
            value="price"
            aria-label="Inserisci prezzo scontato"
            className="data-[state=on]:bg-primary data-[state=on]:text-primary-foreground"
          >
            Prezzo Scontato
          </ToggleGroupItem>
        </ToggleGroup>
      </div>

      {/* Step 3: Input Field Based on Mode */}
      {inputMode === "discount" ? (
        <div className="space-y-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              3. Percentuale di Sconto <span className="text-destructive">*</span>
            </Label>
            <Input
              type="number"
              step="0.01"
              min="0.01"
              max="99.99"
              value={discountPercentage}
              onChange={(e) => handleDiscountPercentageChange(e.target.value)}
              placeholder="1%"
              className={cn(
                "transition-colors",
                !validation.discountPercentage.isValid && "border-destructive focus-visible:ring-destructive"
              )}
            />
            {!validation.discountPercentage.isValid && (
              <p className="text-sm text-destructive">{validation.discountPercentage.message}</p>
            )}
          </div>

          {/* Calculated Discounted Price Display */}
          {originalPrice && discountPercentage && validation.originalPrice.isValid && validation.discountPercentage.isValid && (
            <div className="p-4 bg-muted rounded-lg">
              <Label className="text-sm font-semibold">Prezzo Scontato Calcolato</Label>
              <div className="text-2xl font-bold text-primary">
                {formatCurrency(parseFloat(calculateDiscountedPrice))}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              3. Prezzo Scontato <span className="text-destructive">*</span>
            </Label>
            <Input
              type="number"
              step="0.01"
              min="0"
              value={discountedPrice}
              onChange={(e) => handleDiscountedPriceChange(e.target.value)}
              placeholder="€0.00"
              className={cn(
                "transition-colors",
                !validation.discountedPrice.isValid && "border-destructive focus-visible:ring-destructive"
              )}
            />
            {!validation.discountedPrice.isValid && (
              <p className="text-sm text-destructive">{validation.discountedPrice.message}</p>
            )}
          </div>

          {/* Calculated Discount Percentage Display */}
          {originalPrice && discountedPrice && validation.originalPrice.isValid && validation.discountedPrice.isValid && (
            <div className="p-4 bg-muted rounded-lg">
              <Label className="text-sm font-semibold">Sconto Applicato</Label>
              <div className="text-2xl font-bold text-primary">
                {calculateDiscountPercentage}%
              </div>
            </div>
          )}
        </div>
      )}

      {/* Summary Section */}
      {originalPrice && (inputMode === "discount" ? discountPercentage : discountedPrice) &&
       validation.originalPrice.isValid &&
       (inputMode === "discount" ? validation.discountPercentage.isValid : validation.discountedPrice.isValid) && (
        <div className="p-6 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg border border-primary/20">
          <h3 className="text-lg font-semibold mb-4">Riepilogo Offerta</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div>
              <Label className="text-sm text-muted-foreground">Prezzo Originale</Label>
              <div className="text-xl font-bold">{formatCurrency(parseFloat(originalPrice))}</div>
            </div>
            <div>
              <Label className="text-sm text-muted-foreground">Sconto</Label>
              <div className="text-xl font-bold text-destructive">
                -{inputMode === "discount" ? discountPercentage : calculateDiscountPercentage}%
              </div>
            </div>
            <div>
              <Label className="text-sm text-muted-foreground">Prezzo Finale</Label>
              <div className="text-2xl font-bold text-primary">
                {formatCurrency(parseFloat(inputMode === "discount" ? calculateDiscountedPrice : discountedPrice))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DealPricing;