import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Clock, MapPin, Euro, Calendar, Route } from 'lucide-react';

interface MultiServicePlan {
  id: string;
  services: Array<{
    service_type: string;
    deal: {
      title: string;
      business_name: string;
      business_address: string;
      discounted_price: number;
    };
    scheduled_time: string;
    travel_time_minutes: number;
    cost: number;
  }>;
  total_cost: number;
  total_time_minutes: number;
  savings?: number;
}

interface MultiServicePlannerProps {
  plan: MultiServicePlan;
  onBook: (planId: string) => void;
  onModify: (planId: string) => void;
}

export const MultiServicePlanner: React.FC<MultiServicePlannerProps> = ({
  plan,
  onBook,
  onModify
}) => {
  const formatTime = (timeString: string) => {
    return timeString.substring(0, 5); // Extract HH:MM
  };

  const getTravelIcon = (minutes: number) => {
    if (minutes <= 10) return '🚶‍♂️';
    if (minutes <= 30) return '🚗';
    return '🚌';
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Piano Multi-Servizio
        </CardTitle>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Clock className="h-4 w-4" />
            {Math.floor(plan.total_time_minutes / 60)}h {plan.total_time_minutes % 60}m
          </div>
          <div className="flex items-center gap-1">
            <Euro className="h-4 w-4" />
            €{plan.total_cost}
          </div>
          {plan.savings && plan.savings > 0 && (
            <Badge variant="secondary" className="bg-green-100 text-green-700">
              Risparmi €{plan.savings}
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Timeline */}
        <div className="space-y-4">
          {plan.services.map((service, index) => (
            <div key={index} className="relative">
              {/* Timeline connector */}
              {index < plan.services.length - 1 && (
                <div className="absolute left-6 top-16 w-0.5 h-8 bg-border"></div>
              )}
              
              <div className="flex gap-4">
                {/* Time badge */}
                <div className="flex-shrink-0">
                  <Badge variant="outline" className="bg-primary/10">
                    {formatTime(service.scheduled_time)}
                  </Badge>
                </div>

                {/* Service details */}
                <div className="flex-1 space-y-2">
                  <div className="flex items-start justify-between">
                    <div>
                      <h4 className="font-medium">{service.deal.title}</h4>
                      <p className="text-sm text-muted-foreground">
                        {service.deal.business_name}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">€{service.cost}</p>
                      <Badge variant="secondary" className="text-xs">
                        {service.service_type}
                      </Badge>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <MapPin className="h-3 w-3" />
                    {service.deal.business_address}
                  </div>

                  {/* Travel time to next service */}
                  {index < plan.services.length - 1 && service.travel_time_minutes > 0 && (
                    <div className="flex items-center gap-2 text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded">
                      <Route className="h-3 w-3" />
                      {getTravelIcon(service.travel_time_minutes)} {service.travel_time_minutes} min al prossimo servizio
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Summary */}
        <div className="border-t pt-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">
                {plan.services.length} servizi programmati
              </p>
              <p className="font-medium">
                Totale: €{plan.total_cost}
                {plan.savings && plan.savings > 0 && (
                  <span className="text-green-600 ml-2">
                    (Risparmi €{plan.savings})
                  </span>
                )}
              </p>
            </div>
            
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => onModify(plan.id)}>
                Modifica
              </Button>
              <Button onClick={() => onBook(plan.id)}>
                Prenota Tutto
              </Button>
            </div>
          </div>
        </div>

        {/* Map placeholder */}
        <div className="border rounded-lg p-4 bg-muted/20">
          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
            <MapPin className="h-4 w-4" />
            Percorso Ottimizzato
          </div>
          <div className="h-32 bg-gradient-to-br from-blue-100 to-green-100 rounded flex items-center justify-center text-sm text-muted-foreground">
            🗺️ Mappa del percorso (in sviluppo)
          </div>
        </div>
      </CardContent>
    </Card>
  );
};