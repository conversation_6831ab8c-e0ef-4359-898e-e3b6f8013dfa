import { useState, useEffect, useMemo } from "react";
import { supabase } from "@/integrations/supabase/client";
import MainLayout from "@/layouts/MainLayout";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { toast } from "sonner";
import {
  Clock,
  ChevronLeft,
  ChevronRight,
  Search,
  Filter,
  TestTube,
  Edit,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { But<PERSON> } from "@/components/ui/button";
import { AdminDealEditModal } from "@/components/admin/AdminDealEditModal";

interface DealCategory {
  id: string;
  name: string;
  category_id: string;
}

interface DealWithInfo {
  id: string;
  title: string;
  description: string | null;
  original_price: number;
  discounted_price: number;
  discount_percentage: number | null;
  start_date: string;
  end_date: string;
  status: "draft" | "published" | "expired";
  business_name: string;
  business_address: string | null;
  category_name: string | null;
  time_slots: any; // JSON data from database
  fake?: boolean; // Add fake property
  deal_categories?: DealCategory[];
}

const AdminDeals = () => {
  const [deals, setDeals] = useState<DealWithInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [cityFilter, setCityFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [categories, setCategories] = useState<{ id: string; name: string }[]>(
    []
  );
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedDeal, setSelectedDeal] = useState<DealWithInfo | null>(null);

  useEffect(() => {
    fetchDeals();
    fetchCategories();
  }, []);

  const fetchDeals = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("deals_with_info")
        .select(`*`)
        .order("start_date", { ascending: false });

      if (error) throw error;

      // Fetch deal categories for each deal
      const dealIds = data?.map((deal) => deal.id) || [];
      let dealCategoriesMap: Record<string, DealCategory[]> = {};

      if (dealIds.length > 0) {
        const { data: dealCategoriesData, error: categoriesError } =
          await supabase
            .from("deals_deal_categories")
            .select(
              `
            deal_id,
            deal_categories!inner(id, name, category_id)
          `
            )
            .in("deal_id", dealIds);

        if (!categoriesError && dealCategoriesData) {
          dealCategoriesMap = dealCategoriesData.reduce((acc, item) => {
            if (!acc[item.deal_id]) {
              acc[item.deal_id] = [];
            }
            acc[item.deal_id].push({
              id: item.deal_categories.id,
              name: item.deal_categories.name,
              category_id: item.deal_categories.category_id,
            });
            return acc;
          }, {} as Record<string, DealCategory[]>);
        }
      }

      // Transform the data to match our interface
      const transformedData = (data || []).map((deal) => ({
        id: deal.id,
        title: deal.title,
        description: deal.description,
        original_price: deal.original_price,
        discounted_price: deal.discounted_price,
        discount_percentage: deal.discount_percentage,
        start_date: deal.start_date,
        end_date: deal.end_date,
        status: deal.status,
        time_slots: deal.time_slots,
        fake: deal.fake,
        business_name: deal.business_name || "",
        business_address:
          deal.business_address +
            ", " +
            deal.business_city +
            ", " +
            deal.business_state +
            ", " +
            deal.business_country || "",
        category_name: deal.category_name || null,
        deal_categories: dealCategoriesMap[deal.id] || [],
      }));

      setDeals(transformedData);
    } catch (error) {
      console.error("Errore nel caricamento delle offerte:", error);
      toast.error("Errore nel caricamento delle offerte");
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from("categories")
        .select("id, name")
        .order("name");

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  // Get unique cities for filter
  const uniqueCities = useMemo(() => {
    const cities = deals
      .map((deal) => deal.business_address?.split(",")[0]?.trim()) // Extract city from address
      .filter(Boolean)
      .filter((city, index, array) => array.indexOf(city) === index)
      .sort();
    return cities;
  }, [deals]);

  // Filter and search deals
  const filteredDeals = useMemo(() => {
    return deals.filter((deal) => {
      const matchesSearch =
        !searchTerm ||
        deal.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        deal.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        deal.business_name?.toLowerCase().includes(searchTerm.toLowerCase());

      const dealCity = deal.business_address?.split(",")[0]?.trim();
      const matchesCity =
        !cityFilter || cityFilter === "all" || dealCity === cityFilter;

      const matchesCategory =
        !categoryFilter ||
        categoryFilter === "all" ||
        deal.category_name === categoryFilter;

      return matchesSearch && matchesCity && matchesCategory;
    });
  }, [deals, searchTerm, cityFilter, categoryFilter]);

  // Pagination logic
  const totalPages = Math.ceil(filteredDeals.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedDeals = useMemo(() => {
    return filteredDeals.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredDeals, startIndex, itemsPerPage]);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, cityFilter, categoryFilter]);

  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const clearFilters = () => {
    setSearchTerm("");
    setCityFilter("all");
    setCategoryFilter("all");
    setCurrentPage(1);
  };

  const handleEditDeal = (deal: DealWithInfo) => {
    setSelectedDeal(deal);
    setIsEditModalOpen(true);
  };

  const handleEditModalClose = () => {
    setIsEditModalOpen(false);
    setSelectedDeal(null);
  };

  const handleEditModalSuccess = () => {
    fetchDeals(); // Refresh the deals list
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "published":
        return <Badge variant="default">Pubblicata</Badge>;
      case "draft":
        return <Badge variant="secondary">Bozza</Badge>;
      case "expired":
        return <Badge variant="destructive">Scaduta</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const renderTimeSlots = (timeSlots: any) => {
    if (!timeSlots || !timeSlots.schedule) {
      return <span className="text-muted-foreground">-</span>;
    }

    const schedule = timeSlots.schedule;
    const activeDays = schedule.filter(
      (day: any) => day.time_slots && day.time_slots.length > 0
    );

    if (activeDays.length === 0) {
      return <span className="text-muted-foreground">Nessun orario</span>;
    }

    // Get day abbreviations (L, M, M, G, V, S, D)
    const dayAbbreviations = ["D", "L", "M", "M", "G", "V", "S"];

    // Create tooltip content with detailed schedule
    const tooltipContent = (
      <div className="space-y-2 max-w-xs">
        {activeDays.map((day: any) => (
          <div key={day.day}>
            <div className="font-medium">{day.day_name}</div>
            <div className="space-y-1">
              {day.time_slots.map((slot: any, index: number) => (
                <div key={index} className="text-sm text-muted-foreground">
                  {slot.start_time} - {slot.end_time} ({slot.available_seats}{" "}
                  posti)
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    );

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center gap-2 cursor-help">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <div className="flex gap-1">
                {schedule.map((day: any) => {
                  const hasSlots = day.time_slots && day.time_slots.length > 0;
                  return (
                    <span
                      key={day.day}
                      className={`inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium ${
                        hasSlots
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted text-muted-foreground"
                      }`}
                    >
                      {dayAbbreviations[day.day === 7 ? 0 : day.day]}
                    </span>
                  );
                })}
              </div>
              <div className="text-sm text-muted-foreground">
                {activeDays.length}{" "}
                {activeDays.length === 1 ? "giorno" : "giorni"}
              </div>
            </div>
          </TooltipTrigger>
          <TooltipContent className="bg-popover border">
            {tooltipContent}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Caricamento...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Gestione Offerte</h1>
          <p className="text-muted-foreground">
            Visualizza e gestisci tutte le offerte della piattaforma
          </p>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filtri e Ricerca
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Cerca per titolo, descrizione o business..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="w-full sm:w-48">
                <Select value={cityFilter} onValueChange={setCityFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filtra per città" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tutte le città</SelectItem>
                    {uniqueCities.map((city) => (
                      <SelectItem key={city} value={city}>
                        {city}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full sm:w-48">
                <Select
                  value={categoryFilter}
                  onValueChange={setCategoryFilter}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Filtra per categoria" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tutte le categorie</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.name}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Button variant="outline" onClick={clearFilters}>
                Pulisci Filtri
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Tutte le Offerte ({filteredDeals.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Titolo</TableHead>
                    <TableHead>Business</TableHead>
   
                    <TableHead>Deal Categories</TableHead>
                    <TableHead>Prezzo Orig.</TableHead>
                    <TableHead>Prezzo Scont.</TableHead>
                    <TableHead>Sconto %</TableHead>
                    <TableHead>Orari</TableHead>
                    <TableHead>Periodo</TableHead>
                    <TableHead>Stato</TableHead>
                    <TableHead className="text-right">Azioni</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDeals.length === 0 ? (
                    <TableRow>
                      <TableCell
                        colSpan={11}
                        className="text-center py-8 text-muted-foreground"
                      >
                        {searchTerm ||
                        cityFilter !== "all" ||
                        categoryFilter !== "all"
                          ? "Nessuna offerta trovata con i filtri applicati"
                          : "Nessuna offerta trovata"}
                      </TableCell>
                    </TableRow>
                  ) : (
                    paginatedDeals.map((deal) => (
                      <TableRow key={deal.id}>
                        <TableCell>
                          <div>
                            <div
                              className={`font-medium ${
                                deal.fake ? "text-muted-foreground" : ""
                              }`}
                            >
                              <div className="flex items-center gap-2">
                                {deal.title}

                                {deal.fake && (
                                  <TestTube className="h-4 w-4 text-orange-500" />
                                )}
                              </div>
                            </div>
                            {deal.description && (
                              <div className="text-sm text-muted-foreground truncate max-w-xs">
                                {deal.description}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {deal.business_name}
                            </div>
                            {deal.business_address && (
                              <>
                                <div className="text-sm text-muted-foreground">
                                  {deal.business_address}
                                </div>
                                <div>
                                  {deal.category_name ? (
                                    <Badge variant="outline">
                                      {deal.category_name}
                                    </Badge>
                                  ) : (
                                    <span className="text-muted-foreground">
                                      -
                                    </span>
                                  )}
                                </div>
                              </>
                            )}
                          </div>
                        </TableCell>
                    
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {deal.deal_categories &&
                            deal.deal_categories.length > 0 ? (
                              deal.deal_categories.map((category) => (
                                <Badge
                                  key={category.id}
                                  variant="secondary"
                                  className="text-xs"
                                >
                                  {category.name}
                                </Badge>
                              ))
                            ) : (
                              <span className="text-muted-foreground text-sm">
                                -
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {deal.original_price === 0 &&
                          deal.discounted_price === 0
                            ? ".."
                            : `€${deal.original_price.toFixed(2)}`}
                        </TableCell>
                        <TableCell className="font-medium text-primary">
                          {deal.original_price === 0 &&
                          deal.discounted_price === 0
                            ? ".."
                            : `€${deal.discounted_price.toFixed(2)}`}
                        </TableCell>
                        <TableCell>
                          {deal.discount_percentage ? (
                            <Badge variant="secondary">
                              {deal.discount_percentage.toFixed(0)}%
                            </Badge>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {renderTimeSlots(deal.time_slots)}
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>
                              {format(new Date(deal.start_date), "dd/MM/yyyy", {
                                locale: it,
                              })}
                            </div>
                            <div className="text-muted-foreground">
                              {format(new Date(deal.end_date), "dd/MM/yyyy", {
                                locale: it,
                              })}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{getStatusBadge(deal.status)}</TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditDeal(deal)}
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {filteredDeals.length > itemsPerPage && (
              <div className="flex items-center justify-between px-2 py-4">
                <div className="text-sm text-muted-foreground">
                  Mostrando {startIndex + 1}-
                  {Math.min(startIndex + itemsPerPage, filteredDeals.length)} di{" "}
                  {filteredDeals.length} offerte
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePreviousPage}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Precedente
                  </Button>
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: totalPages }, (_, i) => i + 1)
                      .filter(
                        (page) =>
                          page === 1 ||
                          page === totalPages ||
                          Math.abs(page - currentPage) <= 1
                      )
                      .map((page, index, array) => (
                        <div key={page} className="flex items-center">
                          {index > 0 && array[index - 1] !== page - 1 && (
                            <span className="px-2 text-muted-foreground">
                              ...
                            </span>
                          )}
                          <Button
                            variant={
                              currentPage === page ? "default" : "outline"
                            }
                            size="sm"
                            onClick={() => setCurrentPage(page)}
                            className="min-w-8"
                          >
                            {page}
                          </Button>
                        </div>
                      ))}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleNextPage}
                    disabled={currentPage === totalPages}
                  >
                    Successivo
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <AdminDealEditModal
          isOpen={isEditModalOpen}
          onClose={handleEditModalClose}
          deal={selectedDeal}
          onSuccess={handleEditModalSuccess}
        />
      </div>
    </MainLayout>
  );
};

export default AdminDeals;
