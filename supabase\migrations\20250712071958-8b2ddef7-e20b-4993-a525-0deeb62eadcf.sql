-- Create function for semantic search
CREATE OR REPLACE FUNCTION search_deals_semantic(
  query_embedding vector(1536),
  similarity_threshold float DEFAULT 0.7,
  match_count int DEFAULT 10
)
RETURNS TABLE (
  deal_id uuid,
  title text,
  description text,
  original_price numeric,
  discounted_price numeric,
  business_name text,
  business_city text,
  business_address text,
  business_latitude double precision,
  business_longitude double precision,
  category_id uuid,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id as deal_id,
    d.title,
    d.description,
    d.original_price,
    d.discounted_price,
    b.name as business_name,
    b.city as business_city,
    b.address as business_address,
    b.latitude as business_latitude,
    b.longitude as business_longitude,
    d.category_id,
    1 - (de.embedding <=> query_embedding) as similarity
  FROM deal_embeddings de
  JOIN deals d ON de.deal_id = d.id
  JOIN businesses b ON d.business_id = b.id
  WHERE 
    d.status = 'published'
    AND d.start_date <= CURRENT_DATE
    AND d.end_date >= CURRENT_DATE
    AND 1 - (de.embedding <=> query_embedding) > similarity_threshold
  ORDER BY de.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;