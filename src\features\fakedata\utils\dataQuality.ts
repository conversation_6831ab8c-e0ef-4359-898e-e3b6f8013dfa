import { faker } from '@faker-js/faker';

// Data quality utilities for making synthetic data more realistic

export class DataQualityUtils {
  
  // Introduce realistic minor inconsistencies (like real-world data)
  static introduceRealisticErrors<T extends Record<string, any>>(data: T, errorRate: number = 0.05): T {
    const result = { ...data } as T;
    
    // Small chance of minor errors in each field
    Object.keys(result).forEach(key => {
      if (Math.random() < errorRate) {
        (result as any)[key] = this.introduceFieldError((result as any)[key]);
      }
    });
    
    return result;
  }
  
  private static introduceFieldError(value: any): any {
    if (typeof value === 'string') {
      // Minor typos or formatting issues
      const errorTypes = ['extraSpace', 'missingSpace', 'caseChange'];
      const errorType = faker.helpers.arrayElement(errorTypes);
      
      switch (errorType) {
        case 'extraSpace':
          return value + ' '; // Extra trailing space
        case 'missingSpace':
          return value.replace(' ', ''); // Remove a space
        case 'caseChange':
          // Random case change in one character
          const pos = faker.datatype.number({ min: 0, max: value.length - 1 });
          const chars = value.split('');
          chars[pos] = Math.random() > 0.5 ? chars[pos].toUpperCase() : chars[pos].toLowerCase();
          return chars.join('');
        default:
          return value;
      }
    } else if (typeof value === 'number') {
      // Small rounding errors
      return Math.round(value * 100) / 100;
    }
    
    return value;
  }
  
  // Validate data consistency across related fields
  static validateDataConsistency(data: any[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    data.forEach((item, index) => {
      // Check email format consistency
      if (item.email && !this.isValidEmail(item.email)) {
        errors.push(`Item ${index}: Invalid email format`);
      }
      
      // Check phone format consistency
      if (item.phone && !this.isValidItalianPhone(item.phone)) {
        errors.push(`Item ${index}: Invalid Italian phone format`);
      }
      
      // Check address consistency
      if (item.city && item.zip_code && !this.isConsistentCityZip(item.city, item.zip_code)) {
        errors.push(`Item ${index}: City and ZIP code don't match`);
      }
      
      // Check price consistency
      if (item.original_price && item.discounted_price) {
        if (item.discounted_price >= item.original_price) {
          errors.push(`Item ${index}: Discounted price should be less than original`);
        }
      }
    });
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  
  private static isValidItalianPhone(phone: string): boolean {
    // Italian phone number patterns
    const patterns = [
      /^\+39\s\d{3}\s\d{3}\s\d{4}$/, // Mobile: +39 ************
      /^\+39\s\d{2}\s\d{7,8}$/, // Landline: +39 02 12345678
      /^\d{3}\s\d{3}\s\d{4}$/, // Mobile without country code: ************
      /^\d{2}\s\d{7,8}$/ // Landline without country code: 02 12345678
    ];
    
    return patterns.some(pattern => pattern.test(phone));
  }
  
  private static isConsistentCityZip(city: string, zipCode: string): boolean {
    // Basic ZIP code validation for major Italian cities
    const cityZipPrefixes: Record<string, string[]> = {
      'Milano': ['201'],
      'Roma': ['001'],
      'Napoli': ['801'],
      'Firenze': ['501'],
      'Torino': ['101'],
      'Bologna': ['401'],
      'Venezia': ['301']
    };
    
    const prefixes = cityZipPrefixes[city];
    if (!prefixes) return true; // Unknown city, assume valid
    
    return prefixes.some(prefix => zipCode.startsWith(prefix));
  }
  
  // Age synthetic data to simulate changes over time
  static ageData(data: any, ageInDays: number): any {
    const aged = { ...data };
    
    // Update timestamps
    if (aged.created_at) {
      const createdDate = new Date(aged.created_at);
      createdDate.setDate(createdDate.getDate() - ageInDays);
      aged.created_at = createdDate.toISOString();
    }
    
    if (aged.updated_at) {
      // Updated at should be between created and now
      const createdDate = new Date(aged.created_at || Date.now());
      const updatedDate = faker.date.between(createdDate, new Date());
      aged.updated_at = updatedDate.toISOString();
    }
    
    // Simulate business changes over time
    if (aged.photos && ageInDays > 365) {
      // Businesses older than a year might have more photos
      const additionalPhotos = faker.datatype.number({ min: 0, max: 2 });
      for (let i = 0; i < additionalPhotos; i++) {
        aged.photos.push(faker.image.business(640, 480, true));
      }
    }
    
    return aged;
  }
  
  // Generate realistic data relationships
  static createDataRelationships(businesses: any[], deals: any[], bookings: any[]): void {
    // Ensure some businesses have multiple deals
    businesses.forEach(business => {
      const businessDeals = deals.filter(deal => deal.business_id === business.id);
      
      // Successful businesses should have more deals
      if (business._success_level === 'high' && businessDeals.length < 2) {
        // Note: This just marks for additional deal generation
        business._needs_more_deals = true;
      }
    });
    
    // Ensure customer loyalty patterns
    const userBookings = this.groupBy(bookings, 'user_id');
    
    Object.values(userBookings).forEach((userBookingList: any) => {
      if (userBookingList.length > 3) {
        // Create loyalty pattern - some repeat bookings at same businesses
        const businessIds = userBookingList.map((b: any) => b.business_id);
        const uniqueBusinesses = [...new Set(businessIds)];
        
        if (uniqueBusinesses.length > businessIds.length * 0.7) {
          // Too diverse, create some loyalty
          const favoriteBusinessId = faker.helpers.arrayElement(uniqueBusinesses);
          
          // Mark some bookings for the favorite business
          userBookingList
            .slice(1, 3)
            .forEach((booking: any) => {
              booking._should_be_at_favorite = favoriteBusinessId;
            });
        }
      }
    });
  }
  
  private static groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
    return array.reduce((groups, item) => {
      const group = String(item[key]);
      groups[group] = groups[group] || [];
      groups[group].push(item);
      return groups;
    }, {} as Record<string, T[]>);
  }
  
  // Calculate realistic data statistics
  static calculateDataStats(data: any[]): Record<string, any> {
    if (data.length === 0) return {};
    
    const stats: Record<string, any> = {
      total: data.length,
      fields: {}
    };
    
    // Analyze each field
    const firstItem = data[0];
    Object.keys(firstItem).forEach(field => {
      const values = data.map(item => item[field]).filter(v => v != null);
      
      if (values.length === 0) return;
      
      const fieldStats: any = {
        completeness: values.length / data.length,
        unique: new Set(values).size
      };
      
      if (typeof values[0] === 'number') {
        fieldStats.min = Math.min(...values);
        fieldStats.max = Math.max(...values);
        fieldStats.avg = values.reduce((sum, v) => sum + v, 0) / values.length;
      } else if (typeof values[0] === 'string') {
        fieldStats.avgLength = values.reduce((sum, v) => sum + v.length, 0) / values.length;
        fieldStats.patterns = this.analyzeStringPatterns(values.slice(0, 100)); // Sample first 100
      }
      
      stats.fields[field] = fieldStats;
    });
    
    return stats;
  }
  
  private static analyzeStringPatterns(values: string[]): Record<string, number> {
    const patterns: Record<string, number> = {};
    
    values.forEach(value => {
      // Email pattern
      if (value.includes('@')) {
        patterns.email = (patterns.email || 0) + 1;
      }
      
      // Phone pattern
      if (/[\d\s\+\-\(\)]{8,}/.test(value)) {
        patterns.phone = (patterns.phone || 0) + 1;
      }
      
      // URL pattern
      if (value.startsWith('http')) {
        patterns.url = (patterns.url || 0) + 1;
      }
      
      // Italian words
      if (/[àèéìíîòóù]/.test(value)) {
        patterns.italian = (patterns.italian || 0) + 1;
      }
    });
    
    return patterns;
  }
}