/**
 * <PERSON><PERSON><PERSON>, super<PERSON><PERSON><PERSON>, negozi di cibo names
 */
export const alimentariNames: string[] = [
  "Alimentari Da Mario",
  "Macelleria Rossi",
  "Panificio Il Forno",
  "Gastronomia Italiana",
  "Pastificio Artigianale",
  "Pescheria Del Mare",
  "Salumeria Tradizionale",
  "Fruttivendolo Il Raccolto",
  "Enoteca Vini & Sapori",
  "Negozio del Gusto",
  "Mercato di Via Roma",
  "Emporio Alimentare",
  "Delizie d'Italia",
  "Bottega del Cibo",
  "Alimentari La Bontà",
  "Spesa & Sapori",
  "Mercato Fresco",
  "Il Salumiere",
  "La Dispensa Italiana",
  "Cibo & Sapori",
  "Alimentari Il Sapore",
  "Bottega Gourmet",
  "Emporio del Gusto",
  "Negozio di Delizie",
  "La Casa del Cibo",
  "Mercato della Bontà",
  "Sapori & Sapienza",
  "Delizie del Mercato",
  "Cibo Fresco",
  "Il Gourmet Alimentari",
  "Emporio del Sapore",
  "Bottega dei Sapori",
  "Negozio Saporito",
  "Il Mercato dei Sapori",
  "Cucina e Mercato",
  "Sapori d'Orto",
  "Gastronomia del Centro",
  "Alimentari La Tradizione",
  "Il Bazar del Cibo",
  "Mercato delle Specialità",
  "Sapori e Tradizioni",
  "Bottega del Gusto",
  "Emporio Fresco",
  "La Dispensa Moderna",
  "Negozio delle Prelibatezze",
  "Il Cestino del Gusto",
  "Bottega di Sapori",
  "Mercato d'Italia",
  "Emporio della Tradizione",
  "Alimentari La Freschezza",
]; 