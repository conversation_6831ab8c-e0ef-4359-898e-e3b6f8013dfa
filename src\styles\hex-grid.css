/* Hexagon Grid Layout */
.hex-grid {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 auto;
  max-width: 900px;
  padding: 20px 0;
  position: relative;
  overflow: visible;
}

.hex-row {
  display: flex;
  justify-content: center;
  width: 100%;
  position: relative;
  margin: 0 auto;
}

.hex-row.second-row {
  margin-top: -35px; /* Adjusted for 2-3 layout */
}

.hex-item {
  position: relative;
  width: 160px;
  height: 185px; /* Height = width * 1.1547 for perfect hexagon */
  margin: 0 15px 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
  overflow: hidden;
  z-index: 1;
}

.hex-item:hover {
  transform: scale(1.05);
  z-index: 3;
  box-shadow: 0 0 20px rgba(101, 31, 255, 0.4);
}

.hex-item.featured {
  width: 180px;
  height: 208px;
  margin-bottom: 35px;
  animation: pulse 3s infinite;
  z-index: 2;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(101, 31, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(101, 31, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(101, 31, 255, 0);
  }
}

.hex-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center 20%;
}

.hex-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 15px; /* Move content up a bit */
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.5) 50%, rgba(0, 0, 0, 0.3) 100%);
  transition: all 0.3s ease;
}

.hex-item.featured .hex-overlay {
  background: linear-gradient(to top, rgba(101, 31, 255, 0.9) 0%, rgba(101, 31, 255, 0.7) 60%, rgba(101, 31, 255, 0.4) 100%);
}

.hex-item:hover .hex-overlay {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.7) 60%, rgba(0, 0, 0, 0.4) 100%);
}

.hex-item.featured:hover .hex-overlay {
  background: linear-gradient(to top, rgba(101, 31, 255, 1) 0%, rgba(101, 31, 255, 0.8) 60%, rgba(101, 31, 255, 0.6) 100%);
}

.hex-text {
  text-align: center;
  color: white;
  z-index: 2;
  padding: 0 10px;
  width: 100%;
  position: relative;
  top: 10px; /* Move text up a bit */
}

.hex-name {
  font-weight: bold;
  font-size: 1.2rem;
  margin-bottom: 8px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9);
}

.hex-role {
  font-size: 0.85rem;
  opacity: 0.95;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9);
  padding: 0 5px;
  line-height: 1.2;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hex-grid {
    padding: 10px 0;
  }

  .hex-item {
    width: 120px;
    height: 139px;
    margin: 0 10px 20px;
  }

  .hex-item.featured {
    width: 140px;
    height: 162px;
    margin-bottom: 25px;
  }

  .hex-row.second-row {
    margin-top: -25px;
  }

  .hex-name {
    font-size: 0.9rem;
    margin-bottom: 6px;
  }

  .hex-role {
    font-size: 0.7rem;
    line-height: 1.1;
  }

  .hex-text {
    top: 5px;
  }
}

/* Small mobile screens */
@media (max-width: 480px) {
  .hex-grid {
    max-width: 320px;
    padding: 5px 0;
  }

  .hex-item {
    width: 100px;
    height: 115px;
    margin: 0 5px 20px;
  }

  .hex-item.featured {
    width: 120px;
    height: 138px;
    margin-bottom: 20px;
  }

  .hex-row.second-row {
    margin-top: -15px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .hex-row.second-row .hex-item:nth-child(3) {
    margin-top: 20px;
  }

  .hex-name {
    font-size: 0.75rem;
    margin-bottom: 3px;
  }

  .hex-role {
    font-size: 0.6rem;
    line-height: 1;
    padding: 0 2px;
  }

  .hex-text {
    top: 0;
  }
}

/* Extra small mobile screens */
@media (max-width: 360px) {
  .hex-grid {
    max-width: 280px;
  }

  .hex-item {
    width: 90px;
    height: 104px;
    margin: 0 4px 15px;
  }

  .hex-item.featured {
    width: 110px;
    height: 127px;
  }

  .hex-name {
    font-size: 0.7rem;
  }
}
