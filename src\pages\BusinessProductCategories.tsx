
import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Plus, Edit, Trash2 } from "lucide-react";
import MainLayout from "@/layouts/MainLayout";
import { toast } from "sonner";
import { BusinessProductCategory } from "@/types/types";
import { useBusinessStore } from "@/store/businessStore";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { BusinessProductCategoryModal } from "@/components/business-product-categories/BusinessProductCategoryModal";

const BusinessProductCategories = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<BusinessProductCategory | null>(null);
  const [deletingCategory, setDeletingCategory] = useState<BusinessProductCategory | null>(null);
  const queryClient = useQueryClient();
  const selectedBusiness = useBusinessStore((state) => state.selectedBusiness);

  // Fetch business product categories
  const { data: categories, isLoading } = useQuery({
    queryKey: ["business-product-categories", selectedBusiness?.id],
    queryFn: async () => {
      if (!selectedBusiness?.id) return [];
      
      const { data, error } = await supabase
        .from("business_product_categories")
        .select("*")
        .eq("business_id", selectedBusiness.id)
        .order("name");

      if (error) {
        toast.error("Errore nel caricamento delle categorie prodotto");
        throw error;
      }

      return data as BusinessProductCategory[];
    },
    enabled: !!selectedBusiness?.id,
  });

  // Delete category mutation
  const deleteMutation = useMutation({
    mutationFn: async (categoryId: string) => {
      const { error } = await supabase
        .from("business_product_categories")
        .delete()
        .eq("id", categoryId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["business-product-categories", selectedBusiness?.id] });
      toast.success("Categoria prodotto eliminata con successo");
      setDeletingCategory(null);
    },
    onError: (error) => {
      console.error("Error deleting business product category:", error);
      toast.error("Errore nell'eliminazione della categoria prodotto");
    },
  });

  const handleNewCategory = () => {
    setEditingCategory(null);
    setIsModalOpen(true);
  };

  const handleEditCategory = (category: BusinessProductCategory) => {
    setEditingCategory(category);
    setIsModalOpen(true);
  };

  const handleDeleteCategory = (category: BusinessProductCategory) => {
    setDeletingCategory(category);
  };

  const confirmDelete = () => {
    if (deletingCategory) {
      deleteMutation.mutate(deletingCategory.id);
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingCategory(null);
  };

  if (!selectedBusiness) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          <div className="text-center">
            <h1 className="text-2xl font-semibold mb-4">Categorie Prodotti</h1>
            <p className="text-gray-600">Seleziona un'attività per gestire le categorie prodotti</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-semibold">Categorie Prodotti</h1>
            <p className="text-gray-600">Gestisci le categorie per i prodotti di {selectedBusiness.name}</p>
          </div>
          <Button
            onClick={handleNewCategory}
            className="bg-primary-600 hover:bg-primary-700 text-white"
          >
            <Plus className="mr-2 h-4 w-4" /> Nuova Categoria Prodotto
          </Button>
        </div>

        <div className="bg-white rounded-lg shadow">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nome</TableHead>
                <TableHead>Descrizione</TableHead>
                <TableHead>Icona</TableHead>
                <TableHead>Creata il</TableHead>
                <TableHead className="text-right">Azioni</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8">
                    Caricamento...
                  </TableCell>
                </TableRow>
              ) : categories?.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8">
                    Nessuna categoria prodotto trovata per questa attività
                  </TableCell>
                </TableRow>
              ) : (
                categories?.map((category) => (
                  <TableRow key={category.id}>
                    <TableCell className="font-medium">{category.name}</TableCell>
                    <TableCell>{category.description || "-"}</TableCell>
                    <TableCell>{category.icon || "-"}</TableCell>
                    <TableCell>
                      {new Date(category.created_at).toLocaleDateString("it-IT")}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditCategory(category)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteCategory(category)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Category Modal */}
        <BusinessProductCategoryModal
          isOpen={isModalOpen}
          onClose={handleModalClose}
          category={editingCategory}
          businessId={selectedBusiness.id}
        />

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={!!deletingCategory} onOpenChange={() => setDeletingCategory(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Conferma eliminazione</AlertDialogTitle>
              <AlertDialogDescription>
                Sei sicuro di voler eliminare la categoria prodotto "{deletingCategory?.name}"?
                Questa azione non può essere annullata e rimuoverà il collegamento dai prodotti associati.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Annulla</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDelete}
                className="bg-red-600 hover:bg-red-700"
              >
                Elimina
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </MainLayout>
  );
};

export default BusinessProductCategories;
