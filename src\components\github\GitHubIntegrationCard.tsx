
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Github, Settings, ExternalLink, Users, Lock } from 'lucide-react';
import { GitHubIntegration, GitHubRepository, GitHubRepoData } from '@/types/github';
import { GitHubRepositorySelector } from './GitHubRepositorySelector';
import { useGitHubIntegration } from '@/hooks/useGitHubIntegration';

interface GitHubIntegrationCardProps {
  businessId: string;
  integration?: GitHubIntegration | null;
  onIntegrationChange?: () => void;
}

export const GitHubIntegrationCard = ({
  businessId,
  integration,
  onIntegrationChange
}: GitHubIntegrationCardProps) => {
  const [showRepositorySelector, setShowRepositorySelector] = useState(false);
  const [repositories, setRepositories] = useState<GitHubRepoData[]>([]);
  const [selectedRepositories, setSelectedRepositories] = useState<GitHubRepository[]>([]);
  const [loadingRepos, setLoadingRepos] = useState(false);

  const {
    loading,
    initiateGitHubOAuth,
    getSelectedRepositories,
    updateRepositorySelection,
    revokeGitHubIntegration
  } = useGitHubIntegration();

  useEffect(() => {
    if (integration) {
      loadSelectedRepositories();
    }
  }, [integration]);

  const loadSelectedRepositories = async () => {
    if (!integration) return;
    const repos = await getSelectedRepositories(integration.id);
    setSelectedRepositories(repos);
  };

  const loadUserRepositories = async () => {
    if (!integration) return;
    
    setLoadingRepos(true);
    try {
      // This would typically call GitHub API through a secure backend endpoint
      // For now, we'll simulate it
      const response = await fetch('/api/github/repositories', {
        headers: {
          'Authorization': `Bearer ${integration.access_token}`
        }
      });
      
      if (response.ok) {
        const repos = await response.json();
        setRepositories(repos);
      }
    } catch (error) {
      console.error('Error loading repositories:', error);
    } finally {
      setLoadingRepos(false);
    }
  };

  const handleConnect = async () => {
    await initiateGitHubOAuth(businessId);
  };

  const handleConfigure = async () => {
    await loadUserRepositories();
    setShowRepositorySelector(true);
  };

  const handleRepositorySelectionSave = async (
    accessType: 'all' | 'selected',
    selectedRepos: GitHubRepoData[]
  ) => {
    if (!integration) return;

    const success = await updateRepositorySelection(integration.id, accessType, selectedRepos);
    if (success) {
      setShowRepositorySelector(false);
      await loadSelectedRepositories();
      onIntegrationChange?.();
    }
  };

  const handleRevoke = async () => {
    if (!integration) return;
    
    const success = await revokeGitHubIntegration(integration.id);
    if (success) {
      onIntegrationChange?.();
    }
  };

  const isConnected = integration && integration.is_active;

  return (
    <>
      <Card className="relative overflow-hidden">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-lg bg-gray-100">
                <Github className="h-6 w-6 text-gray-700" />
              </div>
              <div>
                <CardTitle className="text-lg flex items-center gap-2">
                  GitHub
                  {isConnected && (
                    <Badge className="bg-green-100 text-green-800">Connesso</Badge>
                  )}
                </CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  {isConnected ? (
                    <span className="text-sm text-gray-600">
                      @{integration.github_username}
                    </span>
                  ) : (
                    <Badge variant="outline">Disponibile</Badge>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {isConnected && (
                <Switch checked={true} disabled />
              )}
              <Button variant="ghost" size="sm">
                <ExternalLink className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          <CardDescription className="mb-4">
            Integra Github per gestire i tuoi repository e progetti
          </CardDescription>

          <div className="space-y-3">
            <div>
              <h4 className="text-sm font-medium mb-2">Funzionalità:</h4>
              <div className="flex flex-wrap gap-1">
                <Badge variant="secondary" className="text-xs">Repository</Badge>
                <Badge variant="secondary" className="text-xs">Progetti</Badge>
                <Badge variant="secondary" className="text-xs">Clone</Badge>
                <Badge variant="secondary" className="text-xs">Lettura</Badge>
              </div>
            </div>

            {isConnected && (
              <div className="space-y-2">
                <div className="text-sm">
                  <strong>Accesso:</strong> {integration.repository_access_type === 'all' ? 'Tutte le repository' : 'Repository selezionate'}
                </div>
                {integration.repository_access_type === 'selected' && (
                  <div className="text-sm text-gray-600">
                    {selectedRepositories.length} repository selezionate
                  </div>
                )}
              </div>
            )}

            <div className="pt-2 border-t space-y-2">
              {!isConnected ? (
                <Button 
                  size="sm" 
                  className="w-full" 
                  onClick={handleConnect}
                  disabled={loading}
                >
                  {loading ? 'Connessione...' : 'Connetti GitHub'}
                </Button>
              ) : (
                <>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full"
                    onClick={handleConfigure}
                    disabled={loadingRepos}
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    {loadingRepos ? 'Caricamento...' : 'Configura Repository'}
                  </Button>
                  <Button 
                    variant="destructive" 
                    size="sm" 
                    className="w-full"
                    onClick={handleRevoke}
                    disabled={loading}
                  >
                    Revoca Autorizzazione
                  </Button>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <GitHubRepositorySelector
        open={showRepositorySelector}
        onClose={() => setShowRepositorySelector(false)}
        repositories={repositories}
        selectedRepositories={selectedRepositories.map(repo => ({
          id: repo.github_repo_id,
          name: repo.name,
          full_name: repo.full_name,
          owner: {
            login: repo.owner_login,
            type: repo.owner_type
          },
          private: repo.is_private,
          clone_url: repo.clone_url,
          ssh_url: repo.ssh_url,
          default_branch: repo.default_branch
        }))}
        accessType={integration?.repository_access_type || 'all'}
        onSave={handleRepositorySelectionSave}
        loading={loading}
      />
    </>
  );
};
