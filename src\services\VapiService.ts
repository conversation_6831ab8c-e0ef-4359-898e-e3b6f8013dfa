import { CreateAssistantDTO } from "@vapi-ai/web/dist/api";
import { vapi } from "@/lib/vapi.sdk";
import type { VoiceProvider, VoiceEvent } from "./VoiceProvider";

export class VapiService implements VoiceProvider {
  async start(config: CreateAssistantDTO): Promise<void> {
    await vapi.start(config);
  }

  stop(): void {
    vapi.stop();
  }

  on(event: VoiceEvent, handler: (...args: any[]) => void): void {
    vapi.on(event, handler);
  }

  off(event: VoiceEvent, handler: (...args: any[]) => void): void {
    vapi.off(event, handler);
  }
}

export const vapiService = new VapiService();
