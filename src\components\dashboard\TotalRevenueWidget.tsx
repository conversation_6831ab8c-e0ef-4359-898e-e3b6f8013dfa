import React from 'react';
import Widget from './Widget';
import { TotalRevenueWidget as TotalRevenueWidgetType } from '@/types/widget';
import { formatCurrency, formatPercentage } from '@/utils/formatters';
import { DollarSign, TrendingUp, TrendingDown } from 'lucide-react';

interface TotalRevenueWidgetProps {
  widget: TotalRevenueWidgetType;
  className?: string;
  onRemove?: () => void;
}

const TotalRevenueWidget: React.FC<TotalRevenueWidgetProps> = React.memo(({ widget, className, onRemove }) => {
  return (
    <Widget
      widget={widget}
      className={className}
      actionLabel="Dettagli Finanziari"
      onRemove={onRemove}
    >
      <div className="w-full py-2">
        <div className="flex flex-col items-center justify-center text-center">
          <span className="text-4xl font-bold text-green-600">
            {widget.isLoading ? "-" : formatCurrency(widget.data.amount)}
          </span>
          <span className="text-sm text-gray-500 mt-1">
            Ricavi totali negli ultimi {widget.data.period}
          </span>
          <div className="mt-3 flex items-center justify-center">
            <DollarSign className="h-5 w-5 text-green-500 mr-1" />
            <span className="text-sm text-green-600 font-medium">
              Ricavi dalle prenotazioni
            </span>
          </div>

          {widget.data.percentChange !== undefined && (
            <div className="mt-2 flex items-center justify-center">
              {widget.data.percentChange >= 0 ? (
                <TrendingUp className="h-4 w-4 text-emerald-500 mr-1" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span
                className={`text-xs font-medium ${widget.data.percentChange >= 0 ? 'text-emerald-600' : 'text-red-600'}`}
              >
                {widget.data.percentChange >= 0 ? '+' : ''}{formatPercentage(widget.data.percentChange / 100)} rispetto al periodo precedente
              </span>
            </div>
          )}
        </div>
      </div>
    </Widget>
  );
});

export default TotalRevenueWidget;
