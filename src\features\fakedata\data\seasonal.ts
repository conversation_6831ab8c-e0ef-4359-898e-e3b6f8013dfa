import { SeasonalPattern, BusinessCategory } from '../types';

// Seasonal multipliers for different business categories
export const seasonalPatterns: Record<BusinessCategory, SeasonalPattern[]> = {
  bar: [
    { month: 1, dealMultiplier: 0.8, priceMultiplier: 0.9 }, // January - post holidays
    { month: 2, dealMultiplier: 0.9, priceMultiplier: 0.9 }, // February 
    { month: 3, dealMultiplier: 1.1, priceMultiplier: 1.0 }, // March - spring begins
    { month: 4, dealMultiplier: 1.2, priceMultiplier: 1.0 }, // April
    { month: 5, dealMultiplier: 1.3, priceMultiplier: 1.1 }, // May - aperitivo season
    { month: 6, dealMultiplier: 1.4, priceMultiplier: 1.1 }, // June - peak outdoor season
    { month: 7, dealMultiplier: 1.5, priceMultiplier: 1.2 }, // July - summer peak
    { month: 8, dealMultiplier: 1.3, priceMultiplier: 1.2 }, // August - some close for holidays
    { month: 9, dealMultiplier: 1.2, priceMultiplier: 1.1 }, // September - back to work
    { month: 10, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // October
    { month: 11, dealMultiplier: 0.9, priceMultiplier: 0.9 }, // November - slower
    { month: 12, dealMultiplier: 1.1, priceMultiplier: 1.0 }, // December - holidays
  ],
  
  ristorante: [
    { month: 1, dealMultiplier: 1.3, priceMultiplier: 0.9 }, // January - need customers after holidays
    { month: 2, dealMultiplier: 1.4, priceMultiplier: 0.9 }, // February - Valentine's promo
    { month: 3, dealMultiplier: 1.2, priceMultiplier: 1.0 }, // March
    { month: 4, dealMultiplier: 1.1, priceMultiplier: 1.0 }, // April - Easter period
    { month: 5, dealMultiplier: 1.0, priceMultiplier: 1.1 }, // May - spring dining
    { month: 6, dealMultiplier: 0.9, priceMultiplier: 1.1 }, // June - wedding season
    { month: 7, dealMultiplier: 0.8, priceMultiplier: 1.2 }, // July - tourist season
    { month: 8, dealMultiplier: 0.7, priceMultiplier: 1.2 }, // August - many closed
    { month: 9, dealMultiplier: 1.1, priceMultiplier: 1.1 }, // September - reopening
    { month: 10, dealMultiplier: 1.2, priceMultiplier: 1.0 }, // October - autumn menu
    { month: 11, dealMultiplier: 1.3, priceMultiplier: 0.9 }, // November - slow season
    { month: 12, dealMultiplier: 0.8, priceMultiplier: 1.3 }, // December - holiday bookings
  ],

  spa: [
    { month: 1, dealMultiplier: 1.5, priceMultiplier: 0.8 }, // January - New Year wellness
    { month: 2, dealMultiplier: 1.4, priceMultiplier: 0.8 }, // February - winter blues
    { month: 3, dealMultiplier: 1.3, priceMultiplier: 0.9 }, // March - spring prep
    { month: 4, dealMultiplier: 1.2, priceMultiplier: 1.0 }, // April - Easter break
    { month: 5, dealMultiplier: 1.1, priceMultiplier: 1.1 }, // May - pre-summer prep
    { month: 6, dealMultiplier: 0.9, priceMultiplier: 1.2 }, // June - wedding season
    { month: 7, dealMultiplier: 0.8, priceMultiplier: 1.2 }, // July - vacation time
    { month: 8, dealMultiplier: 0.7, priceMultiplier: 1.3 }, // August - premium vacation treatments
    { month: 9, dealMultiplier: 1.0, priceMultiplier: 1.1 }, // September - back to routine
    { month: 10, dealMultiplier: 1.1, priceMultiplier: 1.0 }, // October
    { month: 11, dealMultiplier: 1.2, priceMultiplier: 0.9 }, // November - winter prep
    { month: 12, dealMultiplier: 1.0, priceMultiplier: 1.1 }, // December - holiday treatments
  ],

  palestra: [
    { month: 1, dealMultiplier: 2.0, priceMultiplier: 0.7 }, // January - New Year resolutions peak
    { month: 2, dealMultiplier: 1.8, priceMultiplier: 0.8 }, // February - still strong motivation
    { month: 3, dealMultiplier: 1.5, priceMultiplier: 0.9 }, // March - motivation waning
    { month: 4, dealMultiplier: 1.2, priceMultiplier: 1.0 }, // April - spring fitness
    { month: 5, dealMultiplier: 1.1, priceMultiplier: 1.1 }, // May - summer body prep
    { month: 6, dealMultiplier: 1.0, priceMultiplier: 1.1 }, // June - beach body season
    { month: 7, dealMultiplier: 0.8, priceMultiplier: 1.2 }, // July - vacation time
    { month: 8, dealMultiplier: 0.7, priceMultiplier: 1.2 }, // August - holidays
    { month: 9, dealMultiplier: 1.3, priceMultiplier: 0.9 }, // September - back to routine
    { month: 10, dealMultiplier: 1.1, priceMultiplier: 1.0 }, // October
    { month: 11, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // November
    { month: 12, dealMultiplier: 0.9, priceMultiplier: 1.0 }, // December - holiday indulgence
  ],

  bellezza: [
    { month: 1, dealMultiplier: 1.3, priceMultiplier: 0.9 }, // January - New Year new look
    { month: 2, dealMultiplier: 1.2, priceMultiplier: 0.9 }, // February - Valentine's prep
    { month: 3, dealMultiplier: 1.1, priceMultiplier: 1.0 }, // March - spring refresh
    { month: 4, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // April
    { month: 5, dealMultiplier: 0.9, priceMultiplier: 1.1 }, // May - wedding season prep
    { month: 6, dealMultiplier: 0.8, priceMultiplier: 1.2 }, // June - summer events
    { month: 7, dealMultiplier: 0.7, priceMultiplier: 1.2 }, // July - vacation treatments
    { month: 8, dealMultiplier: 0.8, priceMultiplier: 1.1 }, // August - back to work prep
    { month: 9, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // September - autumn refresh
    { month: 10, dealMultiplier: 1.1, priceMultiplier: 1.0 }, // October
    { month: 11, dealMultiplier: 1.2, priceMultiplier: 0.9 }, // November - holiday prep
    { month: 12, dealMultiplier: 0.9, priceMultiplier: 1.1 }, // December - holiday events
  ],

  abbigliamento: [
    { month: 1, dealMultiplier: 1.8, priceMultiplier: 0.6 }, // January - winter sales peak
    { month: 2, dealMultiplier: 1.6, priceMultiplier: 0.7 }, // February - sales continue
    { month: 3, dealMultiplier: 1.2, priceMultiplier: 0.9 }, // March - spring collections
    { month: 4, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // April
    { month: 5, dealMultiplier: 0.9, priceMultiplier: 1.1 }, // May - full price spring
    { month: 6, dealMultiplier: 0.8, priceMultiplier: 1.1 }, // June - early summer
    { month: 7, dealMultiplier: 1.4, priceMultiplier: 0.8 }, // July - summer sales begin
    { month: 8, dealMultiplier: 1.6, priceMultiplier: 0.7 }, // August - summer sales peak
    { month: 9, dealMultiplier: 1.1, priceMultiplier: 0.9 }, // September - autumn collections
    { month: 10, dealMultiplier: 0.9, priceMultiplier: 1.0 }, // October - full price autumn
    { month: 11, dealMultiplier: 0.8, priceMultiplier: 1.1 }, // November - pre-Christmas
    { month: 12, dealMultiplier: 0.7, priceMultiplier: 1.2 }, // December - holiday shopping
  ],

  alimentari: [
    { month: 1, dealMultiplier: 1.2, priceMultiplier: 0.9 }, // January - health food focus
    { month: 2, dealMultiplier: 1.1, priceMultiplier: 0.9 }, // February
    { month: 3, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // March
    { month: 4, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // April - Easter products
    { month: 5, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // May
    { month: 6, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // June
    { month: 7, dealMultiplier: 0.9, priceMultiplier: 1.1 }, // July - summer products
    { month: 8, dealMultiplier: 0.8, priceMultiplier: 1.1 }, // August - vacation impact
    { month: 9, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // September - back to school
    { month: 10, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // October - autumn products
    { month: 11, dealMultiplier: 1.1, priceMultiplier: 1.0 }, // November - holiday prep
    { month: 12, dealMultiplier: 0.8, priceMultiplier: 1.2 }, // December - premium holiday foods
  ],

  hotel: [
    { month: 1, dealMultiplier: 1.4, priceMultiplier: 0.8 }, // January - post-holiday deals
    { month: 2, dealMultiplier: 1.3, priceMultiplier: 0.9 }, // February - winter breaks
    { month: 3, dealMultiplier: 1.1, priceMultiplier: 1.0 }, // March - spring bookings
    { month: 4, dealMultiplier: 1.0, priceMultiplier: 1.1 }, // April - Easter period
    { month: 5, dealMultiplier: 0.9, priceMultiplier: 1.2 }, // May - high season begins
    { month: 6, dealMultiplier: 0.8, priceMultiplier: 1.3 }, // June - peak season
    { month: 7, dealMultiplier: 0.7, priceMultiplier: 1.4 }, // July - summer peak
    { month: 8, dealMultiplier: 0.6, priceMultiplier: 1.5 }, // August - highest rates
    { month: 9, dealMultiplier: 1.0, priceMultiplier: 1.1 }, // September - shoulder season
    { month: 10, dealMultiplier: 1.2, priceMultiplier: 1.0 }, // October - autumn deals
    { month: 11, dealMultiplier: 1.3, priceMultiplier: 0.9 }, // November - low season
    { month: 12, dealMultiplier: 0.9, priceMultiplier: 1.2 }, // December - holiday season
  ],

  cinema: [
    { month: 1, dealMultiplier: 1.2, priceMultiplier: 0.9 }, // January - post-holiday promotions
    { month: 2, dealMultiplier: 1.1, priceMultiplier: 0.9 }, // February - Valentine's specials
    { month: 3, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // March - normal season
    { month: 4, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // April - Easter holidays
    { month: 5, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // May - blockbuster season
    { month: 6, dealMultiplier: 0.9, priceMultiplier: 1.1 }, // June - summer releases
    { month: 7, dealMultiplier: 0.8, priceMultiplier: 1.1 }, // July - summer blockbuster peak
    { month: 8, dealMultiplier: 0.9, priceMultiplier: 1.1 }, // August - continued summer season
    { month: 9, dealMultiplier: 1.1, priceMultiplier: 1.0 }, // September - back to school
    { month: 10, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // October - Halloween releases
    { month: 11, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // November - pre-holiday releases
    { month: 12, dealMultiplier: 0.9, priceMultiplier: 1.1 }, // December - holiday blockbusters
  ],

  fioraio: [
    { month: 1, dealMultiplier: 1.3, priceMultiplier: 0.9 }, // January - post-holiday cleanup
    { month: 2, dealMultiplier: 0.7, priceMultiplier: 1.3 }, // February - Valentine's peak
    { month: 3, dealMultiplier: 0.8, priceMultiplier: 1.2 }, // March - Women's Day, spring
    { month: 4, dealMultiplier: 1.0, priceMultiplier: 1.1 }, // April - Easter flowers
    { month: 5, dealMultiplier: 0.8, priceMultiplier: 1.2 }, // May - Mother's Day, weddings
    { month: 6, dealMultiplier: 0.7, priceMultiplier: 1.3 }, // June - wedding season peak
    { month: 7, dealMultiplier: 0.9, priceMultiplier: 1.1 }, // July - summer weddings
    { month: 8, dealMultiplier: 1.2, priceMultiplier: 0.9 }, // August - slower period
    { month: 9, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // September - autumn arrangements
    { month: 10, dealMultiplier: 1.1, priceMultiplier: 1.0 }, // October - All Saints prep
    { month: 11, dealMultiplier: 0.9, priceMultiplier: 1.1 }, // November - All Saints Day
    { month: 12, dealMultiplier: 0.8, priceMultiplier: 1.2 }, // December - Christmas arrangements
  ],

  teatro: [
    { month: 1, dealMultiplier: 1.1, priceMultiplier: 0.9 }, // January - post-holiday season
    { month: 2, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // February - winter season
    { month: 3, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // March - spring season starts
    { month: 4, dealMultiplier: 0.9, priceMultiplier: 1.1 }, // April - spring season peak
    { month: 5, dealMultiplier: 0.9, priceMultiplier: 1.1 }, // May - continued spring season
    { month: 6, dealMultiplier: 1.2, priceMultiplier: 0.9 }, // June - season end, summer break
    { month: 7, dealMultiplier: 1.3, priceMultiplier: 0.8 }, // July - summer break
    { month: 8, dealMultiplier: 1.4, priceMultiplier: 0.8 }, // August - summer break continues
    { month: 9, dealMultiplier: 1.1, priceMultiplier: 0.9 }, // September - season reopening
    { month: 10, dealMultiplier: 0.9, priceMultiplier: 1.1 }, // October - autumn season
    { month: 11, dealMultiplier: 0.8, priceMultiplier: 1.1 }, // November - peak autumn season
    { month: 12, dealMultiplier: 0.7, priceMultiplier: 1.2 }, // December - holiday shows
  ],

  aperitivo: [
    { month: 1, dealMultiplier: 1.1, priceMultiplier: 0.9 }, // January - New Year promotions
    { month: 2, dealMultiplier: 1.0, priceMultiplier: 0.9 }, // February - Valentine's specials
    { month: 3, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // March - spring begins
    { month: 4, dealMultiplier: 0.9, priceMultiplier: 1.1 }, // April - outdoor season starts
    { month: 5, dealMultiplier: 0.8, priceMultiplier: 1.2 }, // May - peak aperitivo season
    { month: 6, dealMultiplier: 0.7, priceMultiplier: 1.3 }, // June - summer aperitivo peak
    { month: 7, dealMultiplier: 0.6, priceMultiplier: 1.4 }, // July - summer outdoor peak
    { month: 8, dealMultiplier: 0.8, priceMultiplier: 1.2 }, // August - vacation impact
    { month: 9, dealMultiplier: 0.9, priceMultiplier: 1.1 }, // September - back to work aperitivi
    { month: 10, dealMultiplier: 1.0, priceMultiplier: 1.0 }, // October - autumn season
    { month: 11, dealMultiplier: 1.1, priceMultiplier: 0.9 }, // November - indoor season
    { month: 12, dealMultiplier: 0.9, priceMultiplier: 1.1 }, // December - holiday aperitivi
  ],

  default: [
    { month: 1, dealMultiplier: 1.2, priceMultiplier: 0.9 },
    { month: 2, dealMultiplier: 1.1, priceMultiplier: 0.9 },
    { month: 3, dealMultiplier: 1.0, priceMultiplier: 1.0 },
    { month: 4, dealMultiplier: 1.0, priceMultiplier: 1.0 },
    { month: 5, dealMultiplier: 1.0, priceMultiplier: 1.0 },
    { month: 6, dealMultiplier: 1.0, priceMultiplier: 1.0 },
    { month: 7, dealMultiplier: 0.9, priceMultiplier: 1.1 },
    { month: 8, dealMultiplier: 0.8, priceMultiplier: 1.1 },
    { month: 9, dealMultiplier: 1.0, priceMultiplier: 1.0 },
    { month: 10, dealMultiplier: 1.0, priceMultiplier: 1.0 },
    { month: 11, dealMultiplier: 1.1, priceMultiplier: 1.0 },
    { month: 12, dealMultiplier: 0.9, priceMultiplier: 1.1 },
  ]
};

// Italian holidays that affect business patterns
export const italianHolidays = [
  { month: 1, day: 1, name: "Capodanno", impact: 'closed' },
  { month: 1, day: 6, name: "Epifania", impact: 'reduced' },
  { month: 4, day: 25, name: "Festa della Liberazione", impact: 'closed' },
  { month: 5, day: 1, name: "Festa del Lavoro", impact: 'closed' },
  { month: 6, day: 2, name: "Festa della Repubblica", impact: 'closed' },
  { month: 8, day: 15, name: "Ferragosto", impact: 'closed' },
  { month: 11, day: 1, name: "Ognissanti", impact: 'reduced' },
  { month: 12, day: 8, name: "Immacolata Concezione", impact: 'reduced' },
  { month: 12, day: 25, name: "Natale", impact: 'closed' },
  { month: 12, day: 26, name: "Santo Stefano", impact: 'closed' }
] as const;

// Regional holiday patterns (many businesses close in August)
export const augustClosurePatterns = {
  partial: { probability: 0.4, closureDays: [7, 14] }, // Close for 1-2 weeks
  full: { probability: 0.3, closureDays: [25, 31] },   // Close almost entire month
  open: { probability: 0.3, closureDays: [0, 3] }     // Stay mostly open
} as const;