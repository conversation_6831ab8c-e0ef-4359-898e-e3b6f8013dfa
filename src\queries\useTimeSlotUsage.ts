import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { subDays, format } from 'date-fns';

// Types for the filter options
export interface TimeSlotFilters {
  dateRange: 'last7days' | 'last30days' | 'last90days' | 'all';
  sortBy: 'popularity' | 'time';
  selectedDays: number[]; // 0-6 for Sunday-Saturday
}

// Type for the time slot data
export interface TimeSlotData {
  timeSlot: string;
  count: number;
  dayOfWeek?: number;
}

// Default filter values
export const defaultFilters: TimeSlotFilters = {
  dateRange: 'last30days',
  sortBy: 'popularity',
  selectedDays: [0, 1, 2, 3, 4, 5, 6] // All days selected by default
};

/**
 * Formatta l'orario in formato hh:mm
 */
const formatTimeHHMM = (timeString: string): string => {
  if (!timeString) return '';

  // Se è già in formato hh:mm, ritorna come è
  if (/^\d{1,2}:\d{2}$/.test(timeString)) return timeString;

  try {
    // Rimuovi i secondi se presenti (formato hh:mm:ss)
    if (timeString.includes(':')) {
      const parts = timeString.split(':');
      if (parts.length >= 2) {
        return `${parts[0]}:${parts[1]}`;
      }
    }

    return timeString;
  } catch (error) {
    console.error('Errore durante la formattazione dell\'orario:', error);
    return timeString;
  }
};

/**
 * Hook per ottenere i dati delle fasce orarie più popolari
 */
export const useTimeSlotUsage = (
  businessId: string | undefined,
  filters: TimeSlotFilters = defaultFilters
) => {
  return useQuery({
    // Usa un queryKey stabile che include solo i valori dei filtri, non l'oggetto filters stesso
    queryKey: ['timeSlotUsage', businessId, filters.dateRange, filters.sortBy, filters.selectedDays.join(',')],
    queryFn: async () => {
      if (!businessId) {
        throw new Error('Business ID is required');
      }

      // Calcola l'intervallo di date in base al filtro selezionato
      const now = new Date();
      let startDate: Date;

      switch (filters.dateRange) {
        case 'last7days':
          startDate = subDays(now, 7);
          break;
        case 'last30days':
          startDate = subDays(now, 30);
          break;
        case 'last90days':
          startDate = subDays(now, 90);
          break;
        case 'all':
        default:
          startDate = subDays(now, 365); // Usa un anno come "all"
          break;
      }

      const formattedStartDate = format(startDate, 'yyyy-MM-dd');

      // Ottieni le prenotazioni per il business selezionato
      const { data: bookings, error: bookingsError } = await supabase
        .from('time_slot_bookings')
        .select(`
          id,
          deal_id,
          start_time,
          end_time,
          day_of_week,
          booked_seats,
          booking_date,
          deal:deals(business_id)
        `)
        .eq('deal.business_id', businessId)
        .gte('booking_date', formattedStartDate)
        .order('start_time', { ascending: true });

      if (bookingsError) throw bookingsError;

      // Ottieni anche i dati delle fasce orarie dalle offerte
      const { data: deals, error: dealsError } = await supabase
        .from('deals')
        .select(`
          id,
          time_slots
        `)
        .eq('business_id', businessId);

      if (dealsError) throw dealsError;

      // Raggruppa le prenotazioni per fascia oraria
      const timeSlotCounts: Record<string, { count: number, dayOfWeek?: number }> = {};

      // Aggiungi dati dalle prenotazioni effettive
      bookings?.forEach(booking => {
        // Filtra per giorni selezionati
        if (!filters.selectedDays.includes(booking.day_of_week)) {
          return;
        }

        // Formatta l'orario in formato più leggibile (hh:mm)
        const startTime = formatTimeHHMM(booking.start_time);
        const endTime = formatTimeHHMM(booking.end_time);
        const timeSlot = `${startTime}-${endTime}`;

        if (!timeSlotCounts[timeSlot]) {
          timeSlotCounts[timeSlot] = { count: 0, dayOfWeek: booking.day_of_week };
        }

        timeSlotCounts[timeSlot].count += booking.booked_seats || 1;
      });

      // Aggiungi dati dalle fasce orarie configurate nelle offerte
      deals?.forEach(deal => {
        if (!deal.time_slots || typeof deal.time_slots !== 'object') return;

        try {
          // Gestisci la struttura delle fasce orarie
          const timeSlots = deal.time_slots as any;

          if (timeSlots.schedule && Array.isArray(timeSlots.schedule)) {
            timeSlots.schedule.forEach((daySchedule: any) => {
              if (Array.isArray(daySchedule.time_slots)) {
                daySchedule.time_slots.forEach((slot: any) => {
                  // Filtra per giorni selezionati
                  if (!filters.selectedDays.includes(daySchedule.day)) {
                    return;
                  }

                  // Formatta l'orario in formato più leggibile (hh:mm)
                  const startTime = formatTimeHHMM(slot.start_time);
                  const endTime = formatTimeHHMM(slot.end_time);
                  const timeSlot = `${startTime}-${endTime}`;

                  if (!timeSlotCounts[timeSlot]) {
                    timeSlotCounts[timeSlot] = { count: 0, dayOfWeek: daySchedule.day };
                  }

                  // Incrementa il conteggio solo se non ci sono già prenotazioni per questa fascia
                  // Questo dà priorità ai dati reali delle prenotazioni
                  if (timeSlotCounts[timeSlot].count === 0) {
                    timeSlotCounts[timeSlot].count += 1;
                  }
                });
              }
            });
          }
        } catch (error) {
          console.error('Errore durante l\'elaborazione delle fasce orarie:', error);
        }
      });

      // Converti in array e applica l'ordinamento in base al filtro
      let timeSlotData = Object.entries(timeSlotCounts)
        .map(([timeSlot, data]) => ({
          timeSlot,
          count: data.count,
          dayOfWeek: data.dayOfWeek
        }));

      // Applica l'ordinamento in base al filtro
      if (filters.sortBy === 'popularity') {
        timeSlotData = timeSlotData.sort((a, b) => b.count - a.count);
      } else { // 'time'
        timeSlotData = timeSlotData.sort((a, b) => {
          const timeA = a.timeSlot.split('-')[0];
          const timeB = b.timeSlot.split('-')[0];
          return timeA.localeCompare(timeB);
        });
      }

      // Prendi solo le prime 10 fasce orarie
      return timeSlotData.slice(0, 10);
    },
    enabled: !!businessId,
    // Disabilita il refetch automatico in background
    refetchOnWindowFocus: false,
    // Imposta un staleTime più lungo per evitare richieste inutili
    staleTime: 5 * 60 * 1000, // 5 minuti
  });
};
