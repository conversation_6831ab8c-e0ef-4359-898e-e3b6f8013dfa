
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PlusCircle, Lightbulb, ChevronRight, ChevronDown, DoorClosed, Table, Armchair, Sofa, Bed, FolderOpen, Folder } from "lucide-react";
import { useState, useEffect } from "react";
import {
  Table as UITable,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface LayoutSuggestion {
  title: string;
  description: string;
  elements: Array<{
    type: string;
    name: string;
    count: number;
    capacity?: number;
    parentType?: string;
    parentName?: string;
  }>;
}

interface LayoutSuggestionsProps {
  businessCategory: string;
  onApplySuggestion: (suggestion: LayoutSuggestion) => void;
}

const LayoutSuggestions = ({ businessCategory, onApplySuggestion }: LayoutSuggestionsProps) => {
  const [suggestions, setSuggestions] = useState<LayoutSuggestion[]>([]);
  const [expandedSuggestions, setExpandedSuggestions] = useState<Record<number, boolean>>({});

  // Aggiorniamo i suggerimenti quando cambia la categoria
  useEffect(() => {
    console.log("LayoutSuggestions - Aggiornamento per categoria:", businessCategory);
    const getSuggestionsForCategory = (category: string): LayoutSuggestion[] => {
      switch (category) {
        case "ristorante":
          return [
            {
              title: "Ristorante piccolo",
              description: "Layout per un ristorante di piccole dimensioni",
              elements: [
                { type: "sala", name: "Sala principale", count: 1, capacity: 20 },
                { type: "tavolo", name: "Tavolo per 2", count: 4, capacity: 2, parentType: "sala", parentName: "Sala principale" },
                { type: "tavolo", name: "Tavolo per 4", count: 3, capacity: 4, parentType: "sala", parentName: "Sala principale" },
              ]
            },
            {
              title: "Ristorante con terrazza",
              description: "Layout con sala interna e spazio esterno",
              elements: [
                { type: "sala", name: "Sala interna", count: 1, capacity: 16 },
                { type: "sala", name: "Terrazza", count: 1, capacity: 14 },
                { type: "tavolo", name: "Tavolo per 2", count: 3, capacity: 2, parentType: "sala", parentName: "Sala interna" },
                { type: "tavolo", name: "Tavolo per 4", count: 3, capacity: 4, parentType: "sala", parentName: "Sala interna" },
                { type: "tavolo", name: "Tavolo per 2 esterno", count: 3, capacity: 2, parentType: "sala", parentName: "Terrazza" },
                { type: "tavolo", name: "Tavolo per 4 esterno", count: 2, capacity: 4, parentType: "sala", parentName: "Terrazza" },
              ]
            }
          ];
        case "salone":
          return [
            {
              title: "Salone base",
              description: "Layout standard per un salone di bellezza",
              elements: [
                { type: "sala", name: "Area principale", count: 1, capacity: 8 },
                { type: "sala", name: "Area trattamenti", count: 1, capacity: 4 },
                { type: "postazione", name: "Postazione taglio", count: 3, capacity: 1, parentType: "sala", parentName: "Area principale" },
                { type: "postazione", name: "Postazione colore", count: 2, capacity: 1, parentType: "sala", parentName: "Area principale" },
                { type: "postazione", name: "Postazione trattamento", count: 2, capacity: 1, parentType: "sala", parentName: "Area trattamenti" },
              ]
            }
          ];
        case "hotel":
          return [
            {
              title: "Hotel piccolo",
              description: "Layout per un hotel di piccole dimensioni",
              elements: [
                { type: "piano", name: "Piano terra", count: 1, capacity: 5 },
                { type: "piano", name: "Primo piano", count: 1, capacity: 8 },
                { type: "camera", name: "Camera singola", count: 2, capacity: 1, parentType: "piano", parentName: "Piano terra" },
                { type: "camera", name: "Camera doppia", count: 3, capacity: 2, parentType: "piano", parentName: "Piano terra" },
                { type: "camera", name: "Camera singola", count: 3, capacity: 1, parentType: "piano", parentName: "Primo piano" },
                { type: "camera", name: "Camera doppia", count: 5, capacity: 2, parentType: "piano", parentName: "Primo piano" },
              ]
            }
          ];
        case "palestra":
          return [
            {
              title: "Palestra standard",
              description: "Layout per una palestra di medie dimensioni",
              elements: [
                { type: "sala", name: "Area cardio", count: 1, capacity: 10 },
                { type: "sala", name: "Area pesi", count: 1, capacity: 12 },
                { type: "attrezzo", name: "Tapis roulant", count: 4, capacity: 1, parentType: "sala", parentName: "Area cardio" },
                { type: "attrezzo", name: "Cyclette", count: 3, capacity: 1, parentType: "sala", parentName: "Area cardio" },
                { type: "attrezzo", name: "Panca pesi", count: 5, capacity: 1, parentType: "sala", parentName: "Area pesi" },
                { type: "attrezzo", name: "Bilancieri", count: 3, capacity: 1, parentType: "sala", parentName: "Area pesi" },
              ]
            }
          ];
        case "spa":
          return [
            {
              title: "Spa relax",
              description: "Layout per una spa con focus sul relax",
              elements: [
                { type: "sala", name: "Zona accoglienza", count: 1, capacity: 4 },
                { type: "sala", name: "Area relax", count: 1, capacity: 8 },
                { type: "sala", name: "Area trattamenti", count: 1, capacity: 6 },
                { type: "cabina", name: "Lettino relax", count: 4, capacity: 1, parentType: "sala", parentName: "Area relax" },
                { type: "cabina", name: "Cabina massaggio", count: 3, capacity: 1, parentType: "sala", parentName: "Area trattamenti" },
                { type: "cabina", name: "Cabina trattamenti", count: 2, capacity: 1, parentType: "sala", parentName: "Area trattamenti" },
              ]
            }
          ];
        case "spettacoli":
          return [
            {
              title: "Sala Cinema",
              description: "Layout per una sala cinematografica standard",
              elements: [
                { type: "settore", name: "Platea", count: 1, capacity: 200 },
                { type: "settore", name: "Galleria", count: 1, capacity: 100 },
                { type: "posto", name: "Posto Standard", count: 250, capacity: 1, parentType: "settore", parentName: "Platea" },
                { type: "posto", name: "Posto Premium", count: 50, capacity: 1, parentType: "settore", parentName: "Galleria" },
              ]
            },
            {
              title: "Teatro",
              description: "Layout per un teatro tradizionale",
              elements: [
                { type: "settore", name: "Platea", count: 1, capacity: 150 },
                { type: "settore", name: "Palchi", count: 1, capacity: 100 },
                { type: "settore", name: "Loggione", count: 1, capacity: 50 },
                { type: "posto", name: "Posto Platea", count: 150, capacity: 1, parentType: "settore", parentName: "Platea" },
                { type: "posto", name: "Posto Palco", count: 100, capacity: 1, parentType: "settore", parentName: "Palchi" },
                { type: "posto", name: "Posto Loggione", count: 50, capacity: 1, parentType: "settore", parentName: "Loggione" },
              ]
            },
            {
              title: "Arena Concerti",
              description: "Layout per un'arena concerti",
              elements: [
                { type: "settore", name: "Prato", count: 1, capacity: 1000 },
                { type: "settore", name: "Tribuna A", count: 1, capacity: 300 },
                { type: "settore", name: "Tribuna B", count: 1, capacity: 300 },
                { type: "posto", name: "Posto Tribuna", count: 600, capacity: 1, parentType: "settore", parentName: "Tribuna A" },
                { type: "posto", name: "Posto VIP", count: 100, capacity: 1, parentType: "settore", parentName: "Tribuna B" },
              ]
            }
          ];
        default:
          return [];
      }
    };

    setSuggestions(getSuggestionsForCategory(businessCategory));

    // Reset stato di espansione quando cambiano i suggerimenti
    setExpandedSuggestions({});
  }, [businessCategory]);

  const toggleExpand = (index: number) => {
    setExpandedSuggestions((prev) => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  // Ottieni l'icona in base al tipo di elemento
  const getElementIcon = (type: string) => {
    switch (type) {
      case "sala":
      case "piano":
      case "settore":
        return <DoorClosed className="h-4 w-4" />;
      case "tavolo":
        return <Table className="h-4 w-4" />;
      case "postazione":
      case "attrezzo":
        return <Armchair className="h-4 w-4" />;
      case "camera":
        return <Bed className="h-4 w-4" />;
      case "cabina":
        return <Sofa className="h-4 w-4" />;
      case "posto":
        return <Armchair className="h-4 w-4" />;
      default:
        return <DoorClosed className="h-4 w-4" />;
    }
  };

  // Ottieni elementi raggruppati per sale/piani/settori
  const getGroupedElements = (suggestion: LayoutSuggestion) => {
    // Estrai gli elementi root (sale, piani, settori senza parentName)
    const rootElements = suggestion.elements.filter(e =>
      e.type === "sala" || e.type === "piano" || e.type === "settore"
    );

    return rootElements.map((root) => {
      // Trova gli elementi figli specificatamente associati a questo elemento root
      const childElements = suggestion.elements.filter(e =>
        (e.type !== "sala" && e.type !== "piano" && e.type !== "settore") ||
        (e.type === "settore" && e.parentName) && // Include settori che hanno un parentName
        e.parentName === root.name
      );

      return {
        room: root, // Manteniamo il nome 'room' per compatibilità
        children: childElements
      };
    });
  };

  if (suggestions.length === 0) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="h-5 w-5 text-yellow-500" />
          Layout Consigliati
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {suggestions.map((suggestion, index) => {
            const isExpanded = expandedSuggestions[index] ?? false;
            return (
              <Card key={index} className="border-dashed">
                <CardContent className="pt-6">
                  <div className="flex justify-between items-center mb-2">
                    <div>
                      <h3 className="font-medium">{suggestion.title}</h3>
                      <p className="text-sm text-gray-500">{suggestion.description}</p>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => toggleExpand(index)}
                    >
                      {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                    </Button>
                  </div>

                  {isExpanded ? (
                    <div className="space-y-2 mb-4">
                      {getGroupedElements(suggestion).map((groupedItem, groupIdx) => (
                        <div key={groupIdx} className="border rounded-md">
                          <div className="bg-gray-50 p-2 flex items-center gap-2 font-medium border-b">
                            {isExpanded ? <FolderOpen className="h-4 w-4" /> : <Folder className="h-4 w-4" />}
                            {groupedItem.room.name} ({groupedItem.room.count}x)
                          </div>

                          {groupedItem.children.length > 0 ? (
                            <UITable>
                              <TableBody>
                                {groupedItem.children.map((child, childIdx) => (
                                  <TableRow key={childIdx}>
                                    <TableCell className="pl-6 py-1.5 flex items-center gap-2">
                                      {getElementIcon(child.type)}
                                      <span>
                                        {child.count}x {child.name}
                                      </span>
                                    </TableCell>
                                    <TableCell className="text-right text-gray-500 py-1.5">
                                      {child.capacity && `${child.capacity} posti`}
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </UITable>
                          ) : (
                            <div className="p-2 text-sm text-gray-500">Nessun elemento in questa sala</div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-sm space-y-1 text-gray-500 my-4">
                      <p>{suggestion.elements.filter(e => e.type === "sala" || e.type === "piano" || e.type === "settore").length} aree</p>
                      <p>{suggestion.elements.filter(e => e.type !== "sala" && e.type !== "piano" && e.type !== "settore").length} elementi</p>
                    </div>
                  )}

                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => onApplySuggestion(suggestion)}
                  >
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Applica
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default LayoutSuggestions;
