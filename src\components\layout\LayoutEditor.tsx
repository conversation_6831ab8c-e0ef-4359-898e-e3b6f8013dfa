import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DoorClosed,
  Table,
  Armchair,
  Sofa,
  Bed,
  Plus,
  Trash2,
  ArrowLeft,
} from "lucide-react";
import LayoutItem from "./LayoutItem";
import LayoutSuggestions from "./LayoutSuggestions";
import { toast } from "sonner";
import { LayoutElement, LayoutSuggestion } from "@/types/types";

interface LayoutEditorProps {
  businessCategory: string;
  initialElements?: LayoutElement[];
  initialActiveRoomId?: string | null;
  onLayoutUpdate?: (
    elements: LayoutElement[],
    activeRoomId: string | null
  ) => void;
}

const LayoutEditor = ({
  businessCategory,
  initialElements = [],
  initialActiveRoomId = null,
  onLayoutUpdate,
}: LayoutEditorProps) => {
  const [elements, setElements] = useState<LayoutElement[]>(initialElements);
  const [selectedElement, setSelectedElement] = useState<LayoutElement | null>(
    null
  );
  const [elementOptions, setElementOptions] = useState<
    Array<{ value: string; label: string; icon: any }>
  >([]);
  const [currentTab, setCurrentTab] = useState<string>("sale");
  const [activeRoomId, setActiveRoomId] = useState<string | null>(
    initialActiveRoomId
  );

  const getRooms = () => {
    return elements.filter(
      (element) => element.type === "sala" || element.type === "piano" || element.type === "settore"
    );
  };

  const getChildElements = (parentId: string) => {
    return elements.filter((element) => element.parentId === parentId);
  };

  const getParentElement = (element: LayoutElement) => {
    if (!element.parentId) return null;
    return elements.find((e) => e.id === element.parentId) || null;
  };

  useEffect(() => {
    let options = [];

    switch (businessCategory) {
      case "ristorante":
        options = [
          { value: "sala", label: "Sala", icon: DoorClosed },
          { value: "tavolo", label: "Tavolo", icon: Table },
        ];
        break;
      case "salone":
        options = [
          { value: "sala", label: "Sala", icon: DoorClosed },
          { value: "postazione", label: "Postazione", icon: Armchair },
        ];
        break;
      case "palestra":
        options = [
          { value: "sala", label: "Sala", icon: DoorClosed },
          { value: "attrezzo", label: "Attrezzo", icon: Armchair },
        ];
        break;
      case "hotel":
        options = [
          { value: "piano", label: "Piano", icon: DoorClosed },
          { value: "camera", label: "Camera", icon: Bed },
        ];
        break;
      case "spa":
        options = [
          { value: "sala", label: "Sala", icon: DoorClosed },
          { value: "cabina", label: "Cabina", icon: Sofa },
        ];
        break;
      case "spettacoli":
        options = [
          { value: "settore", label: "Settore", icon: DoorClosed },
          { value: "posto", label: "Posto", icon: Armchair },
        ];
        break;
      default:
        options = [{ value: "sala", label: "Sala", icon: DoorClosed }];
    }

    setElementOptions(options);
  }, [businessCategory]);

  useEffect(() => {
    if (onLayoutUpdate) {
      onLayoutUpdate(elements, activeRoomId);
    }
  }, [elements, activeRoomId, onLayoutUpdate]);

  const handleAddElement = (type: string) => {
    if (type === "sala" || type === "piano") {
      const newElement: LayoutElement = {
        id: `element-${Date.now()}`,
        type,
        name: `Nuovo ${type}`,
        width: 10,
        height: 10,
        capacity: 0,
        parentId: null,
      };

      setElements([...elements, newElement]);
      setSelectedElement(newElement);

      toast.success(
        `${type.charAt(0).toUpperCase() + type.slice(1)} aggiunto con successo`
      );
      return;
    }

    if (activeRoomId) {
      const parentElement = elements.find(
        (element) => element.id === activeRoomId
      );
      if (!parentElement) return;

      const newElement: LayoutElement = {
        id: `element-${Date.now()}`,
        type,
        name: `Nuovo ${type}`,
        capacity: type === "tavolo" ? 4 : 1,
        parentId: activeRoomId,
      };

      setElements([...elements, newElement]);
      setSelectedElement(newElement);

      toast.success(
        `${type.charAt(0).toUpperCase() + type.slice(1)} aggiunto in ${
          parentElement.name
        }`
      );
      return;
    }

    const rooms = getRooms();
    if (rooms.length === 0) {
      toast.error("Devi prima creare una sala o un piano", {
        description: "Gli elementi devono essere associati a una sala o piano",
      });
      return;
    }

    const parentId =
      rooms.length === 1
        ? rooms[0].id
        : selectedElement &&
          (selectedElement.type === "sala" || selectedElement.type === "piano")
        ? selectedElement.id
        : rooms[0].id;

    const parentElement = elements.find((element) => element.id === parentId);
    if (!parentElement) return;

    const newElement: LayoutElement = {
      id: `element-${Date.now()}`,
      type,
      name: `Nuovo ${type}`,
      capacity: type === "tavolo" ? 4 : 1,
      parentId: parentId,
    };

    setElements([...elements, newElement]);
    setSelectedElement(newElement);

    toast.success(
      `${type.charAt(0).toUpperCase() + type.slice(1)} aggiunto in ${
        parentElement.name
      }`
    );
  };

  const handleDeleteElement = (id: string) => {
    const elementToDelete = elements.find((element) => element.id === id);
    if (!elementToDelete) return;

    if (elementToDelete.type === "sala" || elementToDelete.type === "piano") {
      const childElements = elements.filter(
        (element) => element.parentId === id
      );

      if (childElements.length > 0) {
        const confirmDelete = window.confirm(
          `Questa operazione eliminerà anche ${childElements.length} element${
            childElements.length === 1 ? "o" : "i"
          } contenut${childElements.length === 1 ? "o" : "i"} in "${
            elementToDelete.name
          }". Vuoi continuare?`
        );
        if (!confirmDelete) return;
      }

      const newElements = elements.filter(
        (element) => element.id !== id && element.parentId !== id
      );
      setElements(newElements);

      if (activeRoomId === id) {
        setActiveRoomId(null);
      }
    } else {
      const newElements = elements.filter((element) => element.id !== id);
      setElements(newElements);
    }

    if (selectedElement && selectedElement.id === id) {
      setSelectedElement(null);
    }

    toast("Elemento eliminato", {
      description: "L'elemento è stato rimosso dal layout",
    });
  };

  const handleElementSelect = (element: LayoutElement) => {
    setSelectedElement(element);

    if (element.type === "sala" || element.type === "piano") {
      setCurrentTab("sale");
    } else {
      setCurrentTab("elementi");
    }
  };

  const handleElementUpdate = (updatedElement: LayoutElement) => {
    const newElements = elements.map((element) =>
      element.id === updatedElement.id ? updatedElement : element
    );
    setElements(newElements);
    setSelectedElement(updatedElement);
  };

  const handleApplySuggestion = (suggestion: LayoutSuggestion) => {
    const newElements: LayoutElement[] = [];
    const roomMap: Record<string, string> = {};

    suggestion.elements
      .filter((element) => element.type === "sala" || element.type === "piano" || element.type === "settore")
      .forEach((element, index) => {
        for (let i = 0; i < element.count; i++) {
          const roomName =
            element.count > 1 ? `${element.name} ${i + 1}` : element.name;
          const roomId = `element-${Date.now()}-${index}-${i}`;
          const salaElement = {
            id: roomId,
            type: element.type,
            name: roomName,
            width: 10,
            height: 10,
            parentId: null,
          };
          newElements.push(salaElement);

          roomMap[roomName] = roomId;
        }
      });

    suggestion.elements
      .filter((element) => element.type !== "sala" && element.type !== "piano" && element.type !== "settore")
      .forEach((element, elementIndex) => {
        if (!element.parentName) {
          console.error("Elemento senza parentName:", element);
          return;
        }

        for (let i = 0; i < element.count; i++) {
          let parentId: string | null = null;

          const exactParentId = roomMap[element.parentName];
          if (exactParentId) {
            parentId = exactParentId;
          } else {
            const matchingRoomKey = Object.keys(roomMap).find(
              (key) =>
                element.parentName?.startsWith(key) ||
                key.startsWith(element.parentName || "")
            );

            if (matchingRoomKey) {
              parentId = roomMap[matchingRoomKey];
            } else {
              console.warn(
                `Sala/piano '${element.parentName}' non trovata per l'elemento:`,
                element
              );
            }
          }

          if (parentId) {
            const elementName =
              element.count > 1 ? `${element.name} ${i + 1}` : element.name;
            newElements.push({
              id: `element-${Date.now()}-child-${elementIndex}-${i}`,
              type: element.type,
              name: elementName,
              capacity: element.capacity,
              parentId: parentId,
            });
          } else if (Object.values(roomMap).length > 0) {
            const firstRoomId = Object.values(roomMap)[0];
            const elementName =
              element.count > 1 ? `${element.name} ${i + 1}` : element.name;

            console.warn(
              `Assegnazione dell'elemento '${elementName}' alla prima sala disponibile`
            );

            newElements.push({
              id: `element-${Date.now()}-child-${elementIndex}-${i}`,
              type: element.type,
              name: elementName,
              capacity: element.capacity,
              parentId: firstRoomId,
            });
          }
        }
      });

    setElements(newElements);
    setSelectedElement(null);
    setActiveRoomId(null);

    toast.success("Layout consigliato applicato con successo");
    console.log("Layout applicato:", newElements);
  };

  const getParentName = (element: LayoutElement) => {
    if (!element.parentId) return undefined;
    const parent = elements.find((e) => e.id === element.parentId);
    return parent ? parent.name : undefined;
  };

  const handleEnterRoom = (roomId: string) => {
    setActiveRoomId(roomId);
    setCurrentTab("elementi");
    const room = elements.find((e) => e.id === roomId);
    if (room) {
      toast.success(`Visualizzazione elementi in ${room.name}`);
    }
  };

  const handleExitRoom = () => {
    setActiveRoomId(null);
    setCurrentTab("sale");
    toast.success("Ritorno alla visualizzazione completa");
  };

  const activeRoom = activeRoomId
    ? elements.find((e) => e.id === activeRoomId)
    : null;

  return (
    <div className="space-y-6">
      {!activeRoomId && (
        <LayoutSuggestions
          businessCategory={businessCategory}
          onApplySuggestion={handleApplySuggestion}
        />
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>
                    {activeRoom ? `Elementi in ${activeRoom.name}` : "Layout"}
                  </CardTitle>
                  <CardDescription>
                    {activeRoom
                      ? `Gestisci gli elementi all'interno di ${activeRoom.name}`
                      : "Aggiungi e configura gli elementi del layout"}
                  </CardDescription>
                </div>
                {activeRoom && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleExitRoom}
                    className="flex items-center gap-1"
                  >
                    <ArrowLeft className="h-4 w-4" /> Torna alle aree
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent className="min-h-[500px]">
              {activeRoom ? (
                <>
                  <div className="mb-4 flex flex-wrap gap-2">
                    {elementOptions
                      .filter(
                        (option) =>
                          option.value !== "sala" && option.value !== "piano"
                      )
                      .map((option) => (
                        <Button
                          key={option.value}
                          variant="outline"
                          size="sm"
                          className="flex items-center gap-2"
                          onClick={() => handleAddElement(option.value)}
                        >
                          <Plus className="h-4 w-4" />
                          <option.icon className="h-4 w-4" />
                          {option.label}
                        </Button>
                      ))}
                  </div>

                  <div className="border border-dashed border-gray-300 rounded-md p-4 min-h-[400px]">
                    {getChildElements(activeRoomId).length === 0 ? (
                      <div className="flex flex-col items-center justify-center h-full text-center p-6">
                        <p className="text-sm text-gray-500 mb-4">
                          Nessun elemento in questa sala. Aggiungi elementi
                          usando i pulsanti sopra.
                        </p>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                        {getChildElements(activeRoomId).map((element) => (
                          <LayoutItem
                            key={element.id}
                            element={element}
                            isSelected={selectedElement?.id === element.id}
                            onSelect={() => handleElementSelect(element)}
                            onDelete={() => handleDeleteElement(element.id)}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                </>
              ) : (
                <Tabs value={currentTab} onValueChange={setCurrentTab}>
                  <TabsList className="mb-4">
                    <TabsTrigger value="sale">Aree</TabsTrigger>
                    <TabsTrigger value="elementi">Elementi</TabsTrigger>
                  </TabsList>

                  <TabsContent value="sale">
                    <div className="mb-4 flex flex-wrap gap-2">
                      {elementOptions
                        .filter(
                          (option) =>
                            option.value === "sala" || option.value === "piano" || option.value === "settore"
                        )
                        .map((option) => (
                          <Button
                            key={option.value}
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-2"
                            onClick={() => handleAddElement(option.value)}
                          >
                            <Plus className="h-4 w-4" />
                            <option.icon className="h-4 w-4" />
                            {option.label}
                          </Button>
                        ))}
                    </div>

                    <div className="border border-dashed border-gray-300 rounded-md p-4 min-h-[400px]">
                      {getRooms().length === 0 ? (
                        <div className="flex flex-col items-center justify-center h-full text-center p-6">
                          <p className="text-sm text-gray-500 mb-4">
                            Nessuna area aggiunta. Aggiungi almeno una
                            sala, piano o settore per iniziare.
                          </p>
                        </div>
                      ) : (
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                          {getRooms().map((element) => (
                            <LayoutItem
                              key={element.id}
                              element={element}
                              isSelected={selectedElement?.id === element.id}
                              onSelect={() => handleElementSelect(element)}
                              onDelete={() => handleDeleteElement(element.id)}
                              onEnter={() => handleEnterRoom(element.id)}
                            />
                          ))}
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="elementi">
                    <div className="mb-4 flex flex-wrap gap-2">
                      {elementOptions
                        .filter(
                          (option) =>
                            option.value !== "sala" && option.value !== "piano"
                        )
                        .map((option) => (
                          <Button
                            key={option.value}
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-2"
                            onClick={() => handleAddElement(option.value)}
                          >
                            <Plus className="h-4 w-4" />
                            <option.icon className="h-4 w-4" />
                            {option.label}
                          </Button>
                        ))}
                    </div>

                    <div className="border border-dashed border-gray-300 rounded-md p-4 min-h-[400px]">
                      {elements.filter(
                        (e) => e.type !== "sala" && e.type !== "piano" && e.type !== "settore"
                      ).length === 0 ? (
                        <div className="flex flex-col items-center justify-center h-full text-center p-6">
                          <p className="text-sm text-gray-500 mb-4">
                            Nessun elemento aggiunto. Usa i pulsanti sopra per
                            aggiungere elementi al layout.
                          </p>
                        </div>
                      ) : (
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                          {elements
                            .filter(
                              (element) =>
                                element.type !== "sala" &&
                                element.type !== "piano" &&
                                element.type !== "settore"
                            )
                            .map((element) => (
                              <LayoutItem
                                key={element.id}
                                element={element}
                                isSelected={selectedElement?.id === element.id}
                                onSelect={() => handleElementSelect(element)}
                                onDelete={() => handleDeleteElement(element.id)}
                                parentName={getParentName(element)}
                              />
                            ))}
                        </div>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              )}
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Proprietà</CardTitle>
              <CardDescription>
                {selectedElement
                  ? `Modifica le proprietà di ${selectedElement.name}`
                  : "Seleziona un elemento per modificarne le proprietà"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedElement ? (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="element-name">Nome</Label>
                    <Input
                      id="element-name"
                      value={selectedElement.name}
                      onChange={(e) =>
                        handleElementUpdate({
                          ...selectedElement,
                          name: e.target.value,
                        })
                      }
                    />
                  </div>

                  {selectedElement.type !== "sala" &&
                    selectedElement.type !== "piano" &&
                    selectedElement.type !== "settore" && (
                      <div className="space-y-2">
                        <Label htmlFor="element-parent">Posizione</Label>
                        <Select
                          value={selectedElement.parentId || ""}
                          onValueChange={(value) =>
                            handleElementUpdate({
                              ...selectedElement,
                              parentId: value,
                            })
                          }
                        >
                          <SelectTrigger id="element-parent">
                            <SelectValue placeholder="Seleziona sala o piano" />
                          </SelectTrigger>
                          <SelectContent>
                            {getRooms().map((room) => (
                              <SelectItem key={room.id} value={room.id}>
                                {room.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                  <div className="space-y-2">
                    <Label htmlFor="element-capacity">Capacità (posti)</Label>
                    <Input
                      id="element-capacity"
                      type="number"
                      min="0"
                      value={selectedElement.capacity || 0}
                      onChange={(e) =>
                        handleElementUpdate({
                          ...selectedElement,
                          capacity: parseInt(e.target.value) || 0,
                        })
                      }
                    />
                  </div>

                  {(selectedElement.type === "sala" ||
                    selectedElement.type === "piano" ||
                    selectedElement.type === "settore") && (
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="element-width">Larghezza (m)</Label>
                        <Input
                          id="element-width"
                          type="number"
                          min="1"
                          step="0.1"
                          value={selectedElement.width || 10}
                          onChange={(e) =>
                            handleElementUpdate({
                              ...selectedElement,
                              width: parseFloat(e.target.value) || 10,
                            })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="element-height">Lunghezza (m)</Label>
                        <Input
                          id="element-height"
                          type="number"
                          min="1"
                          step="0.1"
                          value={selectedElement.height || 10}
                          onChange={(e) =>
                            handleElementUpdate({
                              ...selectedElement,
                              height: parseFloat(e.target.value) || 10,
                            })
                          }
                        />
                      </div>
                    </div>
                  )}

                  <Button
                    variant="destructive"
                    size="sm"
                    className="w-full mt-4"
                    onClick={() => handleDeleteElement(selectedElement.id)}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Elimina
                  </Button>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center p-8 text-center">
                  <p className="text-sm text-gray-500">
                    Seleziona un elemento dal layout per modificarne le
                    proprietà
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default LayoutEditor;
