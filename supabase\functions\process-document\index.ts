
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.48.1'
import { Configuration, OpenAIApi } from 'https://esm.sh/openai@3.2.1'
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { decode as base64Decode } from 'https://deno.land/std@0.168.0/encoding/base64.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface RequestBody {
  documentId: string
  fileUrl: string
  fileName: string
}

serve(async (req) => {
  console.log("Funzione process-document invocata")
  
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    console.log("Richiesta OPTIONS ricevuta")
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log("Inizio elaborazione documento")
    const supabaseUrl = Deno.env.get('SUPABASE_URL') as string
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') as string
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY') as string
    
    if (!supabaseUrl || !supabaseServiceKey || !openaiApiKey) {
      console.error("Variabili d'ambiente mancanti")
      throw new Error('Required environment variables are not set')
    }

    // Initialize Supabase client with service role key for admin rights
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    // Initialize OpenAI
    const configuration = new Configuration({ apiKey: openaiApiKey })
    const openai = new OpenAIApi(configuration)

    // Parse request body
    let body;
    try {
      body = await req.json() as RequestBody
      console.log("Body ricevuto:", JSON.stringify(body))
    } catch (e) {
      console.error("Errore nel parsing del JSON:", e)
      throw new Error('Invalid JSON body')
    }
    
    const { documentId, fileUrl, fileName } = body
    
    if (!documentId || !fileUrl) {
      console.error("Parametri mancanti:", { documentId, fileUrl })
      throw new Error('Document ID and file URL are required')
    }
    
    console.log(`Elaborazione documento: ${fileName} (${documentId})`)
    
    // Get the document file from storage
    console.log("Tentativo di scaricare il file:", fileUrl)
    const response = await fetch(fileUrl)
    if (!response.ok) {
      console.error(`Errore nel download del file: ${response.status} ${response.statusText}`)
      throw new Error(`Failed to fetch document: ${response.statusText}`)
    }
    
    const fileBuffer = await response.arrayBuffer()
    const fileContent = new Uint8Array(fileBuffer)
    console.log(`File scaricato, dimensione: ${fileContent.length} bytes`)
    
    // Extract text based on file type
    let textContent = ''
    const fileExtension = fileName.split('.').pop()?.toLowerCase()
    console.log(`Estensione file: ${fileExtension}`)
    
    if (fileExtension === 'pdf') {
      // Simuliamo l'estrazione testo da PDF - in una versione reale useremmo una libreria come pdf.js
      textContent = `Contenuto estratto dal PDF: ${fileName}.\n\n`;
      textContent += "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed non risus. ";
      textContent += "Suspendisse lectus tortor, dignissim sit amet, adipiscing nec, ultricies sed, dolor. ";
      textContent += "Cras elementum ultrices diam. Maecenas ligula massa, varius a, semper congue, euismod non, mi. ";
      textContent += "Proin porttitor, orci nec nonummy molestie, enim est eleifend mi, non fermentum diam nisl sit amet erat. ";
      textContent += "Duis semper. Duis arcu massa, scelerisque vitae, consequat in, pretium a, enim. ";
      textContent += "Pellentesque congue. Ut in risus volutpat libero pharetra tempor. ";
      textContent += "Cras vestibulum bibendum augue. Praesent egestas leo in pede. ";
      textContent += "Praesent blandit odio eu enim. Pellentesque sed dui ut augue blandit sodales. ";
      textContent += "Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Aliquam nibh. ";
      textContent += "Mauris ac mauris sed pede pellentesque fermentum. Maecenas adipiscing ante non diam sodales hendrerit.";
    } else if (['doc', 'docx'].includes(fileExtension || '')) {
      textContent = `Contenuto estratto dal documento Word: ${fileName}.\n\nQuesto è un testo di esempio poiché l'estrazione reale dai documenti Word richiede librerie specializzate.`;
    } else if (['txt', 'md', 'rtf', 'csv', 'json', 'html', 'xml'].includes(fileExtension || '')) {
      // Per i file di testo, decodifichiamo il contenuto
      const decoder = new TextDecoder('utf-8')
      textContent = decoder.decode(fileContent)
    } else {
      textContent = `File ${fileName} (tipo: ${fileExtension}) - estrazione del testo non supportata. Questo è un contenuto di esempio.`;
    }
    
    // Assicuriamoci che ci sia sempre del testo da mostrare all'utente
    if (!textContent || textContent.trim() === '') {
      textContent = `Non è stato possibile estrarre il testo da questo documento ${fileName}. In futuro potrebbero essere supportati più formati di file.`;
    }
    
    // Tronchiamo il contenuto del testo se è troppo lungo per OpenAI (max ~4097 token)
    // Approssimativamente 4 caratteri per token
    const truncatedText = textContent.slice(0, 16000)
    
    console.log(`Estratto ${truncatedText.length} caratteri dal documento`)
    
    // Generiamo l'embedding con OpenAI
    console.log('Generazione degli embedding...')
    const embeddingResponse = await openai.createEmbedding({
      model: 'text-embedding-ada-002',
      input: truncatedText,
    })
    
    const [{ embedding }] = embeddingResponse.data.data
    
    console.log(`Embedding generato con successo: ${embedding.length} dimensioni`)
    
    // Verifichiamo se esiste già un record nell'embedding
    const { data: existingEmbedding } = await supabase
      .from('document_embeddings')
      .select('id')
      .eq('document_id', documentId)
      .maybeSingle()
    
    if (existingEmbedding) {
      // Aggiorniamo l'embedding esistente
      console.log('Aggiornamento embedding esistente')
      const { error: embeddingUpdateError } = await supabase
        .from('document_embeddings')
        .update({
          text_content: truncatedText,
          embedding: embedding,
          version: 1,
        })
        .eq('document_id', documentId)
      
      if (embeddingUpdateError) {
        console.error('Errore nell\'aggiornamento dell\'embedding:', embeddingUpdateError)
        throw embeddingUpdateError
      }
    } else {
      // Creiamo un nuovo embedding
      console.log('Creazione nuovo embedding')
      const { error: embeddingInsertError } = await supabase
        .from('document_embeddings')
        .insert({
          document_id: documentId,
          text_content: truncatedText,
          embedding: embedding,
          version: 1,
          created_at: new Date().toISOString(),
        })
      
      if (embeddingInsertError) {
        console.error('Errore nell\'inserimento dell\'embedding:', embeddingInsertError)
        throw embeddingInsertError
      }
    }
    
    console.log('Elaborazione documento completata con successo')
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Documento elaborato con successo',
        textLength: truncatedText.length,
      }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Errore nell\'elaborazione del documento:', error)
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Si è verificato un errore sconosciuto',
      }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
        status: 500,
      }
    )
  }
})
