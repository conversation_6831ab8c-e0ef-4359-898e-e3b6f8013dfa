import { CreditCard, Check, Zap, Users, Bot, AlertCircle, Star, Crown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import MainLayout from "@/layouts/MainLayout";
import { useBusinessStore } from "@/store/businessStore";
import {
  usePricingTiersWithCurrent,
  formatPrice,
  getAgentTypeLabel,
} from "@/queries/usePricingTiers";
import { PricingCard } from "@/components/PricingCard";
import { useBusinessLimits } from "@/hooks/useBusinessLimits";
import { useState } from "react";
export type SubscriptionPlan = 'basic' | 'professional' | 'enterprise';
const SubscriptionPlan = () => {
  const selectedBusiness = useBusinessStore((state) => state.selectedBusiness);
  const {
    data: tiers,
    isLoading,
    error,
  } = usePricingTiersWithCurrent(selectedBusiness?.owner_id);
  const { data: businessLimits, isLoading: limitsLoading } =
    useBusinessLimits();
  const [isYearly, setIsYearly] = useState(false);

  console.log("selectedBusiness", selectedBusiness);
  // Calculate the price based on billing cycle
  const getPrice = (tier: any) => {
    return isYearly ? tier.price_yearly : tier.price_monthly;
  };

  // Calculate yearly savings percentage
  const getYearlySavings = (tier: any) => {
    const monthlyTotal = tier.price_monthly * 12;
    const yearlyPrice = tier.price_yearly;
    const savings = ((monthlyTotal - yearlyPrice) / monthlyTotal) * 100;
    return Math.round(savings);
  };

  const getPlanIcon = (planId: SubscriptionPlan) => {
    switch (planId) {
      case "basic":
        return <Users className="w-6 h-6" />;
      case "professional":
        return <Star className="w-6 h-6" />;
      case "enterprise":
        return <Crown className="w-6 h-6" />;
      default:
        return <Users className="w-6 h-6" />;
    }
  };
  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Piano Abbonamento
            </h1>
            <p className="text-gray-600">
              Gestisci il tuo piano e i tuoi agenti AI Voice
            </p>
            {selectedBusiness && (
              <p className="text-sm text-gray-500 mt-1">
                Attività: {selectedBusiness.name}
              </p>
            )}

            {/* Billing Cycle Toggle - Loading State */}
            <div className="flex items-center justify-center mt-6 space-x-4">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-6 w-12" />
              <Skeleton className="h-4 w-16" />
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-32" />
                  <Skeleton className="h-4 w-48" />
                  <Skeleton className="h-8 w-24" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-32 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Piano Abbonamento
            </h1>
            <p className="text-gray-600">
              Gestisci il tuo piano e i tuoi agenti AI Voice
            </p>
            {selectedBusiness && (
              <p className="text-sm text-gray-500 mt-1">
                Attività: {selectedBusiness.name}
              </p>
            )}

            {/* Billing Cycle Toggle - Error State */}
            <div className="flex items-center justify-center mt-6 space-x-4">
              <Label
                htmlFor="billing-toggle-error"
                className={`text-sm font-medium ${
                  !isYearly ? "text-gray-900" : "text-gray-500"
                }`}
              >
                Mensile
              </Label>
              <Switch
                id="billing-toggle-error"
                checked={isYearly}
                onCheckedChange={setIsYearly}
              />
              <Label
                htmlFor="billing-toggle-error"
                className={`text-sm font-medium ${
                  isYearly ? "text-gray-900" : "text-gray-500"
                }`}
              >
                Annuale
              </Label>
              {isYearly && (
                <Badge variant="secondary" className="ml-2">
                  Risparmia fino al 20%
                </Badge>
              )}
            </div>
          </div>

          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Errore durante il caricamento dei piani tariffari. Riprova più
              tardi.
            </AlertDescription>
          </Alert>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Piano Abbonamento
          </h1>
          <p className="text-gray-600">
            Gestisci il tuo piano e i tuoi agenti AI Voice
          </p>
          {selectedBusiness && (
            <p className="text-sm text-gray-500 mt-1">
              Attività: {selectedBusiness.name}
            </p>
          )}

          {/* Business Limits Info */}
          {businessLimits && !limitsLoading && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium text-blue-900">
                    I tuoi Business
                  </h3>
                  <p className="text-sm text-blue-700">
                    {businessLimits.currentBusinessCount} di{" "}
                    {businessLimits.isUnlimited
                      ? "∞"
                      : businessLimits.maxBusinesses}{" "}
                    business utilizzati
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-xs text-blue-600">
                    Piano: {businessLimits.highestTier}
                  </p>
                  {!businessLimits.canCreateMore && (
                    <p className="text-xs text-orange-600 font-medium">
                      Limite raggiunto
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Billing Cycle Toggle */}
          <div className="flex items-center justify-center mt-6 space-x-4">
            <Label
              htmlFor="billing-toggle"
              className={`text-sm font-medium ${
                !isYearly ? "text-gray-900" : "text-gray-500"
              }`}
            >
              Mensile
            </Label>
            <Switch
              id="billing-toggle"
              checked={isYearly}
              onCheckedChange={setIsYearly}
            />
            <Label
              htmlFor="billing-toggle"
              className={`text-sm font-medium ${
                isYearly ? "text-gray-900" : "text-gray-500"
              }`}
            >
              Annuale
            </Label>
            {isYearly && tiers.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                Risparmia fino al{" "}
                {Math.max(...tiers.map((tier) => getYearlySavings(tier)))}%
              </Badge>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {tiers.map((tier) => {
            const isPopular = tier.tier_type === "professional" && !tier.current;
            return (
              <PricingCard
                key={tier.id}
                tier={tier}
                isYearly={isYearly}
                isPopular={isPopular}
                showCurrentBadge={true}
                businessLimits={businessLimits}
                showAgentDetails={true}
                showBusinessLimits={true}
                buttonText={tier.current ? "Piano Attuale" : `Passa a ${tier.name}`}
              />
            );
          })}
        </div>

        <div className="mt-12 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Hai domande?
          </h2>
          <p className="text-gray-600 mb-6">
            Contattaci per discutere quale piano è più adatto alle tue esigenze
          </p>
          <Button variant="outline">
            <Users className="h-4 w-4 mr-2" />
            Contatta il Supporto
          </Button>
        </div>
      </div>
    </MainLayout>
  );
};

export default SubscriptionPlan;
