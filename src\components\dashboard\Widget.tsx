import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Widget as WidgetType, WidgetSize } from '@/types/widget';
import { motion } from 'framer-motion';
import { Calendar, Package, Users, BarChart4, Clock, Loader2, ChevronRight, MoreHorizontal, Trash2, DollarSign, TrendingUp, Tag,  Maximize2, Minimize2, LayoutGrid } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useWidgetStore } from '@/store/widgetStore';
import { toast } from 'sonner';


interface WidgetProps {
  widget: WidgetType;
  children: React.ReactNode;
  className?: string;
  onRemove?: () => void;

  actionLabel?: string;
  actionLink?: string;
  actionOnClick?: () => void;
}

// Mappa delle icone per i widget
const iconMap: Record<string, React.ReactNode> = {
  'Calendar': <Calendar className="h-6 w-6 text-primary-500" />,
  'Package': <Package className="h-6 w-6 text-blue-500" />,
  'Users': <Users className="h-6 w-6 text-violet-500" />,
  'BarChart4': <BarChart4 className="h-6 w-6 text-green-500" />,
  'Clock': <Clock className="h-6 w-6 text-orange-500" />,
  'DollarSign': <DollarSign className="h-6 w-6 text-green-600" />,
  'TrendingUp': <TrendingUp className="h-6 w-6 text-blue-600" />,
  'Tag': <Tag className="h-6 w-6 text-purple-600" />
};

// Mappa delle etichette per le dimensioni dei widget
const sizeLabels: Record<WidgetSize, string> = {
  [WidgetSize.SMALL]: 'Piccolo (1 colonna)',
  [WidgetSize.MEDIUM]: 'Medio (2 colonne)',
  [WidgetSize.LARGE]: 'Grande (3 colonne)',
  [WidgetSize.FULL]: 'Completo (6 colonne)'
};

// Mappa delle icone per le dimensioni dei widget
const sizeIcons: Record<WidgetSize, React.ReactNode> = {
  [WidgetSize.SMALL]: <Minimize2 className="mr-2 h-4 w-4" />,
  [WidgetSize.MEDIUM]: <LayoutGrid className="mr-2 h-4 w-4" />,
  [WidgetSize.LARGE]: <Maximize2 className="mr-2 h-4 w-4" />,
  [WidgetSize.FULL]: <Maximize2 className="mr-2 h-4 w-4" />
};

// Funzione per ottenere le classi CSS in base alla dimensione del widget
const getSizeClasses = (size: WidgetSize): string => {
  // Con il layout masonry, non abbiamo bisogno di classi grid specifiche
  // Usiamo classi generiche per definire lo stile del widget
  switch (size) {
    case WidgetSize.SMALL:
      return 'col-span-1 md:col-span-1 row-span-1'; // 1 colonna
    case WidgetSize.MEDIUM:
      return 'col-span-1 md:col-span-2 row-span-1'; // 2 colonne
    case WidgetSize.LARGE:
      return 'col-span-1 md:col-span-3 row-span-1'; // 3 colonne
    case WidgetSize.FULL:
      return 'col-span-1 md:col-span-6 row-span-1'; // 6 colonne (larghezza completa)
    default:
      return 'col-span-1 md:col-span-1 row-span-1';
  }
};

// Componente Widget
const Widget: React.FC<WidgetProps> = React.memo(({
  widget,
  children,
  className,
  onRemove,
  actionLabel,
  actionLink,
  actionOnClick
}) => {
  // Ottieni la funzione updateWidget dallo store
  const { updateWidget } = useWidgetStore();



  // Stato per la finestra di dialogo di conferma
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Gestisce il click sul pulsante di rimozione
  const handleRemoveClick = () => {
    setShowDeleteConfirm(true);
  };

  // Gestisce la conferma di rimozione
  const handleConfirmRemove = () => {
    // Chiama sempre la funzione onRemove se disponibile
    if (typeof onRemove === 'function') {
      onRemove();
    }

    // Chiudi sempre la finestra di dialogo
    setShowDeleteConfirm(false);
  };

  // Gestisce il cambio di dimensione del widget
  const handleSizeChange = (newSize: WidgetSize) => {
    if (widget.size === newSize) return;

    updateWidget(widget.id, { size: newSize });
    toast.success(`Dimensione del widget "${widget.title}" modificata`);
  };


  // Calcola il ritardo dell'animazione in base alla posizione
  const animationDelay = widget.position * 0.1;

  // Ottieni l'icona dal widget o usa un'icona predefinita
  const icon = widget.icon ? iconMap[widget.icon] : null;

  // Ottieni le classi CSS per la dimensione del widget
  const sizeClasses = getSizeClasses(widget.size);

  return (
    <>

    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: animationDelay }}
      className={cn(sizeClasses, 'widget-container', className)}
      layout
    >
      <Card className="h-full flex flex-col overflow-hidden">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between gap-2 mb-1">
            <div className="flex items-center gap-2">
              {icon && <div className="flex-shrink-0">{icon}</div>}
              <CardTitle className="text-lg">{widget.title}</CardTitle>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8 text-muted-foreground hover:text-foreground">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => handleSizeChange(WidgetSize.SMALL)}
                  disabled={widget.size === WidgetSize.SMALL}
                >
                  {sizeIcons[WidgetSize.SMALL]}
                  {sizeLabels[WidgetSize.SMALL]}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleSizeChange(WidgetSize.MEDIUM)}
                  disabled={widget.size === WidgetSize.MEDIUM}
                >
                  {sizeIcons[WidgetSize.MEDIUM]}
                  {sizeLabels[WidgetSize.MEDIUM]}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleSizeChange(WidgetSize.LARGE)}
                  disabled={widget.size === WidgetSize.LARGE}
                >
                  {sizeIcons[WidgetSize.LARGE]}
                  {sizeLabels[WidgetSize.LARGE]}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleRemoveClick}
                  className="text-red-600 focus:text-red-600 mt-2 border-t"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Rimuovi widget
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          {widget.description && (
            <CardDescription className="text-sm text-muted-foreground">
              {widget.description}
            </CardDescription>
          )}
        </CardHeader>
        <CardContent className="flex-grow pb-2 overflow-auto">
          {widget.isLoading ? (
            <div className="flex items-center justify-center h-24">
              <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
            </div>
          ) : (
            <div className="h-full">
              {children}
            </div>
          )}
        </CardContent>
        {(actionLabel || actionLink || actionOnClick) && (
          <CardFooter className="pt-0">
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-between text-primary hover:text-primary-600 hover:bg-primary-50"
              onClick={actionOnClick}
              asChild={!!actionLink}
            >
              {actionLink ? (
                <a href={actionLink} className="flex w-full justify-between">
                  {actionLabel || 'Visualizza'}
                  <ChevronRight className="h-4 w-4" />
                </a>
              ) : (
                <>
                  {actionLabel || 'Visualizza'}
                  <ChevronRight className="h-4 w-4" />
                </>
              )}
            </Button>
          </CardFooter>
        )}
      </Card>
    </motion.div>


    <AlertDialog open={showDeleteConfirm === true} onOpenChange={(open) => setShowDeleteConfirm(open)}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Rimuovere il widget?</AlertDialogTitle>
          <AlertDialogDescription>
            Sei sicuro di voler rimuovere il widget "{widget.title}"? Questa azione non può essere annullata.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Annulla</AlertDialogCancel>
          <AlertDialogAction onClick={handleConfirmRemove} className="bg-red-600 hover:bg-red-700 text-white">
            Rimuovi
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
    </>
  );
});


export default Widget;
