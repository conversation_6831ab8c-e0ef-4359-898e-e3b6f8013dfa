-- Create the ai_business_agents table
create table public.ai_business_agents (
  id uuid not null default gen_random_uuid(),
  name text not null,
  description text null,
  image_url text null,
  assistant_id text not null,
  created_at timestamp with time zone not null default timezone('utc'::text, now()),
  updated_at timestamp with time zone not null default timezone('utc'::text, now()),
  voice_id text null,
  constraint ai_business_agents_pkey primary key (id)
);

-- Create the handle_updated_at function if it doesn't exist
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  NEW.updated_at = timezone('utc'::text, now());
  RETURN NEW;
END;
$$;

-- Create the trigger for updated_at
CREATE TRIGGER set_updated_at 
  BEFORE UPDATE ON public.ai_business_agents 
  FOR EACH ROW 
  EXECUTE FUNCTION handle_updated_at();

-- Enable RLS
ALTER TABLE public.ai_business_agents ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (assuming similar access pattern to business_ai_agents)
CREATE POLICY "Enable read access for all users" 
ON public.ai_business_agents 
FOR SELECT 
USING (true);