
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AnimatePresence } from "framer-motion";
import { AuthProvider } from "./contexts/AuthContext";
import ProtectedRoute from "./components/ProtectedRoute";
import { AdminRoute } from "./components/AdminRoute";
import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import Auth from "./pages/Auth";
import NotFound from "./pages/NotFound";
import Deals from "./pages/deals/Deals";
import DealDetail from "./pages/DealDetail";

import Settings from "./pages/Settings";
import UserManager from "./pages/UserManager";
import Calendar from "./pages/Calendar";
import Documents from "./pages/Documents";
import Pricing from "./pages/Pricing";
import LayoutPage from "./pages/LayoutPage";
import Categories from "./pages/Categories";
import BusinessProductCategories from "./pages/BusinessProductCategories";
import Integrations from "./pages/Integrations";
import SubscriptionPlan from "./pages/SubscriptionPlan";
import AIAgents from "./pages/AIAgents";
import AgentConfig from "./pages/AgentConfig";
import AdminBusinesses from "./pages/AdminBusinesses";
import AdminAgentDefinitions from "./pages/AdminAgentDefinitions";
import AdminPricingTiers from "./pages/AdminPricingTiers";
import EditBusiness from "./pages/EditBusiness";
import CreateBusiness from "./pages/CreateBusiness";
import UpsertDeal from "./pages/deals/upsert-deal";
import AdminDeals from "./pages/AdminDeals";
import AdminFakeData from "./pages/AdminFakeData";
import AdminCampaigns from "./pages/AdminCampaigns";
import AdminEmailTemplates from "./pages/AdminEmailTemplates";
import AdminWaitlist from "./pages/AdminWaitlist";
import AdminLeads from "./pages/AdminLeads";
import ListBusinesses from "./pages/ListBusinesses";
import AdminCatchUpPlan from "./pages/AdminCatchUpPlan";
import AdminReviews from "./pages/AdminReviews";
import AdminSubcategories from "./pages/AdminSubcategories";
import AdminDealCategories from "./pages/AdminDealCategories";
import AdminSearchMap from "./pages/AdminSearchMap";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <AnimatePresence mode="wait">
            <Routes>
              {/* Public routes */}
              <Route path="/" element={<Index />} />
              <Route path="/auth" element={<Auth />} />
            
               {/* Protected routes */}
              <Route element={<ProtectedRoute />}>
                
                <Route path="/catchup/plan" element={<AdminCatchUpPlan />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/calendar" element={<Calendar />} />

                <Route path="/deals" element={<Deals />} />
                <Route path="/deals/upsert" element={<UpsertDeal />} />


                <Route path="/deals/:id" element={<DealDetail />} />
                <Route path="/documents" element={<Documents />} />
                <Route path="/pricing" element={<Pricing />} />
                <Route path="/layout" element={<LayoutPage />} />
                <Route path="/settings" element={<Settings />} />
              
               
                <Route path="/business-product-categories" element={<BusinessProductCategories />} />
                <Route path="/integrations" element={<Integrations />} />
                <Route path="/subscription-plan" element={<SubscriptionPlan />} />
                <Route path="/ai-agents" element={<AIAgents />} />
                <Route path="/ai-agents/:agentId/config" element={<AgentConfig />} />
             
                <Route path="/create-business" element={<CreateBusiness />} />
                <Route path="/business/:id/edit" element={<EditBusiness />} />
                <Route path="/list-businesses" element={<ListBusinesses />} />
              </Route>

              {/* Admin routes */}
              <Route element={<AdminRoute><ProtectedRoute /></AdminRoute>}>
                <Route path="/admin/fake-data" element={<AdminFakeData />} />
                <Route path="/admin/businesses" element={<AdminBusinesses />} />
                <Route path="/admin/deals" element={<AdminDeals />} />
                <Route path="/admin/leads" element={<AdminLeads />} />
                <Route path="/admin/waitlist" element={<AdminWaitlist />} />
                <Route path="/admin/campaigns" element={<AdminCampaigns />} />
                <Route path="/admin/email-templates" element={<AdminEmailTemplates />} />
                <Route path="/admin/agent-definitions" element={<AdminAgentDefinitions />} />
                <Route path="/admin/pricing-tiers" element={<AdminPricingTiers />} />
                <Route path="/admin/reviews" element={<AdminReviews />} />
                <Route path="/admin/subcategories" element={<AdminSubcategories />} />
                <Route path="/admin/deal-categories" element={<AdminDealCategories />} />
                <Route path="/admin/search-map" element={<AdminSearchMap />} />
                <Route path="/usermanager" element={<UserManager />} />
                <Route path="/categories" element={<Categories />} />
              </Route>

              {/* Catch-all route */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </AnimatePresence>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
