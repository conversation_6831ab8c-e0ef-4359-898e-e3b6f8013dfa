
import { useState } from 'react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

interface IntegrationAuth {
  id: string;
  user_id: string;
  business_id: string;
  integration_id: string;
  provider: string;
  access_token: string;
  refresh_token?: string;
  expires_at?: string;
  scope?: string;
  authorized_at: string;
  is_active: boolean;
}

export const useIntegrationAuth = () => {
  const [loading, setLoading] = useState(false);

  const authorizeGoogleCalendar = async (businessId: string) => {
    setLoading(true);
    try {
      // In a real implementation, this would redirect to Google OAuth
      // For now, we'll simulate the authorization process
      console.log('Authorizing Google Calendar for business:', businessId);
      
      // Simulate OAuth callback with tokens
      const mockTokens = {
        access_token: 'mock_access_token_' + Date.now(),
        refresh_token: 'mock_refresh_token_' + Date.now(),
        expires_at: new Date(Date.now() + 3600000).toISOString(), // 1 hour
        scope: 'https://www.googleapis.com/auth/calendar'
      };

      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('User not authenticated');

      // Store authorization in database using raw query to avoid type issues
      const { error } = await supabase
        .from('integration_authorizations' as any)
        .upsert({
          user_id: user.user.id,
          business_id: businessId,
          integration_id: 'google-calendar',
          provider: 'google',
          access_token: mockTokens.access_token,
          refresh_token: mockTokens.refresh_token,
          expires_at: mockTokens.expires_at,
          scope: mockTokens.scope,
          authorized_at: new Date().toISOString(),
          is_active: true
        });

      if (error) throw error;

      toast.success('Google Calendar autorizzato con successo');
      return true;
    } catch (error) {
      console.error('Error authorizing Google Calendar:', error);
      toast.error('Errore durante l\'autorizzazione di Google Calendar');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const revokeAuthorization = async (integrationId: string, businessId: string) => {
    setLoading(true);
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('integration_authorizations' as any)
        .update({ is_active: false })
        .eq('user_id', user.user.id)
        .eq('business_id', businessId)
        .eq('integration_id', integrationId);

      if (error) throw error;

      toast.success('Autorizzazione revocata con successo');
      return true;
    } catch (error) {
      console.error('Error revoking authorization:', error);
      toast.error('Errore durante la revoca dell\'autorizzazione');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const getAuthorization = async (integrationId: string, businessId: string): Promise<IntegrationAuth | null> => {
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) return null;

      const { data, error } = await supabase
        .from('integration_authorizations' as any)
        .select('*')
        .eq('user_id', user.user.id)
        .eq('business_id', businessId)
        .eq('integration_id', integrationId)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching authorization:', error);
        return null;
      }

      // Check if data exists and is not an error before casting
      if (!data || typeof data === 'string') {
        return null;
      }

      return data as unknown as IntegrationAuth;
    } catch (error) {
      console.error('Error fetching authorization:', error);
      return null;
    }
  };

  return {
    loading,
    authorizeGoogleCalendar,
    revokeAuthorization,
    getAuthorization
  };
};
