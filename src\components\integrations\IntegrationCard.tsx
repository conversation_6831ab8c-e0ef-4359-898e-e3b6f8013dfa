
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ExternalLink, Settings, CheckCircle, Clock, AlertCircle } from "lucide-react";

interface Integration {
  id: string;
  name: string;
  description: string;
  icon: React.ElementType;
  category: string;
  status: 'connected' | 'available' | 'coming-soon';
  enabled: boolean;
  premium?: boolean;
  features: string[];
  setup?: {
    apiKey?: string;
    webhook?: string;
    settings?: Record<string, any>;
  };
}

interface IntegrationCardProps {
  integration: Integration;
  onToggle: (integrationId: string) => void;
  onAuthorize?: (integrationId: string) => void;
  onRevoke?: (integrationId: string) => void;
}

export const IntegrationCard = ({ integration, onToggle, onAuthorize, onRevoke }: IntegrationCardProps) => {
  const IconComponent = integration.icon;
  
  const getStatusBadge = (status: Integration['status']) => {
    switch (status) {
      case 'connected':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Connessa</Badge>;
      case 'available':
        return <Badge variant="outline"><Clock className="h-3 w-3 mr-1" />Disponibile</Badge>;
      case 'coming-soon':
        return <Badge variant="secondary"><AlertCircle className="h-3 w-3 mr-1" />Prossimamente</Badge>;
      default:
        return null;
    }
  };

  const isComingSoon = integration.status === 'coming-soon';

  return (
    <Card className="relative overflow-hidden">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-gray-100">
              <IconComponent className="h-6 w-6 text-gray-700" />
            </div>
            <div>
              <CardTitle className="text-lg flex items-center gap-2">
                {integration.name}
                {integration.premium && (
                  <Badge variant="outline" className="text-xs">Pro</Badge>
                )}
              </CardTitle>
              <div className="flex items-center gap-2 mt-1">
                {getStatusBadge(integration.status)}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {!isComingSoon && (
              <Switch
                checked={integration.enabled}
                onCheckedChange={() => onToggle(integration.id)}
                disabled={isComingSoon}
              />
            )}
            <Button variant="ghost" size="sm">
              <ExternalLink className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <CardDescription className="mb-4">
          {integration.description}
        </CardDescription>
        
        <div className="space-y-3">
          <div>
            <h4 className="text-sm font-medium mb-2">Funzionalità:</h4>
            <div className="flex flex-wrap gap-1">
              {integration.features.map((feature, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {feature}
                </Badge>
              ))}
            </div>
          </div>
          
          {integration.enabled && integration.status === 'connected' && (
            <div className="pt-2 border-t space-y-2">
              <Button variant="outline" size="sm" className="w-full">
                <Settings className="h-4 w-4 mr-2" />
                Configura
              </Button>
              {onRevoke && (
                <Button variant="destructive" size="sm" className="w-full" onClick={() => onRevoke(integration.id)}>
                  Revoca Autorizzazione
                </Button>
              )}
            </div>
          )}
          
          {integration.enabled && integration.status === 'available' && onAuthorize && (
            <div className="pt-2 border-t">
              <Button size="sm" className="w-full" onClick={() => onAuthorize(integration.id)}>
                Autorizza
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
