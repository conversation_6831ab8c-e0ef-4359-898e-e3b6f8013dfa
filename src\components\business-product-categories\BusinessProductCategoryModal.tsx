
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { BusinessProductCategory } from "@/types/types";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";

const businessProductCategorySchema = z.object({
  name: z.string().min(1, "Il nome è obbligatorio").max(100, "Il nome non può superare i 100 caratteri"),
  description: z.string().max(500, "La descrizione non può superare i 500 caratteri").optional(),
  icon: z.string().max(50, "L'icona non può superare i 50 caratteri").optional(),
});

type BusinessProductCategoryFormData = z.infer<typeof businessProductCategorySchema>;

interface BusinessProductCategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  category?: BusinessProductCategory | null;
  businessId: string;
}

export const BusinessProductCategoryModal = ({ 
  isOpen, 
  onClose, 
  category, 
  businessId 
}: BusinessProductCategoryModalProps) => {
  const queryClient = useQueryClient();
  const isEditing = !!category;

  const form = useForm<BusinessProductCategoryFormData>({
    resolver: zodResolver(businessProductCategorySchema),
    defaultValues: {
      name: "",
      description: "",
      icon: "",
    },
  });

  // Reset form when modal opens/closes or category changes
  useEffect(() => {
    if (isOpen) {
      if (category) {
        form.reset({
          name: category.name,
          description: category.description || "",
          icon: category.icon || "",
        });
      } else {
        form.reset({
          name: "",
          description: "",
          icon: "",
        });
      }
    }
  }, [isOpen, category, form]);

  // Create category mutation
  const createMutation = useMutation({
    mutationFn: async (data: BusinessProductCategoryFormData) => {
      const { error } = await supabase
        .from("business_product_categories")
        .insert({
          name: data.name,
          description: data.description || null,
          icon: data.icon || null,
          business_id: businessId,
        });

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["business-product-categories", businessId] });
      toast.success("Categoria prodotto creata con successo");
      onClose();
    },
    onError: (error) => {
      console.error("Error creating business product category:", error);
      toast.error("Errore nella creazione della categoria prodotto");
    },
  });

  // Update category mutation
  const updateMutation = useMutation({
    mutationFn: async (data: BusinessProductCategoryFormData) => {
      if (!category) throw new Error("Category not found");

      const { error } = await supabase
        .from("business_product_categories")
        .update({
          name: data.name,
          description: data.description || null,
          icon: data.icon || null,
          updated_at: new Date().toISOString(),
        })
        .eq("id", category.id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["business-product-categories", businessId] });
      toast.success("Categoria prodotto aggiornata con successo");
      onClose();
    },
    onError: (error) => {
      console.error("Error updating business product category:", error);
      toast.error("Errore nell'aggiornamento della categoria prodotto");
    },
  });

  const onSubmit = (data: BusinessProductCategoryFormData) => {
    if (isEditing) {
      updateMutation.mutate(data);
    } else {
      createMutation.mutate(data);
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Modifica Categoria Prodotto" : "Nuova Categoria Prodotto"}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? "Modifica i dettagli della categoria prodotto." 
              : "Crea una nuova categoria per i prodotti della tua attività."
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome *</FormLabel>
                  <FormControl>
                    <Input placeholder="Nome della categoria prodotto" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrizione</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Descrizione della categoria prodotto (opzionale)"
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="icon"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Icona</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Nome dell'icona (es. package, box, tag)"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Annulla
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Salvando..." : isEditing ? "Aggiorna" : "Crea"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
