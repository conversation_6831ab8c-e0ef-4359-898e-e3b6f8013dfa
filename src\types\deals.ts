export type TimeSlot = {
  start_time: string;
  end_time: string;
  available_seats: number;
  booked_seats?: number;
};

export type DealStatus = 'draft' | 'published' | 'expired';

export type DaySchedule = {
  day: number;
  day_name: string;
  time_slots: TimeSlot[];
};

export type WeeklySchedule = {
  schedule: DaySchedule[];
  exceptions: string[];
};

export type FormData = {
  title: string;
  description: string;
  original_price: string;
  discount_percentage: string;
  discounted_price: string;
  start_date: string;
  end_date: string;
  time_slots: WeeklySchedule;
  images: File[];
  status: DealStatus;
  deal_categories: string[]; // Array of deal category IDs
};