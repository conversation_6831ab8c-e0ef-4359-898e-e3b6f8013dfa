import React from 'react';
import Widget from './Widget';
import { ClientsWidget as ClientsWidgetType } from '@/types/widget';
import { Users } from 'lucide-react';

interface ClientsWidgetProps {
  widget: ClientsWidgetType;
  className?: string;
  onRemove?: () => void;
}

const ClientsWidget: React.FC<ClientsWidgetProps> = React.memo(({ widget, className, onRemove }) => {
  return (
    <Widget
      widget={widget}
      className={className}
      actionLabel="Visualizza Clienti"
      onRemove={onRemove}
    >
      <div className="w-full py-2">
        <div className="flex flex-col items-center justify-center text-center">
          <span className="text-4xl font-bold text-violet-600">
            {widget.isLoading ? "-" : widget.data.count}
          </span>
          <span className="text-sm text-gray-500 mt-1">
            {widget.data.count === 1 ? "cliente" : "clienti"} registrati
          </span>

          <div className="w-full mt-4 bg-gray-100 h-1 rounded-full overflow-hidden">
            <div
              className="bg-violet-500 h-full rounded-full"
              style={{ width: `${Math.min(100, widget.data.count / 2)}%` }}
            ></div>
          </div>
          <span className="text-xs text-gray-500 mt-1">
            {Math.min(100, Math.round(widget.data.count / 2))}% dell'obiettivo
          </span>
        </div>
      </div>
    </Widget>
  );
});

export default ClientsWidget;
