
import { useState } from 'react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

export type UserRole = 'user' | 'admin' | 'business_owner';

export const useRoleManagement = () => {
  const [isUpdating, setIsUpdating] = useState(false);

  const updateUserRole = async (userId: string, newRole: UserRole) => {
    setIsUpdating(true);
    try {
      const { error } = await supabase.rpc('update_user_metadata', {
        target_user_id: userId,
        new_metadata: { role: newRole }
      });

      if (error) throw error;

      toast.success("Ruolo utente aggiornato con successo");
      return true;
    } catch (error) {
      console.error('Error updating user role:', error);
      toast.error("Errore durante l'aggiornamento del ruolo");
      return false;
    } finally {
      setIsUpdating(false);
    }
  };

  const getUserRole = async (userId: string): Promise<UserRole | null> => {
    try {
      const { data, error } = await supabase.rpc('get_user_role', {
        user_id: userId
      });

      if (error) throw error;
      return data as UserRole;
    } catch (error) {
      console.error('Error getting user role:', error);
      return null;
    }
  };

  return {
    isUpdating,
    updateUserRole,
    getUserRole
  };
};
