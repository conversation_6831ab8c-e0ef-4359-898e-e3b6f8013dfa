# ScrollToTop Component

A customizable "scroll to top" button component for React applications using Tailwind CSS.

## Features

- Appears when the user scrolls down the page
- Smooth scrolling animation when clicked
- Fully customizable colors, position, and appearance
- Responsive design with different sizes for mobile and desktop
- Animated icon for better user experience
- Configurable scroll threshold

## Usage

```tsx
import ScrollToTop from '@/components/scrolltop/ScrollToTop';

// Basic usage with default styling
const MyPage = () => {
  return (
    <div>
      {/* Your page content */}
      <ScrollToTop />
    </div>
  );
};

// Custom styling
const MyCustomPage = () => {
  return (
    <div>
      {/* Your page content */}
      <ScrollToTop 
        bgColor="bg-emerald-500/90"
        hoverBgColor="hover:bg-emerald-600"
        iconColor="text-white"
        borderColor="border border-emerald-300/30"
        positionClasses="bottom-8 right-8"
        scrollThreshold={200}
      />
    </div>
  );
};
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `bgColor` | `string` | `"bg-pangea-blue/90"` | Background color class for the button |
| `hoverBgColor` | `string` | `"hover:bg-pangea-blue"` | Hover background color class for the button |
| `iconColor` | `string` | `"text-white"` | Icon color class |
| `borderColor` | `string` | `"border border-white/20"` | Border color class |
| `shadowClass` | `string` | `"shadow-lg"` | Shadow class for the button |
| `zIndexClass` | `string` | `"z-40"` | Z-index class for the button |
| `positionClasses` | `string` | `"bottom-4 sm:bottom-6 right-4 sm:right-6"` | Position classes (bottom, right) |
| `scrollThreshold` | `number` | `300` | Scroll threshold to show the button (in pixels) |
| `icon` | `React.ReactNode` | `<ChevronUp />` | Custom icon (defaults to ChevronUp) |
| `className` | `string` | `undefined` | Additional class names |

## Examples

See `ScrollToTopExample.tsx` for more usage examples.

## Integration with Other Projects

To use this component in other projects:

1. Copy the `ScrollToTop.tsx` file to your project
2. Make sure you have the required dependencies:
   - Tailwind CSS
   - `cn` utility function (from `@/lib/utils` or similar)
   - Lucide React (or another icon library)
3. Import and use the component as shown in the usage examples

## Customization Tips

- Use Tailwind color classes that match your project's theme
- Adjust the `positionClasses` to place the button where it fits best in your layout
- Change the `scrollThreshold` based on your page content (lower for shorter pages)
- Provide a custom `icon` to match your design system
