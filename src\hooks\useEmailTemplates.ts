import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface SendEmailOptions {
  templateId: string;
  toEmail: string;
  variables: Record<string, string>;
}

export const useEmailTemplates = () => {
  const sendEmail = async ({ templateId, toEmail, variables }: SendEmailOptions) => {
    try {
      const { data, error } = await supabase.functions.invoke('send-email', {
        body: {
          template_id: templateId,
          to_email: toEmail,
          variables,
        },
      });

      if (error) throw error;

      if (!data.success) {
        throw new Error(data.error || 'Failed to send email');
      }

      toast.success('Email inviata con successo');
      return { success: true, emailId: data.email_id };
    } catch (error: any) {
      console.error('Error sending email:', error);
      toast.error(`Errore nell'invio dell'email: ${error.message}`);
      return { success: false, error: error.message };
    }
  };

  const getTemplates = async () => {
    try {
      const { data, error } = await supabase
        .from('email_templates')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast.error('Errore nel caricamento dei template');
      return [];
    }
  };

  const getTemplate = async (id: string) => {
    try {
      const { data, error } = await supabase
        .from('email_templates')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching template:', error);
      return null;
    }
  };

  const sendWelcomeEmail = async (toEmail: string, variables: Record<string, string>) => {
    try {
      // Get welcome template
      const { data: template } = await supabase
        .from('email_templates')
        .select('id')
        .eq('template_type', 'welcome')
        .eq('is_active', true)
        .limit(1)
        .single();

      if (!template) {
        throw new Error('Welcome template not found');
      }

      return await sendEmail({
        templateId: template.id,
        toEmail,
        variables,
      });
    } catch (error: any) {
      console.error('Error sending welcome email:', error);
      toast.error(`Errore nell'invio dell'email di benvenuto: ${error.message}`);
      return { success: false, error: error.message };
    }
  };

  const sendUpdatesEmail = async (toEmail: string, variables: Record<string, string>) => {
    try {
      // Get updates template
      const { data: template } = await supabase
        .from('email_templates')
        .select('id')
        .eq('template_type', 'updates')
        .eq('is_active', true)
        .limit(1)
        .single();

      if (!template) {
        throw new Error('Updates template not found');
      }

      return await sendEmail({
        templateId: template.id,
        toEmail,
        variables,
      });
    } catch (error: any) {
      console.error('Error sending updates email:', error);
      toast.error(`Errore nell'invio dell'email di aggiornamenti: ${error.message}`);
      return { success: false, error: error.message };
    }
  };

  return {
    sendEmail,
    sendWelcomeEmail,
    sendUpdatesEmail,
    getTemplates,
    getTemplate,
  };
};