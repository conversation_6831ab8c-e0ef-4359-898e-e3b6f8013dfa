# FloatingMenuBar Component

Un componente React riutilizzabile per creare una barra di menu flottante con animazioni e effetti di hover.

## Caratteristiche

- Menu flottante che si adatta allo scroll della pagina
- Supporto per icone
- Evidenziazione dell'elemento attivo
- Animazioni fluide con Framer Motion
- Completamente personalizzabile tramite props
- Supporto per la navigazione automatica o callback personalizzate

## Installazione

Assicurati di avere installato le dipendenze necessarie:

```bash
npm install framer-motion
```

## Utilizzo Base

```tsx
import FloatingMenuBar from "@/components/FloatingMenuBar";

const MyPage = () => {
  return (
    <div>
      <FloatingMenuBar
        items={[
          { id: "section1", label: "Home" },
          { id: "section2", label: "About" },
          { id: "section3", label: "Services" },
          { id: "section4", label: "Contact" },
        ]}
        activeItemId="section1"
        topOffset={80}
      />
      
      <section id="section1">
        <h1>Home Section</h1>
      </section>
      
      {/* Altre sezioni... */}
    </div>
  );
};
```

## Utilizzo con Icone

```tsx
import FloatingMenuBar from "@/components/FloatingMenuBar";
import { Home, Info, Settings, Mail } from "lucide-react";

const MyPage = () => {
  return (
    <FloatingMenuBar
      items={[
        { id: "home", label: "Home", icon: <Home className="w-4 h-4" /> },
        { id: "about", label: "About", icon: <Info className="w-4 h-4" /> },
        { id: "services", label: "Services", icon: <Settings className="w-4 h-4" /> },
        { id: "contact", label: "Contact", icon: <Mail className="w-4 h-4" /> },
      ]}
      activeItemId="home"
    />
  );
};
```

## Utilizzo con Callback Personalizzata

```tsx
import FloatingMenuBar from "@/components/FloatingMenuBar";

const MyPage = () => {
  const handleMenuItemClick = (itemId: string) => {
    console.log(`Clicked on ${itemId}`);
    // Logica personalizzata qui
  };

  return (
    <FloatingMenuBar
      items={[
        { id: "item1", label: "Item 1" },
        { id: "item2", label: "Item 2" },
      ]}
      onItemClick={handleMenuItemClick}
    />
  );
};
```

## Personalizzazione dei Colori

```tsx
import FloatingMenuBar from "@/components/FloatingMenuBar";

const MyPage = () => {
  return (
    <FloatingMenuBar
      items={[
        { id: "section1", label: "Home" },
        { id: "section2", label: "About" },
      ]}
      primaryColor="bg-blue-100"
      primaryDarkColor="text-blue-800"
      primaryLightColor="border-blue-100"
      textColor="text-gray-700"
      hoverTextColor="hover:bg-gray-200"
    />
  );
};
```

## Props

| Prop | Tipo | Default | Descrizione |
|------|------|---------|-------------|
| `items` | `MenuItem[]` | Richiesto | Array di elementi del menu |
| `activeItemId` | `string` | `undefined` | ID dell'elemento attualmente attivo |
| `topOffset` | `number` | `64` | Offset superiore per lo scroll automatico |
| `onItemClick` | `(itemId: string) => void` | `undefined` | Callback per il click su un elemento |
| `primaryColor` | `string` | `bg-primary-100` | Classe per il colore di sfondo dell'elemento attivo |
| `primaryLightColor` | `string` | `border-primary-100` | Classe per il colore del bordo in hover |
| `primaryDarkColor` | `string` | `text-primary-800` | Classe per il colore del testo dell'elemento attivo |
| `textColor` | `string` | `text-gray-600` | Classe per il colore del testo normale |
| `hoverTextColor` | `string` | `hover:bg-gray-100` | Classe per il colore di sfondo in hover |
| `className` | `string` | `""` | Classi CSS aggiuntive per il contenitore principale |

## Interfacce

```tsx
interface MenuItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
}

interface FloatingMenuBarProps {
  items: MenuItem[];
  activeItemId?: string;
  topOffset?: number;
  onItemClick?: (itemId: string) => void;
  primaryColor?: string;
  primaryLightColor?: string;
  primaryDarkColor?: string;
  textColor?: string;
  hoverTextColor?: string;
  className?: string;
}
```

## Note

- Il componente utilizza `framer-motion` per le animazioni
- Per il corretto funzionamento dello scroll automatico, assicurati che gli elementi abbiano ID corrispondenti a quelli specificati negli `items`
- Per nascondere la scrollbar, importa uno stile CSS come `hide-scrollbar.css` con il seguente contenuto:

```css
/* hide-scrollbar.css */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
```
