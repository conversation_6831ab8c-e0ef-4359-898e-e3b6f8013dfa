# Fake Data Generation System

Un sistema completo per la generazione di dati sintetici realistici per applicazioni business italiane.

## Panoramica

Questo sistema genera dati finti altamente realistici che simulano:

- **Attività commerciali** con caratteristiche geografiche e demografiche accurate
- **Offerte e promozioni** con pattern stagionali realistici  
- **Utenti** con comportamenti di acquisto e preferenze credibili
- **Prenotazioni** che seguono logiche temporali e geografiche naturali

## Caratteristiche Principali

### 🏢 Generazione Business Realistica
- **Clustering geografico**: Le attività si concentrano in aree commerciali realistiche
- **Ciclo di vita aziendale**: Simula attività nuove, consolidate e mature
- **Livelli di successo**: Varia prezzi, foto e descrizioni in base al successo simulato
- **Orari specifici per categoria**: Bar, ristoranti, spa seguono orari realistici

### 🎯 Pattern Stagionali Intelligenti
- **Variazioni mensili**: <PERSON>lestre hanno picchi a gennaio, spa d'inverno
- **Festività italiane**: Considera Ferragosto, Natale e altri periodi speciali
- **Prezzi dinamici**: Saldi estivi per abbigliamento, tariffe turistiche

### 👥 Comportamenti Cliente Realistici
- **Demografie per età**: Giovani preferiscono bar, adulti spa e ristoranti
- **Preferenze geografiche**: Alcuni utenti preferiscono attività locali
- **Pattern di prenotazione**: Tempi di anticipo realistici per età

### 📊 Qualità dei Dati
- **Validazione automatica**: Controlli di coerenza su email, telefoni, indirizzi
- **Errori realistici**: Introduce piccole inconsistenze come nei dati reali
- **Relazioni logiche**: Clienti fedeli, attività di successo con più offerte

## Struttura del Progetto

```
src/features/fakedata/
├── data/                    # Dati di base
│   ├── cities.ts           # Città italiane con coordinate
│   ├── businesses.ts       # Nomi e prodotti per categoria
│   ├── customers.ts        # Demografia e comportamenti clienti
│   ├── seasonal.ts         # Pattern stagionali
│   └── daysNames.ts        # Giorni e orari
├── generators/             # Generatori specifici
│   ├── businessGenerator.ts
│   ├── dealGenerator.ts
│   ├── userGenerator.ts
│   └── bookingGenerator.ts
├── utils/                  # Utilità
│   ├── dataQuality.ts      # Controllo qualità
│   ├── clustering.ts       # Analisi geografica
│   └── validation.ts       # Validazioni
├── components/             # Componenti UI
│   ├── GenerationPanel.tsx
│   ├── ProgressIndicator.tsx
│   ├── ResultsSummary.tsx
│   ├── BusinessSelectionList.tsx
│   └── DateRangeSelector.tsx
├── types/                  # Definizioni TypeScript
├── FakeDataManager.ts      # Classe manager principale
└── index.ts               # Esportazioni
```

## Utilizzo Base

```typescript
import { fakeDataManager } from '@/features/fakedata';

// Genera 10 business a Milano per categoria bar
const result = await fakeDataManager.generateBusinesses(
  categoryId,
  'Milano',
  10,
  {
    useSeasonality: true,
    useBusinessLifecycle: true,
    useRealisticClustering: true,
    useCustomerBehavior: true
  }
);

console.log(`Creati ${result.created} business`);
if (result.errors.length > 0) {
  console.error('Errori:', result.errors);
}
```

## Componenti UI

### GenerationPanel
```tsx
<GenerationPanel
  title="Genera Business"
  count={count}
  onCountChange={setCount}
  onGenerate={handleGenerate}
  isLoading={isLoading}
  showAdvancedOptions={true}
>
  {/* Contenuto personalizzato */}
</GenerationPanel>
```

### ProgressIndicator
```tsx
<ProgressIndicator
  current={current}
  total={total}
  message="Generazione in corso..."
  title="Progresso"
/>
```

### ResultsSummary
```tsx
<ResultsSummary
  result={generationResult}
  title="Risultati"
  showDetails={true}
/>
```

## Generatori Specifici

### BusinessGenerator
Genera attività commerciali con:
- Nomi realistici per categoria (Bar Sport, Trattoria da Mario)
- Indirizzi nelle vie principali delle città
- Coordinate GPS accurate
- Orari di apertura specifici per tipo
- Livelli di successo che influenzano prezzi e qualità

### DealGenerator  
Crea offerte con:
- Titoli accattivanti basati su prodotti reali
- Prezzi stagionali (saldi estivi, promozioni invernali)
- Sconti realistici basati sul successo dell'attività
- Time slot specifici per categoria
- Termini e condizioni appropriati

### UserGenerator
Genera utenti con:
- Nomi italiani appropriati per età
- Email con provider comuni per fascia d'età
- Telefoni mobili/fissi realistici
- Comportamenti di acquisto coerenti
- Preferenze geografiche

### BookingGenerator
Crea prenotazioni che seguono:
- Pattern temporali realistici (anticipo per età)
- Preferenze geografiche (locale vs citywide)
- Orari appropriati (giovani serali, anziani diurni)
- Fedeltà ai business preferiti

## Utilità Avanzate

### DataQualityUtils
- Introduce errori realistici (spazi extra, maiuscole)
- Valida coerenza dati (email, telefoni, indirizzi)
- Calcola statistiche sui dati generati
- Simula invecchiamento dati nel tempo

### BusinessClusteringUtils
- Genera cluster commerciali realistici
- Analizza densità geografica
- Suggerisce posizioni ottimali per nuovi business
- Identifica aree sovrasature o sottosviluppate

### DataValidationUtils
- Valida opzioni di generazione
- Controlla range di date
- Verifica conteggi e limiti
- Consolida risultati di validazione

## Configurazioni Avanzate

### Opzioni di Generazione
```typescript
interface GenerationOptions {
  useSeasonality: boolean;         // Pattern stagionali
  useBusinessLifecycle: boolean;   // Cicli vita aziendali
  useRealisticClustering: boolean; // Clustering geografico
  useCustomerBehavior: boolean;    // Comportamenti clienti
}
```

### Pattern Stagionali per Categorie
- **Bar**: Picco estate (aperitivi), calo inverno
- **Ristoranti**: Promozioni gennaio-febbraio, pieno regime estate
- **Palestre**: Boom gennaio (buoni propositi), calo estate
- **Spa**: Picco inverno (relax), prep-estate primavera
- **Abbigliamento**: Saldi gennaio e luglio/agosto

### Demografia Clienti
- **Giovani (18-35)**: Bar, ristoranti, palestre, moda
- **Adulti (36-55)**: Ristoranti, spa, bellezza, alimentari  
- **Senior (55+)**: Alimentari, spa, bellezza, preferenze locali

## Metriche e Analisi

### Analisi Qualità Dati
```typescript
const analysis = await fakeDataManager.analyzeDataQuality();
console.log(`Salute dati: ${analysis.overallHealth}`);
console.log('Raccomandazioni:', analysis.recommendations);
```

### Analisi Clustering Business
```typescript
const clustering = await fakeDataManager.analyzeBusinessClustering('Milano');
console.log(`Trovati ${clustering.clusters.length} cluster commerciali`);
console.log(`${clustering.isolatedBusinesses} business isolati`);
```

## Best Practices

### Per Business Realistici
1. Usa clustering geografico per concentrare attività
2. Varia livelli di successo per diversificare qualità
3. Applica orari specifici per categoria
4. Considera caratteristiche urbane per prezzi

### Per Offerte Convincenti  
1. Attiva pattern stagionali per realismo
2. Bilancia sconti con successo business
3. Usa time slot appropriati per categoria
4. Includi termini e condizioni realistici

### Per Comportamenti Clienti
1. Differenzia per fasce d'età
2. Applica preferenze geografiche
3. Simula fedeltà a business preferiti
4. Usa anticipi prenotazione realistici

### Per Prenotazioni Naturali
1. Rispetta orari apertura business
2. Concentra su weekend per intrattenimento
3. Distribuisci per preferenze demografiche
4. Mantieni coerenza con offerte disponibili

## Migrazione e Portabilità

Il sistema è progettato per essere facilmente trasferibile:

1. **Struttura modulare**: Ogni componente è indipendente
2. **Configurazione centralizzata**: Dati italiani facilmente sostituibili
3. **Interfacce TypeScript**: API chiare e documentate
4. **Separazione UI/Logic**: Generatori indipendenti dai componenti

### Per Usare in Altro Progetto
1. Copia la cartella `src/features/fakedata/`
2. Adatta i tipi database in `types/index.ts`
3. Modifica dati specifici paese in `data/`
4. Aggiorna componenti UI se necessario

## Limitazioni Attuali

- **Limiti di rate**: Generazione utenti limitata da Supabase Auth
- **Memoria**: Generazioni massive potrebbero richiedere batching
- **Localizzazione**: Ottimizzato per Italia, richiede adattamento per altri paesi
- **Relazioni complesse**: Reti sociali e partnership business semplificate

## Roadmap Future

- [ ] **Reti sociali**: Simulare gruppi amici e famiglie
- [ ] **Partnerships business**: Collaborazioni tra attività
- [ ] **Eventi e festivali**: Impatti su business e prenotazioni  
- [ ] **Recensioni e rating**: Sistema reputazione realistica
- [ ] **Dati real-time**: Aggiornamenti dinamici basati su attività
- [ ] **Machine Learning**: Pattern più sofisticati da dati reali
- [ ] **Multi-paese**: Supporto per altri mercati europei
- [ ] **Performance**: Ottimizzazioni per generazioni massive

## Contribuire

Il sistema è progettato per essere estensibile:

1. **Nuove categorie**: Aggiungi in `data/businesses.ts`
2. **Pattern stagionali**: Estendi `data/seasonal.ts`
3. **Comportamenti clienti**: Modifica `data/customers.ts`
4. **Utilità**: Aggiungi in `utils/`
5. **Componenti UI**: Estendi `components/`

Ogni contributo dovrebbe mantenere il focus sulla **qualità** e **realismo** dei dati generati.