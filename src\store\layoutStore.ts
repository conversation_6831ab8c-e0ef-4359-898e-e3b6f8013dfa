
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { LayoutElement } from '@/types/types';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface LayoutState {
  businessCategory: string;
  layoutElements: LayoutElement[];
  activeRoomId: string | null;
  isLoading: boolean;
  
  // Actions
  setBusinessCategory: (category: string) => void;
  setLayoutElements: (elements: LayoutElement[]) => void;
  setActiveRoomId: (id: string | null) => void;
  updateLayout: (elements: LayoutElement[], activeRoomId: string | null) => void;
  resetLayout: () => void;
  loadLayout: (businessId: string) => Promise<void>;
  saveLayout: (businessId: string) => Promise<void>;
}

export const useLayoutStore = create<LayoutState>()(
  persist(
    (set, get) => ({
      businessCategory: '',
      layoutElements: [],
      activeRoomId: null,
      isLoading: false,
      
      setBusinessCategory: (category) => set({ businessCategory: category }),
      
      setLayoutElements: (elements) => set((state) => {
        // Solo se gli elementi sono effettivamente cambiati
        if (JSON.stringify(state.layoutElements) !== JSON.stringify(elements)) {
          return { layoutElements: elements };
        }
        return state; // Ritorna lo stato attuale invece di un oggetto vuoto
      }),
      
      setActiveRoomId: (id) => set((state) => {
        // Solo se l'ID è effettivamente cambiato
        if (state.activeRoomId !== id) {
          return { activeRoomId: id };
        }
        return state; // Ritorna lo stato attuale invece di un oggetto vuoto
      }),
      
      updateLayout: (elements, activeRoomId) => 
        set((state) => {
          // Verifica se qualcosa è cambiato prima di aggiornare
          const elementsChanged = JSON.stringify(state.layoutElements) !== JSON.stringify(elements);
          const roomIdChanged = state.activeRoomId !== activeRoomId;
          
          if (!elementsChanged && !roomIdChanged) {
            return state; // Ritorna lo stato attuale invece di un oggetto vuoto
          }
          
          const updates: Partial<LayoutState> = {};
          
          if (elementsChanged) {
            updates.layoutElements = elements;
          }
          
          if (roomIdChanged) {
            updates.activeRoomId = activeRoomId;
          }
          
          return updates;
        }),
      
      resetLayout: () => 
        set({ 
          layoutElements: [], 
          activeRoomId: null 
        }),

      loadLayout: async (businessId: string) => {
        set({ isLoading: true });
        try {
          const { data, error } = await supabase
            .from('businesses')
            .select('layout')
            .eq('id', businessId)
            .single();

          if (error) {
            console.error('Error loading layout:', error);
            toast.error('Errore nel caricamento del layout');
            return;
          }

          const layoutData = data?.layout as any;
          if (layoutData && Object.keys(layoutData).length > 0) {
            set({
              businessCategory: layoutData.businessCategory || '',
              layoutElements: layoutData.elements || [],
              activeRoomId: layoutData.activeRoomId || null,
            });
          } else {
            // Reset layout if no data found
            set({
              businessCategory: '',
              layoutElements: [],
              activeRoomId: null,
            });
          }
        } catch (error) {
          console.error('Error loading layout:', error);
          toast.error('Errore nel caricamento del layout');
        } finally {
          set({ isLoading: false });
        }
      },

      saveLayout: async (businessId: string) => {
        const { businessCategory, layoutElements, activeRoomId } = get();
        set({ isLoading: true });
        
        try {
          const layoutData = {
            businessCategory,
            elements: layoutElements,
            activeRoomId,
            timestamp: new Date().toISOString(),
          };

          const { error } = await supabase
            .from('businesses')
            .update({ layout: layoutData as any })
            .eq('id', businessId);

          if (error) {
            console.error('Error saving layout:', error);
            toast.error('Errore nel salvataggio del layout');
            return;
          }

          toast.success('Layout salvato con successo!');
        } catch (error) {
          console.error('Error saving layout:', error);
          toast.error('Errore nel salvataggio del layout');
        } finally {
          set({ isLoading: false });
        }
      },
    }),
    {
      name: 'layout-storage',
      // Solo ciò che vogliamo persistere nel localStorage
      partialize: (state) => ({
        businessCategory: state.businessCategory,
        layoutElements: state.layoutElements,
        activeRoomId: state.activeRoomId,
      }),
    }
  )
);
