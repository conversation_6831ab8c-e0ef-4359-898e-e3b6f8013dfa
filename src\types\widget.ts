import { LucideIcon } from "lucide-react";

/**
 * Tipi di widget disponibili nella dashboard
 */
export enum WidgetType {
  BOOKINGS = "bookings",
  DEALS = "deals",
  CLIENTS = "clients",
  CHART = "chart",
  CALENDAR = "calendar",
  CUSTOM = "custom",
  TIME_SLOT_USAGE = "time_slot_usage",
  TOTAL_REVENUE = "total_revenue",
  ESTIMATED_PROFIT = "estimated_profit",
  AVG_BOOKING_PRICE = "avg_booking_price",
  REVENUE_PER_CLIENT = "revenue_per_client",
  NEW_CUSTOMERS = "new_customers",
  RECENT_BOOKINGS = "recent_bookings"
}

/**
 * Dimensioni disponibili per i widget
 */
export enum WidgetSize {
  SMALL = "small",   // 1 colonna
  MEDIUM = "medium", // 2 colonne
  LARGE = "large",   // 3 colonne
  FULL = "full"      // 6 colonne (larghezza completa)
}

/**
 * Interfaccia base per tutti i widget
 */
export interface WidgetBase {
  id: string;
  type: WidgetType;
  title: string;
  description?: string;
  size: WidgetSize;
  position: number;
  icon?: string;
  isLoading?: boolean;
  isVisible?: boolean;
  refreshInterval?: number; // in millisecondi, se il widget deve aggiornarsi automaticamente
}

/**
 * Widget per le prenotazioni
 */
export interface BookingsWidget extends WidgetBase {
  type: WidgetType.BOOKINGS;
  data: {
    count: number;
  };
}

/**
 * Widget per le offerte
 */
export interface DealsWidget extends WidgetBase {
  type: WidgetType.DEALS;
  data: {
    dealsCount: number;
    activeDealsCount: number;
    expiredDealsCount: number;
  };
}

/**
 * Widget per i clienti
 */
export interface ClientsWidget extends WidgetBase {
  type: WidgetType.CLIENTS;
  data: {
    count: number;
  };
}

/**
 * Widget per grafici
 */
export interface ChartWidget extends WidgetBase {
  type: WidgetType.CHART;
  chartType: "bar" | "line" | "pie" | "doughnut";
  data: any; // Dati specifici per il tipo di grafico
  options?: any; // Opzioni di configurazione del grafico
}

/**
 * Widget per calendario
 */
export interface CalendarWidget extends WidgetBase {
  type: WidgetType.CALENDAR;
  data: {
    events: Array<{
      id: string;
      title: string;
      start: string;
      end: string;
    }>;
  };
}

/**
 * Widget personalizzato
 */
export interface CustomWidget extends WidgetBase {
  type: WidgetType.CUSTOM;
  component: string; // Nome del componente da renderizzare
  data: any; // Dati personalizzati
}

/**
 * Widget per l'utilizzo delle fasce orarie
 */
export interface TimeSlotUsageWidget extends WidgetBase {
  type: WidgetType.TIME_SLOT_USAGE;
  data: {
    timeSlotData: Array<{
      timeSlot: string;
      count: number;
      dayOfWeek?: number;
    }>;
    filters: {
      dateRange: 'last7days' | 'last30days' | 'last90days' | 'all';
      sortBy: 'popularity' | 'time';
      selectedDays: number[]; // 0-6 for Sunday-Saturday
    };
  };
}

/**
 * Widget per il ricavo totale
 */
export interface TotalRevenueWidget extends WidgetBase {
  type: WidgetType.TOTAL_REVENUE;
  data: {
    amount: number;
    period: string;
    percentChange?: number; // Variazione percentuale rispetto al periodo precedente
  };
}

/**
 * Widget per il profitto stimato
 */
export interface EstimatedProfitWidget extends WidgetBase {
  type: WidgetType.ESTIMATED_PROFIT;
  data: {
    amount: number;
    margin: number;
    period: string;
    percentChange?: number; // Variazione percentuale rispetto al periodo precedente
  };
}

/**
 * Widget per il prezzo medio di prenotazione
 */
export interface AvgBookingPriceWidget extends WidgetBase {
  type: WidgetType.AVG_BOOKING_PRICE;
  data: {
    amount: number;
    period: string;
    percentChange?: number; // Variazione percentuale rispetto al periodo precedente
  };
}

/**
 * Widget per il ricavo per cliente
 */
export interface RevenuePerClientWidget extends WidgetBase {
  type: WidgetType.REVENUE_PER_CLIENT;
  data: {
    amount: number;
    period: string;
    percentChange?: number; // Variazione percentuale rispetto al periodo precedente
  };
}

/**
 * Widget per i nuovi clienti
 */
export interface NewCustomersWidget extends WidgetBase {
  type: WidgetType.NEW_CUSTOMERS;
  data: {
    count: number;
    period: string;
    percentChange?: number; // Variazione percentuale rispetto al periodo precedente
  };
}

/**
 * Widget per le prenotazioni recenti
 */
export interface RecentBookingsWidget extends WidgetBase {
  type: WidgetType.RECENT_BOOKINGS;
  data: {
    count: number;
    period: string;
    percentChange?: number; // Variazione percentuale rispetto al periodo precedente
  };
}

/**
 * Tipo unione per tutti i tipi di widget
 */
export type Widget =
  | BookingsWidget
  | DealsWidget
  | ClientsWidget
  | ChartWidget
  | CalendarWidget
  | CustomWidget
  | TimeSlotUsageWidget
  | TotalRevenueWidget
  | EstimatedProfitWidget
  | AvgBookingPriceWidget
  | RevenuePerClientWidget
  | NewCustomersWidget
  | RecentBookingsWidget;

/**
 * Configurazione della dashboard
 */
export interface DashboardConfig {
  widgets: Widget[];
  layout: string; // Layout della dashboard (es. "grid", "masonry", ecc.)
  lastUpdated: string; // Data dell'ultimo aggiornamento
}
