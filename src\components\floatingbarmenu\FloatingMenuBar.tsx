import { useState, useEffect } from "react";
import { motion } from "framer-motion";

export interface MenuItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
}

export interface FloatingMenuBarProps {
  items: MenuItem[];
  activeItemId?: string;
  topOffset?: number;
  onItemClick?: (itemId: string) => void;
  primaryColor?: string;
  primaryLightColor?: string;
  primaryDarkColor?: string;
  textColor?: string;
  hoverTextColor?: string;
  className?: string;
}

const FloatingMenuBar = ({
  items,
  activeItemId,
  topOffset = 64, // Default top offset (16rem)
  onItemClick,
  primaryColor = "bg-primary-100",
  primaryLightColor = "border-primary-100",
  primaryDarkColor = "text-primary-800",
  textColor = "text-gray-600",
  hoverTextColor = "hover:bg-gray-100",
  className = "",
}: FloatingMenuBarProps) => {
  const [scrolled, setScrolled] = useState(false);
  const [activeItem, setActiveItem] = useState<string | undefined>(activeItemId);

  // Gestisce lo scroll e aggiorna lo stato
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 100);
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll(); // Chiamata iniziale

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // Aggiorna activeItem quando cambia activeItemId
  useEffect(() => {
    if (activeItemId !== undefined) {
      setActiveItem(activeItemId);
    }
  }, [activeItemId]);

  // Gestisce il click su un elemento del menu
  const handleItemClick = (itemId: string) => {
    setActiveItem(itemId);
    
    if (onItemClick) {
      onItemClick(itemId);
    } else {
      // Comportamento di default: scroll verso l'elemento con l'ID corrispondente
      const element = document.getElementById(itemId);
      if (element) {
        window.scrollTo({
          top: element.offsetTop - topOffset,
          behavior: "smooth",
        });
      }
    }
  };

  return (
    <div className={`fixed top-16 left-0 right-0 z-40 flex justify-center px-4 py-2 ${className}`}>
      <motion.div
        initial={{ y: -20, opacity: 0 }}
        animate={{
          y: 0,
          opacity: 1,
          scale: scrolled ? 0.95 : 1,
          paddingTop: scrolled ? "0.25rem" : "0.25rem",
          paddingBottom: scrolled ? "0.25rem" : "0.25rem",
        }}
        transition={{ duration: 0.3 }}
        className={`bg-white/90 backdrop-blur-sm rounded-full shadow-md border border-gray-100 px-2 py-1 flex items-center space-x-1 overflow-x-auto max-w-full hide-scrollbar transition-all duration-300 ${
          scrolled ? "shadow-lg" : "shadow-md"
        } hover:shadow-lg hover:${primaryLightColor}`}
        whileHover={{ scale: 1.02 }}
      >
        {items.map((item) => (
          <motion.button
            key={item.id}
            onClick={() => handleItemClick(item.id)}
            className={`px-3 py-1.5 text-sm font-medium rounded-full whitespace-nowrap transition-all duration-300 flex items-center gap-2 ${
              activeItem === item.id
                ? `${primaryColor} ${primaryDarkColor} scale-105`
                : `${textColor} ${hoverTextColor}`
            }`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
            {item.label}
          </motion.button>
        ))}
      </motion.div>
    </div>
  );
};

export default FloatingMenuBar;
