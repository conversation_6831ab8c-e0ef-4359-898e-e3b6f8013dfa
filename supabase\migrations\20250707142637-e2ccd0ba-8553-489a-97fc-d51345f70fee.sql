
-- Create basic subscriptions for business owners who don't have active subscriptions
INSERT INTO public.business_subscriptions (
  user_id, 
  tier_type, 
  is_yearly, 
  status, 
  started_at, 
  expires_at
)
SELECT 
  b.owner_id,
  'basic'::pricing_tier_type,
  false,
  'active',
  now(),
  now() + INTERVAL '1 month'
FROM businesses b
WHERE b.owner_id IS NOT NULL
  AND NOT EXISTS (
    SELECT 1 FROM business_subscriptions bs 
    WHERE bs.user_id = b.owner_id 
    AND bs.status = 'active'
  )
GROUP BY b.owner_id;

-- Create Sofia (booking agent) for each business whose owner gets a new subscription
INSERT INTO public.ai_business_agents (
  business_id,
  agent_type,
  name,
  description,
  voice_id,
  avatar_url,
  is_active
)
SELECT DISTINCT
  b.id,
  'booking'::agent_type,
  'Sofia',
  'Risponde 24/7, consiglia orari disponibili, propone sconti nelle ore meno frequentate e conferma tramite SMS o email.',
  'CwhRBWXzGAHq8TQ4Fs17',
  'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80',
  true
FROM businesses b
WHERE b.owner_id IS NOT NULL
  AND NOT EXISTS (
    SELECT 1 FROM business_subscriptions bs 
    WHERE bs.user_id = b.owner_id 
    AND bs.status = 'active'
  )
  AND NOT EXISTS (
    SELECT 1 FROM ai_business_agents aba 
    WHERE aba.business_id = b.id
  );

-- Display summary of what was created
DO $$
DECLARE
  subscription_count INTEGER;
  agent_count INTEGER;
BEGIN
  -- Count subscriptions created (within last minute)
  SELECT COUNT(*) INTO subscription_count
  FROM business_subscriptions 
  WHERE tier_type = 'basic' 
  AND created_at >= now() - INTERVAL '1 minute';
  
  -- Count agents created (within last minute)
  SELECT COUNT(*) INTO agent_count
  FROM ai_business_agents 
  WHERE agent_type = 'booking' 
  AND created_at >= now() - INTERVAL '1 minute';
  
  RAISE NOTICE 'Setup completato:';
  RAISE NOTICE '- % sottoscrizioni Basic create', subscription_count;
  RAISE NOTICE '- % agenti Sofia (Booking) creati', agent_count;
END $$;
