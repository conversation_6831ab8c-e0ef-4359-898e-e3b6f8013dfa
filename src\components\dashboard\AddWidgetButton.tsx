import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus, LayoutGrid } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Widget, WidgetType, WidgetSize } from '@/types/widget';
import { useWidgetStore } from '@/store/widgetStore';
import { useDashboardConfig } from '@/hooks/useDashboardConfig';
import { toast } from 'sonner';

const AddWidgetButton: React.FC = () => {
  const [open, setOpen] = useState(false);
  const [widgetType, setWidgetType] = useState<WidgetType>(WidgetType.BOOKINGS);
  const [widgetTitle, setWidgetTitle] = useState('');
  const [widgetSize, setWidgetSize] = useState<WidgetSize>(WidgetSize.MEDIUM);

  const { dashboardConfig, addWidget } = useWidgetStore();
  const { loadWidgetData } = useDashboardConfig();

  const handleAddWidget = () => {
    const newWidget: Partial<Widget> = {
      id: `widget-${Date.now()}`,
      type: widgetType,
      title: widgetTitle || getDefaultTitle(widgetType),
      description: getDefaultDescription(widgetType),
      size: widgetSize,
      position: dashboardConfig.widgets.length,
      icon: getDefaultIcon(widgetType),
      isLoading: false,
      isVisible: true,
    };

    switch (widgetType) {
      case WidgetType.BOOKINGS:
        newWidget.data = { count: 0 };
        break;
      case WidgetType.DEALS:
        newWidget.data = { dealsCount: 0, activeDealsCount: 0, expiredDealsCount: 0 };
        break;
      case WidgetType.CLIENTS:
        newWidget.data = { count: 0 };
        break;
      case WidgetType.TIME_SLOT_USAGE:
        newWidget.data = {
          timeSlotData: [],
          filters: {
            dateRange: 'last30days',
            sortBy: 'popularity',
            selectedDays: [0, 1, 2, 3, 4, 5, 6]
          }
        };
        break;
      case WidgetType.TOTAL_REVENUE:
        newWidget.data = { amount: 0, period: '30 giorni', percentChange: 0 };
        break;
      case WidgetType.ESTIMATED_PROFIT:
        newWidget.data = { amount: 0, margin: 0.3, period: '30 giorni', percentChange: 0 };
        break;
      case WidgetType.AVG_BOOKING_PRICE:
        newWidget.data = { amount: 0, period: '30 giorni', percentChange: 0 };
        break;
      case WidgetType.REVENUE_PER_CLIENT:
        newWidget.data = { amount: 0, period: '30 giorni', percentChange: 0 };
        break;
      case WidgetType.NEW_CUSTOMERS:
        newWidget.data = { count: 0, period: '30 giorni', percentChange: 0 };
        break;
      case WidgetType.RECENT_BOOKINGS:
        newWidget.data = { count: 0, period: '30 giorni', percentChange: 0 };
        break;
    }

    const widget = newWidget as Widget;
    addWidget(widget);

    setTimeout(() => {
      loadWidgetData(widget);
      toast.success(`Widget "${widget.title}" aggiunto con successo`);
    }, 100);

    setOpen(false);

    setWidgetType(WidgetType.BOOKINGS);
    setWidgetTitle('');
    setWidgetSize(WidgetSize.MEDIUM);
  };

  const getDefaultTitle = (type: WidgetType): string => {
    switch (type) {
      case WidgetType.BOOKINGS:
        return 'Prenotazioni';
      case WidgetType.DEALS:
        return 'Offerte';
      case WidgetType.CLIENTS:
        return 'Clienti';
      case WidgetType.CHART:
        return 'Grafico';
      case WidgetType.CALENDAR:
        return 'Calendario';
      case WidgetType.CUSTOM:
        return 'Widget Personalizzato';
      case WidgetType.TIME_SLOT_USAGE:
        return 'Fasce Orarie Popolari';
      case WidgetType.TOTAL_REVENUE:
        return 'Ricavi Totali';
      case WidgetType.ESTIMATED_PROFIT:
        return 'Profitto Stimato';
      case WidgetType.AVG_BOOKING_PRICE:
        return 'Prezzo Medio Prenotazione';
      case WidgetType.REVENUE_PER_CLIENT:
        return 'Ricavo per Cliente';
      case WidgetType.NEW_CUSTOMERS:
        return 'Nuovi Clienti';
      case WidgetType.RECENT_BOOKINGS:
        return 'Prenotazioni Recenti';
      default:
        return 'Nuovo Widget';
    }
  };

  const getDefaultDescription = (type: WidgetType): string => {
    switch (type) {
      case WidgetType.BOOKINGS:
        return 'Le prenotazioni dei tuoi clienti';
      case WidgetType.DEALS:
        return 'Statistiche delle tue offerte';
      case WidgetType.CLIENTS:
        return 'Persone che hanno prenotato con te';
      case WidgetType.CHART:
        return 'Visualizzazione dati';
      case WidgetType.CALENDAR:
        return 'Eventi in programma';
      case WidgetType.CUSTOM:
        return 'Widget personalizzato';
      case WidgetType.TIME_SLOT_USAGE:
        return 'Analisi delle fasce orarie più prenotate';
      case WidgetType.TOTAL_REVENUE:
        return 'Ricavi totali degli ultimi 30 giorni';
      case WidgetType.ESTIMATED_PROFIT:
        return 'Profitto stimato degli ultimi 30 giorni';
      case WidgetType.AVG_BOOKING_PRICE:
        return 'Prezzo medio delle prenotazioni';
      case WidgetType.REVENUE_PER_CLIENT:
        return 'Ricavo medio per cliente';
      case WidgetType.NEW_CUSTOMERS:
        return 'Nuovi clienti degli ultimi 30 giorni';
      case WidgetType.RECENT_BOOKINGS:
        return 'Prenotazioni degli ultimi 30 giorni';
      default:
        return '';
    }
  };

  const getDefaultIcon = (type: WidgetType): string => {
    switch (type) {
      case WidgetType.BOOKINGS:
        return 'Calendar';
      case WidgetType.DEALS:
        return 'Package';
      case WidgetType.CLIENTS:
        return 'Users';
      case WidgetType.CHART:
        return 'BarChart4';
      case WidgetType.CALENDAR:
        return 'Calendar';
      case WidgetType.CUSTOM:
        return 'Package';
      case WidgetType.TIME_SLOT_USAGE:
        return 'Clock';
      case WidgetType.TOTAL_REVENUE:
        return 'DollarSign';
      case WidgetType.ESTIMATED_PROFIT:
        return 'TrendingUp';
      case WidgetType.AVG_BOOKING_PRICE:
        return 'Tag';
      case WidgetType.REVENUE_PER_CLIENT:
        return 'Users';
      case WidgetType.NEW_CUSTOMERS:
        return 'Users';
      case WidgetType.RECENT_BOOKINGS:
        return 'Calendar';
      default:
        return 'Package';
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="bg-primary-600 hover:bg-primary-700 text-white flex items-center gap-2">
          <LayoutGrid className="h-4 w-4" />
          <span>Aggiungi Widget</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Aggiungi nuovo widget</DialogTitle>
          <DialogDescription>
            Configura un nuovo widget da aggiungere alla tua dashboard.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="widget-type" className="text-right">
              Tipo
            </Label>
            <Select
              value={widgetType}
              onValueChange={(value) => setWidgetType(value as WidgetType)}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Seleziona tipo di widget" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={WidgetType.BOOKINGS}>Prenotazioni</SelectItem>
                <SelectItem value={WidgetType.DEALS}>Offerte</SelectItem>
                <SelectItem value={WidgetType.CLIENTS}>Clienti</SelectItem>
                <SelectItem value={WidgetType.CHART}>Grafico</SelectItem>
                <SelectItem value={WidgetType.CALENDAR}>Calendario</SelectItem>
                <SelectItem value={WidgetType.TIME_SLOT_USAGE}>Fasce Orarie Popolari</SelectItem>
                <SelectItem value={WidgetType.TOTAL_REVENUE}>Ricavi Totali</SelectItem>
                <SelectItem value={WidgetType.ESTIMATED_PROFIT}>Profitto Stimato</SelectItem>
                <SelectItem value={WidgetType.AVG_BOOKING_PRICE}>Prezzo Medio Prenotazione</SelectItem>
                <SelectItem value={WidgetType.REVENUE_PER_CLIENT}>Ricavo per Cliente</SelectItem>
                <SelectItem value={WidgetType.NEW_CUSTOMERS}>Nuovi Clienti</SelectItem>
                <SelectItem value={WidgetType.RECENT_BOOKINGS}>Prenotazioni Recenti</SelectItem>
                <SelectItem value={WidgetType.CUSTOM}>Personalizzato</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="widget-title" className="text-right">
              Titolo
            </Label>
            <Input
              id="widget-title"
              value={widgetTitle}
              onChange={(e) => setWidgetTitle(e.target.value)}
              placeholder={getDefaultTitle(widgetType)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="widget-size" className="text-right">
              Dimensione
            </Label>
            <Select
              value={widgetSize}
              onValueChange={(value) => setWidgetSize(value as WidgetSize)}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Seleziona dimensione" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={WidgetSize.SMALL}>Piccolo (1 colonna)</SelectItem>
                <SelectItem value={WidgetSize.MEDIUM}>Medio (2 colonne)</SelectItem>
                <SelectItem value={WidgetSize.LARGE}>Grande (3 colonne)</SelectItem>
                <SelectItem value={WidgetSize.FULL}>Larghezza completa (6 colonne)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button
            type="submit"
            onClick={handleAddWidget}
            className="bg-primary-600 hover:bg-primary-700 text-white"
          >
            <Plus className="mr-2 h-4 w-4" /> Aggiungi Widget
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AddWidgetButton;
