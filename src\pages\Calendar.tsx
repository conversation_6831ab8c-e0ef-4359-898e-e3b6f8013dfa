import { useState, useEffect } from 'react';
import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, eachDayOfInterval, isSameMonth, isSameDay, addDays, addMonths, subMonths } from 'date-fns';
import { it } from 'date-fns/locale';
import { supabase } from '@/integrations/supabase/client';
import { useQuery } from '@tanstack/react-query';
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Clock } from 'lucide-react';
import MainLayout from '@/layouts/MainLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useBusinessStore } from '@/store/businessStore';

interface Booking {
  id: string;
  booking_date: string;
  booking_time: string;
  deal_id: string;
  user_id: string;
  discount_percentage: number;
  discounted_price: number;
  original_price: number;
  status: string;
  created_at: string;
  updated_at: string;
  deal?: {
    title: string;
    business_name?: string;
  };
  user?: {
    first_name: string | null;
    last_name: string | null;
    email: string | null;
  };
}

interface BookingsByDateAndTime {
  [date: string]: {
    [time: string]: Booking[];
  };
}

const CalendarPage = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const selectedBusiness = useBusinessStore(state => state.selectedBusiness);
  const [view, setView] = useState<'month' | 'day' | 'schedule'>('month');

  const fetchBookings = async () => {
    if (!selectedBusiness) return [];
    
    try {
      const monthStart = startOfMonth(currentDate);
      const monthEnd = endOfMonth(currentDate);

      const { data, error } = await supabase
        .from('bookings')
        .select(`
          *,
          deal:deals(title, business:businesses(name))
        `)
        .eq('deals.business_id', selectedBusiness.id)
        .gte('booking_date', format(monthStart, 'yyyy-MM-dd'))
        .lte('booking_date', format(monthEnd, 'yyyy-MM-dd'));

      if (error) {
        throw error;
      }

      // Format the data for easier use
      return data.map(booking => ({
        ...booking,
        deal: {
          title: booking.deal?.title || '',
          business_name: booking.deal?.business?.name || ''
        }
      }));
    } catch (error) {
      console.error('Error fetching bookings:', error);
      return [];
    }
  };

  const { data: bookings, isLoading, isError, error, refetch } = useQuery({
    queryKey: ['bookings', selectedBusiness?.id, format(currentDate, 'yyyy-MM')],
    queryFn: fetchBookings,
    enabled: !!selectedBusiness,
  });

  const organizeBookingsByDateAndTime = (bookings: Booking[] = []): BookingsByDateAndTime => {
    return bookings.reduce((acc: BookingsByDateAndTime, booking) => {
      if (!acc[booking.booking_date]) {
        acc[booking.booking_date] = {};
      }
      
      if (!acc[booking.booking_date][booking.booking_time]) {
        acc[booking.booking_date][booking.booking_time] = [];
      }
      
      acc[booking.booking_date][booking.booking_time].push(booking);
      
      return acc;
    }, {});
  };
  
  const organizedBookings = organizeBookingsByDateAndTime(bookings || []);

  const renderCalendarDays = () => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const startDate = startOfWeek(monthStart, { locale: it });
    const endDate = endOfWeek(monthEnd, { locale: it });
    
    const days = eachDayOfInterval({ start: startDate, end: endDate });
    
    const weekDays = [...Array(7)].map((_, i) => 
      format(addDays(startOfWeek(new Date(), { locale: it }), i), 'EEEE', { locale: it })
    );

    return (
      <div className="flex flex-col h-full">
        <div className="grid grid-cols-7 mb-2">
          {weekDays.map((day, index) => (
            <div key={index} className="text-center text-sm font-medium py-2">
              {day.charAt(0).toUpperCase() + day.slice(1, 3)}
            </div>
          ))}
        </div>
        
        <div className="grid grid-cols-7 flex-grow auto-rows-fr gap-1">
          {days.map((day) => {
            const dateString = format(day, 'yyyy-MM-dd');
            const dayBookings = organizedBookings[dateString] || {};
            const bookingCount = Object.values(dayBookings).reduce(
              (count, bookings) => count + bookings.length, 0
            );
            
            return (
              <div
                key={dateString}
                className={`
                  border rounded-md p-1 h-full min-h-[80px] relative
                  ${isSameMonth(day, currentDate) ? 'bg-white' : 'bg-gray-50 text-gray-400'}
                  ${isSameDay(day, new Date()) ? 'border-blue-500' : 'border-gray-200'}
                  ${selectedDate && isSameDay(day, selectedDate) ? 'ring-2 ring-blue-500' : ''}
                  hover:bg-gray-50 cursor-pointer
                `}
                onClick={() => {
                  setSelectedDate(day);
                  if (bookingCount > 0) {
                    setView('day');
                  }
                }}
              >
                <div className="flex flex-col h-full">
                  <div className="text-right text-sm">
                    {format(day, 'd', { locale: it })}
                  </div>
                  
                  {bookingCount > 0 && (
                    <div className="mt-1 overflow-hidden">
                      {bookingCount <= 3 ? (
                        Object.entries(dayBookings).flatMap(([time, bookings]) =>
                          bookings.map(booking => (
                            <div 
                              key={booking.id} 
                              className="text-xs bg-blue-100 text-blue-800 p-1 rounded mb-1 truncate"
                              title={`${booking.deal?.title} - ${time}`}
                            >
                              {time.substring(0, 5)} - {booking.deal?.title?.substring(0, 12)}...
                            </div>
                          ))
                        ).slice(0, 3)
                      ) : (
                        <div className="text-xs font-medium text-center mt-2">
                          <Badge variant="secondary">{bookingCount} prenotazioni</Badge>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderDayView = () => {
    if (!selectedDate) return null;
    
    const dateString = format(selectedDate, 'yyyy-MM-dd');
    const dayBookings = organizedBookings[dateString] || {};
    
    return (
      <div className="flex flex-col h-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">
            {format(selectedDate, 'EEEE d MMMM yyyy', { locale: it })}
          </h2>
          <Button variant="outline" size="sm" onClick={() => setView('month')}>
            Torna al mese
          </Button>
        </div>
        
        <ScrollArea className="flex-grow">
          {Object.keys(dayBookings).length > 0 ? (
            Object.entries(dayBookings)
              .sort(([timeA], [timeB]) => timeA.localeCompare(timeB))
              .map(([time, bookingsAtTime]) => (
                <div key={time} className="mb-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <h3 className="text-md font-medium">
                      {time.substring(0, 5)}
                    </h3>
                    <Badge>{bookingsAtTime.length} prenotazioni</Badge>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 pl-6">
                    {bookingsAtTime.map(booking => (
                      <Card key={booking.id} className="overflow-hidden">
                        <CardHeader className="p-3 pb-2">
                          <CardTitle className="text-sm truncate">
                            {booking.deal?.title}
                          </CardTitle>
                          <CardDescription className="text-xs">
                            {booking.user?.first_name} {booking.user?.last_name}
                          </CardDescription>
                        </CardHeader>
                        <Separator />
                        <CardContent className="p-3 pt-2 text-xs">
                          <div className="flex justify-between">
                            <span>Prezzo: {booking.discounted_price}€</span>
                            <Badge
                              className="text-[10px] h-5"
                              variant={booking.status === 'confirmed' ? 'default' : 
                                      booking.status === 'cancelled' ? 'destructive' : 'outline'}
                            >
                              {booking.status === 'confirmed' ? 'Confermata' :
                               booking.status === 'cancelled' ? 'Cancellata' : 'In Attesa'}
                            </Badge>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              ))
          ) : (
            <div className="flex flex-col items-center justify-center h-full py-8">
              <p className="text-gray-500">Nessuna prenotazione per questa data</p>
            </div>
          )}
        </ScrollArea>
      </div>
    );
  };

  const renderScheduleView = () => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd });
    
    const daysWithBookings = daysInMonth.filter(day => {
      const dateString = format(day, 'yyyy-MM-dd');
      return organizedBookings[dateString] && Object.keys(organizedBookings[dateString]).length > 0;
    });
    
    if (daysWithBookings.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center h-64">
          <CalendarIcon className="h-12 w-12 text-gray-300 mb-2" />
          <p className="text-gray-500">Nessuna prenotazione per questo mese</p>
        </div>
      );
    }
    
    return (
      <ScrollArea className="h-[calc(100vh-200px)]">
        <div className="space-y-8 pb-8">
          {daysWithBookings.map(day => {
            const dateString = format(day, 'yyyy-MM-dd');
            const dayBookings = organizedBookings[dateString];
            
            return (
              <div key={dateString} className="relative">
                <div className="sticky top-0 bg-white z-10 pb-2">
                  <h3 className="text-lg font-medium">
                    {format(day, 'EEEE d MMMM', { locale: it })}
                  </h3>
                  <Separator className="mt-2" />
                </div>
                
                <div className="pl-4 space-y-4 mt-3">
                  {Object.entries(dayBookings)
                    .sort(([timeA], [timeB]) => timeA.localeCompare(timeB))
                    .map(([time, bookingsAtTime]) => (
                      <div key={`${dateString}-${time}`} className="relative">
                        <div className="absolute left-[-1.75rem] top-1 h-4 w-4 rounded-full bg-blue-500"></div>
                        <div className="ml-4 mb-1 flex items-center">
                          <span className="text-sm font-medium">{time.substring(0, 5)}</span>
                          <Badge className="ml-2 text-xs" variant="outline">
                            {bookingsAtTime.length} {bookingsAtTime.length === 1 ? 'prenotazione' : 'prenotazioni'}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 ml-4">
                          {bookingsAtTime.map(booking => (
                            <Card key={booking.id} className="bg-white">
                              <CardHeader className="p-3 pb-2">
                                <div className="flex justify-between items-start">
                                  <CardTitle className="text-sm">
                                    {booking.deal?.title}
                                  </CardTitle>
                                  <Badge
                                    variant={booking.status === 'confirmed' ? 'default' : 
                                            booking.status === 'cancelled' ? 'destructive' : 'outline'}
                                    className="text-[10px] h-5"
                                  >
                                    {booking.status === 'confirmed' ? 'Confermata' :
                                     booking.status === 'cancelled' ? 'Cancellata' : 'In Attesa'}
                                  </Badge>
                                </div>
                                <CardDescription className="text-xs">
                                  {booking.user?.first_name} {booking.user?.last_name}
                                </CardDescription>
                              </CardHeader>
                              <CardContent className="p-3 pt-2 text-xs">
                                <div className="flex justify-between">
                                  <span>Prezzo: {booking.discounted_price}€</span>
                                  <span>{booking.discount_percentage}% sconto</span>
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            );
          })}
        </div>
      </ScrollArea>
    );
  };

  const renderLoading = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-32" />
        <div className="flex space-x-2">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
        </div>
      </div>
      
      <div className="grid grid-cols-7 gap-2">
        {[...Array(7)].map((_, i) => (
          <Skeleton key={`weekday-${i}`} className="h-6" />
        ))}
      </div>
      
      <div className="grid grid-cols-7 gap-1">
        {[...Array(35)].map((_, i) => (
          <Skeleton key={`day-${i}`} className="h-20" />
        ))}
      </div>
    </div>
  );

  const renderError = () => (
    <div className="flex flex-col items-center justify-center h-64">
      <p className="text-red-500 mb-4">
        Errore nel caricamento delle prenotazioni: {error instanceof Error ? error.message : 'Errore sconosciuto'}
      </p>
      <Button onClick={() => refetch()} variant="outline">
        Riprova
      </Button>
    </div>
  );

  return (
    <MainLayout>
      <div className="container mx-auto py-8">
        <div className="flex flex-col space-y-6">
          <div className="flex flex-col md:flex-row md:justify-between md:items-center space-y-4 md:space-y-0">
            <h1 className="text-3xl font-bold">Calendario Prenotazioni</h1>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setCurrentDate(subMonths(currentDate, 1))}
                >
                  <ChevronLeft className="h-4 w-4" />
                  <span className="sr-only">Mese precedente</span>
                </Button>
                
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="min-w-[150px]">
                      {format(currentDate, 'MMMM yyyy', { locale: it })}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    {/* Potremmo aggiungere un selettore di date più avanzato qui */}
                  </PopoverContent>
                </Popover>
                
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setCurrentDate(addMonths(currentDate, 1))}
                >
                  <ChevronRight className="h-4 w-4" />
                  <span className="sr-only">Mese successivo</span>
                </Button>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentDate(new Date())}
              >
                Oggi
              </Button>
            </div>
          </div>
          
          <Tabs defaultValue="month" value={view} onValueChange={(v) => setView(v as any)}>
            <TabsList className="mb-4">
              <TabsTrigger value="month">Mese</TabsTrigger>
              <TabsTrigger value="day">Giorno</TabsTrigger>
              <TabsTrigger value="schedule">Agenda</TabsTrigger>
            </TabsList>
            
            <Card>
              <CardContent className="p-4">
                {isLoading ? (
                  renderLoading()
                ) : isError ? (
                  renderError()
                ) : (
                  <TabsContent value="month" className="mt-0">
                    {renderCalendarDays()}
                  </TabsContent>
                )}
                
                <TabsContent value="day" className="mt-0">
                  {renderDayView()}
                </TabsContent>
                
                <TabsContent value="schedule" className="mt-0">
                  {renderScheduleView()}
                </TabsContent>
              </CardContent>
            </Card>
          </Tabs>
        </div>
      </div>
    </MainLayout>
  );
};

export default CalendarPage;
