
/**
 * Formatta un valore numerico come valuta (EUR)
 */
export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('it-IT', {
    style: 'currency',
    currency: 'EUR'
  }).format(value);
};

/**
 * Formatta un valore numerico come percentuale
 */
export const formatPercentage = (value: number): string => {
  return new Intl.NumberFormat('it-IT', {
    style: 'percent',
    minimumFractionDigits: 1,
    maximumFractionDigits: 1
  }).format(value);
};

/**
 * Formatta una data in formato italiano (dd/mm/yyyy)
 */
export const formatDate = (date: Date | string): string => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('it-IT');
};

/**
 * Formatta un orario in formato hh:mm
 */
export const formatTimeHHMM = (timeString: string): string => {
  if (!timeString) return '';

  // Se è già in formato hh:mm, ritorna come è
  if (/^\d{1,2}:\d{2}$/.test(timeString)) return timeString;

  try {
    // Rimuovi i secondi se presenti (formato hh:mm:ss)
    if (timeString.includes(':')) {
      const parts = timeString.split(':');
      if (parts.length >= 2) {
        return `${parts[0]}:${parts[1]}`;
      }
    }

    return timeString;
  } catch (error) {
    console.error('Errore durante la formattazione dell\'orario:', error);
    return timeString;
  }
};

/**
 * Formatta un numero con le virgole come separatore delle migliaia
 */
export const formatNumberWithCommas = (value: number): string => {
  return new Intl.NumberFormat('it-IT').format(value);
};
