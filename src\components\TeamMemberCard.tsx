import React from "react";
import { motion } from "framer-motion";
import { Play } from "lucide-react";

interface TeamMemberCardProps {
  name: string;
  role: string;
  description: string;
  avatar: string;
  videoPlaceholder: string;
  videoId?: string; // ID del video YouTube (opzionale perché i video non sono ancora pronti)
  delay: number;
}

const TeamMemberCard: React.FC<TeamMemberCardProps> = ({
  name,
  role,
  description,
  avatar,
  videoPlaceholder,
  videoId,
  delay,
}) => {
  // Estrai il nome dal formato "Nome - Ruolo"
  const firstName = name.split(" - ")[0];
  const jobTitle = name.split(" - ")[1] || role;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      className="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden"
    >
      {/* Video section */}
      <div className="relative aspect-video w-full overflow-hidden bg-gray-100">
        {videoId ? (
          // Se il video è disponibile, mostralo
          <iframe
            width="100%"
            height="100%"
            src={`https://www.youtube.com/embed/${videoId}`}
            title={`${name} Video`}
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            className="absolute inset-0 w-full h-full"
          ></iframe>
        ) : (
          // Altrimenti, mostra un placeholder con un pulsante play
          <div className="absolute inset-0 w-full h-full">
            <img
              src={videoPlaceholder}
              alt={`${name} Video Placeholder`}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
              <div className="relative">
                <div className="absolute inset-0 bg-primary-600/50 rounded-full animate-ping opacity-70"></div>
                <div className="relative bg-primary-600 rounded-full p-4 cursor-pointer hover:bg-primary-700 transition-colors">
                  <Play className="h-8 w-8 text-white" />
                </div>
              </div>
            </div>
            <div className="absolute bottom-2 right-2 bg-amber-500 text-white text-xs px-2 py-1 rounded-full">
              Video in arrivo
            </div>
          </div>
        )}
      </div>

      {/* Content section */}
      <div className="p-5">
        <div className="flex items-center mb-3">
          <img
            src={avatar}
            alt={firstName}
            className="w-12 h-12 rounded-full object-cover border-2 border-primary-100 mr-3"
          />
          <div>
            <h3 className="text-xl font-bold text-gray-900">Ciao, sono {firstName}!</h3>
            <p className="text-sm text-gray-600">{jobTitle}</p>
          </div>
        </div>
        <p className="text-gray-700 mb-4">{description}</p>
        {/* <ul className="space-y-2">
          <li className="flex items-start">
            <div className="bg-primary-100 rounded-full p-1 mr-2 mt-0.5">
              <div className="w-1.5 h-1.5 bg-primary-600 rounded-full"></div>
            </div>
            <span className="text-sm text-gray-600">Risponde 24/7, sempre disponibile</span>
          </li>
          <li className="flex items-start">
            <div className="bg-primary-100 rounded-full p-1 mr-2 mt-0.5">
              <div className="w-1.5 h-1.5 bg-primary-600 rounded-full"></div>
            </div>
            <span className="text-sm text-gray-600">Personalizzabile in base alle tue esigenze</span>
          </li>
          <li className="flex items-start">
            <div className="bg-primary-100 rounded-full p-1 mr-2 mt-0.5">
              <div className="w-1.5 h-1.5 bg-primary-600 rounded-full"></div>
            </div>
            <span className="text-sm text-gray-600">Integrazione con i tuoi sistemi esistenti</span>
          </li>
        </ul> */}
      </div>
    </motion.div>
  );
};

export default TeamMemberCard;
