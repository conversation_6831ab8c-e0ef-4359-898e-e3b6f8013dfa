
-- Create categories_sub table (subcategories)
CREATE TABLE public.categories_sub (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  category_id uuid NOT NULL,
  name text NOT NULL,
  description text NULL,
  icon text NULL,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now())
);

-- Add foreign key constraint to categories table
ALTER TABLE public.categories_sub 
ADD CONSTRAINT fk_categories_sub_category_id 
FOREIGN KEY (category_id) REFERENCES public.categories(id) ON DELETE CASCADE;

-- Add index for better performance on foreign key
CREATE INDEX idx_categories_sub_category_id ON public.categories_sub(category_id);

-- Enable Row Level Security
ALTER TABLE public.categories_sub ENABLE ROW LEVEL SECURITY;

-- Policy: All users (including public) can select
CREATE POLICY "Everyone can view subcategories" 
ON public.categories_sub 
FOR SELECT 
USING (true);

-- Policy: Only admins can insert
CREATE POLICY "Only admins can create subcategories" 
ON public.categories_sub 
FOR INSERT 
WITH CHECK (get_user_role(auth.uid()) = 'admin');

-- Policy: Only admins can update
CREATE POLICY "Only admins can update subcategories" 
ON public.categories_sub 
FOR UPDATE 
USING (get_user_role(auth.uid()) = 'admin')
WITH CHECK (get_user_role(auth.uid()) = 'admin');

-- Create trigger for automatic updated_at timestamp
CREATE TRIGGER update_categories_sub_updated_at
BEFORE UPDATE ON public.categories_sub
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();
