
import { DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { PlusCircle, AlertTriangle } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useCanCreateBusiness } from "@/hooks/useBusinessLimits";
import { useNavigate } from "react-router-dom";
import { useState } from "react";

export const BusinessNewAction = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { canCreate, reason, currentCount, maxCount, isUnlimited, isLoading } = useCanCreateBusiness();
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);

  const handleNewBusinessClick = (e: React.MouseEvent) => {
    e.preventDefault();
    
    if (isLoading) {
      toast({
        title: "Caricamento",
        description: "Verifica dei limiti in corso...",
      });
      return;
    }

    if (canCreate) {
      // User can create a new business, navigate to create page
      navigate('/create-business');
    } else {
      // User has reached the limit, show upgrade dialog
      setShowUpgradeDialog(true);
    }
  };

  const handleUpgrade = () => {
    setShowUpgradeDialog(false);
    navigate('/subscription-plan');
  };

  return (
    <>
      <DropdownMenuItem asChild>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <a 
                href="#" 
                className={`flex items-center w-full ${!canCreate && !isLoading ? 'opacity-75' : ''}`}
                onClick={handleNewBusinessClick}
              >
                <PlusCircle className={`mr-2 h-4 w-4 ${canCreate || isLoading ? 'text-blue-600' : 'text-orange-500'}`} />
                <span className="flex-1">Crea nuova attività</span>
                {!isUnlimited && (
                  <span className="text-xs text-gray-500 ml-2">
                    ({currentCount}/{maxCount})
                  </span>
                )}
              </a>
            </TooltipTrigger>
            <TooltipContent>
              {canCreate ? (
                <p>Crea una nuova attività</p>
              ) : (
                <p>{reason}</p>
              )}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </DropdownMenuItem>

      {/* Upgrade Dialog */}
      <Dialog open={showUpgradeDialog} onOpenChange={setShowUpgradeDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              Limite raggiunto
            </DialogTitle>
            <DialogDescription>
              {reason}
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col gap-4 mt-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-600">
                <strong>Attuale:</strong> {currentCount} di {isUnlimited ? '∞' : maxCount} business
              </p>
            </div>
            <div className="flex gap-2 justify-end">
              <Button variant="outline" onClick={() => setShowUpgradeDialog(false)}>
                Annulla
              </Button>
              <Button onClick={handleUpgrade}>
                Aggiorna Piano
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
