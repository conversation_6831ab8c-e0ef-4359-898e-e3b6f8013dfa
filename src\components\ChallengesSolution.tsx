import React from "react";
import { motion } from "framer-motion";
import { CheckCircle2 } from "lucide-react";
interface Challenge {
  question: string;
  solution: string;
}
interface ChallengesSolutionProps {
  challenges: Challenge[];
  left_column_title: string;
  right_column_title: string;
}
const ChallengesSolution: React.FC<ChallengesSolutionProps> = ({
  challenges,
  left_column_title,
  right_column_title
}) => {
  return <div className="grid md:grid-cols-2 gap-12">
      {/* Challenges Column */}
      <motion.div initial={{
      opacity: 0,
      x: -20
    }} whileInView={{
      opacity: 1,
      x: 0
    }} viewport={{
      once: true
    }} transition={{
      duration: 0.5
    }} className="space-y-6">
        <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          <span className="text-primary-600">Prima di</span> CatchUp
        </h3>
        <ul className="space-y-4">
          {challenges.map((challenge, index) => <motion.li key={index} initial={{
          opacity: 0,
          y: 10
        }} whileInView={{
          opacity: 1,
          y: 0
        }} viewport={{
          once: true
        }} transition={{
          duration: 0.3,
          delay: index * 0.1
        }} className="flex items-start bg-white p-4 rounded-lg shadow-sm border border-gray-100 py-[31px]">
              <div className="bg-amber-100 rounded-full p-1 mr-3 mt-0.5">
                <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
              </div>
              <span className="text-gray-700">{challenge.question}</span>
            </motion.li>)}
        </ul>
      </motion.div>

      {/* Solutions Column */}
      <motion.div initial={{
      opacity: 0,
      x: 20
    }} whileInView={{
      opacity: 1,
      x: 0
    }} viewport={{
      once: true
    }} transition={{
      duration: 0.5
    }} className="space-y-6">
         <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          <span className="text-primary-600">Con</span> CatchUp
        </h3>
        <ul className="space-y-4">
          {challenges.map((challenge, index) => <motion.li key={index} initial={{
          opacity: 0,
          y: 10
        }} whileInView={{
          opacity: 1,
          y: 0
        }} viewport={{
          once: true
        }} transition={{
          duration: 0.3,
          delay: index * 0.1 + 0.2
        }} className="flex items-start bg-primary-50 p-4 rounded-lg shadow-sm border border-primary-100">
              <CheckCircle2 className="h-5 w-5 text-primary-600 mr-3 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700">{challenge.solution}</span>
            </motion.li>)}
        </ul>
      </motion.div>
    </div>;
};
export default ChallengesSolution;