
export interface GitHubIntegration {
  id: string;
  user_id: string;
  business_id: string;
  access_token: string;
  refresh_token?: string;
  expires_at?: string;
  scope: string;
  github_user_id: number;
  github_username: string;
  avatar_url?: string;
  repository_access_type: 'all' | 'selected';
  authorized_at: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface GitHubRepository {
  id: string;
  integration_id: string;
  github_repo_id: number;
  name: string;
  full_name: string;
  owner_login: string;
  owner_type: 'User' | 'Organization';
  is_private: boolean;
  clone_url: string;
  ssh_url: string;
  default_branch: string;
  selected_at: string;
  created_at: string;
}

export interface GitHubRepoData {
  id: number;
  name: string;
  full_name: string;
  owner: {
    login: string;
    type: 'User' | 'Organization';
  };
  private: boolean;
  clone_url: string;
  ssh_url: string;
  default_branch: string;
}

export interface GitHubUser {
  id: number;
  login: string;
  avatar_url: string;
}
