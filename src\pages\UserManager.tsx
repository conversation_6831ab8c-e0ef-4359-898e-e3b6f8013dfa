
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import MainLayout from "@/layouts/MainLayout";
import { toast } from "sonner";
import { UserWithDetails } from "@/types/supabase";
import { RoleSelector } from "@/components/RoleSelector";
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  createColumnHelper,
  flexRender,
} from "@tanstack/react-table";
import { DataTablePagination } from "@/components/DataTablePagination";

const columnHelper = createColumnHelper<UserWithDetails>();

const UserManager = () => {
  const [refreshKey, setRefreshKey] = useState(0);

  const { data: users, isLoading, refetch } = useQuery({
    queryKey: ["users", refreshKey],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("users_with_details")
        .select();

      if (error) {
        toast.error("Errore nel caricamento degli utenti");
        throw error;
      }

      return (data || []) as UserWithDetails[];
    },
  });

  const getInitials = (user: UserWithDetails) => {
    if (user.first_name && user.last_name) {
      return `${user.first_name[0]}${user.last_name[0]}`.toUpperCase();
    }
    return user.email[0].toUpperCase();
  };

  const handleRoleChange = () => {
    setRefreshKey(prev => prev + 1);
  };

  const columns = [
    columnHelper.accessor("email", {
      header: "Utente",
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div className="flex items-center gap-3">
            <Avatar>
              <AvatarImage src={user.avatar_url || undefined} />
              <AvatarFallback>{getInitials(user)}</AvatarFallback>
            </Avatar>
            <span>{user.display_name || user.email}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("email", {
      header: "Email",
      cell: ({ getValue }) => getValue(),
    }),
    columnHelper.accessor((row) => `${row.first_name} ${row.last_name}`, {
      id: "name",
      header: "Nome",
      cell: ({ row }) => {
        const user = row.original;
        return user.first_name && user.last_name
          ? `${user.first_name} ${user.last_name}`
          : "-";
      },
    }),
    columnHelper.accessor("phone_number", {
      header: "Telefono",
      cell: ({ getValue }) => getValue() || "-",
    }),
    columnHelper.accessor("role", {
      header: "Ruolo",
      cell: ({ row }) => {
        const user = row.original;
        return (
          <RoleSelector
            currentRole={user.role}
            userId={user.id}
            onRoleChange={handleRoleChange}
          />
        );
      },
    }),
    columnHelper.accessor("registered_at", {
      header: "Registrato il",
      cell: ({ getValue }) => new Date(getValue()).toLocaleDateString("it-IT"),
    }),
  ];

  const table = useReactTable({
    data: users || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  });

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <h1 className="text-2xl font-semibold mb-8">Gestione Utenti</h1>

        <div className="bg-white rounded-lg shadow">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    Caricamento...
                  </TableCell>
                </TableRow>
              ) : table.getRowModel().rows.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    Nessun utente trovato
                  </TableCell>
                </TableRow>
              ) : (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
          <DataTablePagination table={table} />
        </div>
      </div>
    </MainLayout>
  );
};

export default UserManager;
