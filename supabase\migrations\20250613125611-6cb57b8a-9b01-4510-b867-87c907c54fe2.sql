
-- Create table for GitHub OAuth integrations
CREATE TABLE github_integrations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  business_id UUID NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
  access_token TEXT NOT NULL,
  refresh_token TEXT,
  expires_at TIMESTAMPTZ,
  scope TEXT NOT NULL,
  github_user_id INTEGER NOT NULL,
  github_username TEXT NOT NULL,
  avatar_url TEXT,
  repository_access_type TEXT NOT NULL CHECK (repository_access_type IN ('all', 'selected')),
  authorized_at TIMESTAMPTZ DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create table for selected GitHub repositories
CREATE TABLE github_repositories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  integration_id UUID NOT NULL REFERENCES github_integrations(id) ON DELETE CASCADE,
  github_repo_id INTEGER NOT NULL,
  name TEXT NOT NULL,
  full_name TEXT NOT NULL,
  owner_login TEXT NOT NULL,
  owner_type TEXT NOT NULL CHECK (owner_type IN ('User', 'Organization')),
  is_private BOOLEAN DEFAULT false,
  clone_url TEXT NOT NULL,
  ssh_url TEXT NOT NULL,
  default_branch TEXT DEFAULT 'main',
  selected_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_github_integrations_user_business ON github_integrations(user_id, business_id);
CREATE INDEX idx_github_integrations_github_user ON github_integrations(github_user_id);
CREATE INDEX idx_github_repositories_integration ON github_repositories(integration_id);
CREATE INDEX idx_github_repositories_github_repo ON github_repositories(github_repo_id);

-- Enable RLS (Row Level Security)
ALTER TABLE github_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE github_repositories ENABLE ROW LEVEL SECURITY;

-- RLS policies for github_integrations
CREATE POLICY "Users can view their own GitHub integrations" ON github_integrations
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own GitHub integrations" ON github_integrations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own GitHub integrations" ON github_integrations
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own GitHub integrations" ON github_integrations
  FOR DELETE USING (auth.uid() = user_id);

-- RLS policies for github_repositories
CREATE POLICY "Users can view repositories from their integrations" ON github_repositories
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM github_integrations gi 
      WHERE gi.id = integration_id AND gi.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert repositories to their integrations" ON github_repositories
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM github_integrations gi 
      WHERE gi.id = integration_id AND gi.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update repositories from their integrations" ON github_repositories
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM github_integrations gi 
      WHERE gi.id = integration_id AND gi.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete repositories from their integrations" ON github_repositories
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM github_integrations gi 
      WHERE gi.id = integration_id AND gi.user_id = auth.uid()
    )
  );

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_github_integrations_updated_at 
  BEFORE UPDATE ON github_integrations 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
