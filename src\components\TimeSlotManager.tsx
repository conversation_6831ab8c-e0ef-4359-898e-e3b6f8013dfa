import { useState } from "react";
import { Plus, Trash2, Clock, Calendar, Copy, Check } from "lucide-react";
import { DaySchedule, TimeSlot, WeeklySchedule } from "@/types/deals";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";

interface TimeSlotManagerProps {
  value: WeeklySchedule;
  onChange: (schedule: WeeklySchedule) => void;
}

const TimeSlotManager = ({ value, onChange }: TimeSlotManagerProps) => {
  const [newStartTime, setNewStartTime] = useState("");
  const [newEndTime, setNewEndTime] = useState("");
  const [newSeats, setNewSeats] = useState("");
  const [selectedDay, setSelectedDay] = useState<number>(1);
  const [newException, setNewException] = useState("");
  const [copyDialogOpen, setCopyDialogOpen] = useState(false);
  const [selectedDays, setSelectedDays] = useState<number[]>([]);

  const handleAddTimeSlot = () => {
    if (!newStartTime || !newEndTime) return;

    const seatsValue = parseInt(newSeats);
    if (isNaN(seatsValue) || seatsValue < 1) return;

    const updatedSchedule = value.schedule.map((daySchedule) => {
      if (daySchedule.day === selectedDay) {
        return {
          ...daySchedule,
          time_slots: [
            ...daySchedule.time_slots,
            {
              start_time: newStartTime,
              end_time: newEndTime,
              available_seats: seatsValue,
            },
          ],
        };
      }
      return daySchedule;
    });

    onChange({
      ...value,
      schedule: updatedSchedule,
    });

    setNewStartTime("");
    setNewEndTime("");
    setNewSeats("");
  };

  const handleRemoveTimeSlot = (dayIndex: number, slotIndex: number) => {
    const updatedSchedule = value.schedule.map((daySchedule, idx) => {
      if (idx === dayIndex) {
        return {
          ...daySchedule,
          time_slots: daySchedule.time_slots.filter((_, i) => i !== slotIndex),
        };
      }
      return daySchedule;
    });

    onChange({
      ...value,
      schedule: updatedSchedule,
    });
  };

  const handleAddException = () => {
    if (!newException) return;

    onChange({
      ...value,
      exceptions: [...value.exceptions, newException],
    });

    setNewException("");
  };

  const handleRemoveException = (index: number) => {
    onChange({
      ...value,
      exceptions: value.exceptions.filter((_, i) => i !== index),
    });
  };

  const handleCopyTimeSlots = () => {
    if (selectedDays.length === 0) {
      setCopyDialogOpen(false);
      return;
    }

    const sourceDay = value.schedule.find((day) => day.day === selectedDay);
    if (!sourceDay || sourceDay.time_slots.length === 0) {
      setCopyDialogOpen(false);
      return;
    }

    const updatedSchedule = value.schedule.map((day) => {
      if (selectedDays.includes(day.day) && day.day !== selectedDay) {
        return {
          ...day,
          time_slots: [...sourceDay.time_slots],
        };
      }
      return day;
    });

    onChange({
      ...value,
      schedule: updatedSchedule,
    });

    setCopyDialogOpen(false);
    setSelectedDays([]);
  };

  const toggleDaySelection = (day: number) => {
    setSelectedDays((prev) =>
      prev.includes(day) ? prev.filter((d) => d !== day) : [...prev, day]
    );
  };

  const selectedDayTimeSlots =
    value.schedule.find((d) => d.day === selectedDay)?.time_slots || [];
  const hasTimeSlots = selectedDayTimeSlots.length > 0;

  return (
    <div className="space-y-6">
      {/* Weekly Schedule Section */}
      <Card>
        <CardHeader>
          <CardTitle>Orari Settimanali</CardTitle>
          <CardDescription>
            Seleziona un giorno e aggiungi gli orari disponibili
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Day Selection Grid - Desktop Version */}
          <div className="grid grid-cols-7 gap-2">
            {value.schedule.map((day) => {
              const hasTimeSlots = day.time_slots.length > 0;
              return (
                <button
                  key={day.day}
                  type="button"
                  onClick={() => setSelectedDay(day.day)}
                  className={`
                    relative p-3 rounded-lg text-sm font-medium transition-all duration-200
                    ${
                      selectedDay === day.day
                        ? "bg-primary text-primary-foreground shadow-md"
                        : hasTimeSlots
                        ? "bg-green-50 border-2 border-green-200 text-green-700 hover:bg-green-100"
                        : "bg-muted border border-border text-muted-foreground hover:bg-muted/80"
                    }
                  `}
                >
                  <span className="block text-xs font-bold truncate">
                    {day.day_name}
                  </span>
                  {hasTimeSlots && (
                    <div className="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center text-white text-xs">
                      {day.time_slots.length}
                    </div>
                  )}
                </button>
              );
            })}
          </div>

          {/* Selected Day Info */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h4 className="text-lg font-medium">
                {value.schedule.find((d) => d.day === selectedDay)?.day_name}
              </h4>

              {hasTimeSlots && (
                <Dialog open={copyDialogOpen} onOpenChange={setCopyDialogOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Copy className="h-4 w-4 mr-2" />
                      Copia orari
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Copia fasce orarie</DialogTitle>
                      <DialogDescription>
                        Seleziona i giorni in cui vuoi copiare le fasce orarie
                        di{" "}
                        {
                          value.schedule.find((d) => d.day === selectedDay)
                            ?.day_name
                        }
                      </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-3">
                      {value.schedule
                        .filter((day) => day.day !== selectedDay)
                        .map((day) => (
                          <div
                            key={day.day}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              id={`day-${day.day}`}
                              checked={selectedDays.includes(day.day)}
                              onCheckedChange={() =>
                                toggleDaySelection(day.day)
                              }
                            />
                            <label
                              htmlFor={`day-${day.day}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {day.day_name}
                              {day.time_slots.length > 0 && (
                                <span className="text-xs text-amber-600 ml-2">
                                  (Sostituirà {day.time_slots.length} fasce
                                  orarie esistenti)
                                </span>
                              )}
                            </label>
                          </div>
                        ))}
                    </div>

                    <DialogFooter>
                      <Button
                        type="button"
                        onClick={() => setCopyDialogOpen(false)}
                        variant="outline"
                      >
                        Annulla
                      </Button>
                      <Button
                        type="button"
                        onClick={handleCopyTimeSlots}
                        disabled={selectedDays.length === 0}
                      >
                        <Check className="h-4 w-4 mr-2" />
                        Conferma
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              )}
            </div>

            {/* Time Slot Input */}
            <div className="grid grid-cols-4 gap-4 items-end">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Ora inizio
                </label>
                <Input
                  type="time"
                  value={newStartTime}
                  onChange={(e) => setNewStartTime(e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">
                  Ora fine
                </label>
                <Input
                  type="time"
                  value={newEndTime}
                  onChange={(e) => setNewEndTime(e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">
                  Posti
                </label>
                <Input
                  type="number"
                  min="1"
                  value={newSeats}
                  onChange={(e) => setNewSeats(e.target.value)}
                />
              </div>
              <div>
                <Button
                  type="button"
                  onClick={handleAddTimeSlot}
                  disabled={!newStartTime || !newEndTime}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Aggiungi
                </Button>
              </div>
            </div>
          </div>

          {/* Time Slots List */}
          <div>
            {selectedDayTimeSlots.length > 0 ? (
              <div className="space-y-2">
                {selectedDayTimeSlots.map((slot, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-3 p-3 bg-muted rounded-lg"
                  >
                    <div className="bg-primary/10 p-2 rounded-full">
                      <Clock className="h-4 w-4 text-primary" />
                    </div>
                    <div className="flex-1">
                      <span className="font-medium block">
                        {slot.start_time} - {slot.end_time}
                      </span>
                      <span className="text-sm text-muted-foreground">
                        Posti: {slot.available_seats}
                      </span>
                    </div>
                    <Button
                      type="button"
                      onClick={() => handleRemoveTimeSlot(selectedDay - 1, index)}
                      variant="ghost"
                      size="sm"
                      className="text-destructive hover:bg-destructive/10"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 bg-muted rounded-lg border-dashed border-2">
                <Clock className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">
                  Nessun orario impostato per questo giorno
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Exceptions Section */}
      <Card>
        <CardHeader>
          <CardTitle>Date di Chiusura</CardTitle>
          <CardDescription>
            Aggiungi le date in cui non sarai disponibile
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <label className="block text-sm font-medium mb-2">
                Data di chiusura
              </label>
              <Input
                type="date"
                value={newException}
                onChange={(e) => setNewException(e.target.value)}
              />
            </div>
            <Button
              type="button"
              onClick={handleAddException}
              disabled={!newException}
            >
              <Plus className="h-4 w-4 mr-2" />
              Aggiungi
            </Button>
          </div>

          {/* Exceptions List */}
          {value.exceptions.length > 0 ? (
            <div className="space-y-2">
              {value.exceptions.map((date, index) => (
                <div
                  key={index}
                  className="flex items-center gap-3 p-3 bg-muted rounded-lg"
                >
                  <div className="bg-destructive/10 p-2 rounded-full">
                    <Calendar className="h-4 w-4 text-destructive" />
                  </div>
                  <span className="flex-1 font-medium">
                    {format(new Date(date), "EEEE d MMMM yyyy", { locale: it })}
                  </span>
                  <Button
                    type="button"
                    onClick={() => handleRemoveException(index)}
                    variant="ghost"
                    size="sm"
                    className="text-destructive hover:bg-destructive/10"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 bg-muted rounded-lg border-dashed border-2">
              <Calendar className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">
                Nessuna data di chiusura impostata
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TimeSlotManager;