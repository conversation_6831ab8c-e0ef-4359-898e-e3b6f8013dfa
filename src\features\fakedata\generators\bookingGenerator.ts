import { faker } from '@faker-js/faker';
import { User<PERSON>ersona, FakeDataResult } from '../types';
import { ageGroupBehavior, locationPreferences } from '../data/customers';
import { supabase } from '@/integrations/supabase/client';

export class BookingGenerator {
  
  private async getFakeUsers(): Promise<any[]> {
    const { data: users } = await supabase.auth.admin.listUsers();
    return users?.users?.filter((user: any) => user.user_metadata?.fake === true) || [];
  }

  private async getAvailableDeals(
    dateRange: { start: Date; end: Date },
    cityFilter?: string,
    categoryFilter?: string
  ): Promise<any[]> {
    let query = supabase
      .from('deals')
      .select(`
        *,
        businesses!inner (
          id, name, address, city, category_id, latitude, longitude
        )
      `)
      .eq('fake', true)
      .eq('status', 'published')
      .gte('end_date', dateRange.start.toISOString().split('T')[0])
      .lte('start_date', dateRange.end.toISOString().split('T')[0]);

    if (cityFilter) {
      query = query.eq('businesses.city', cityFilter);
    }

    if (categoryFilter) {
      query = query.eq('businesses.category_id', categoryFilter);
    }

    const { data, error } = await query;
    
    if (error) {
      console.error('Error fetching deals:', error);
      return [];
    }

    return data || [];
  }

  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c * 1000; // Return in meters
  }

  private filterDealsByUserPreferences(deals: any[], user: any): any[] {
    const persona: UserPersona = user.user_metadata.persona;
    const userLat = faker.datatype.float({ min: 45.4, max: 45.5 }); // Fake user location
    const userLng = faker.datatype.float({ min: 9.1, max: 9.2 });

    let filteredDeals = deals.filter(deal => {
      // Filter by preferred categories
      const businessCategory = deal.businesses.category_id;
      // For now, we'll be less strict about category matching
      
      // Filter by location preference
      if (persona.locationPreference === 'local') {
        const distance = this.calculateDistance(
          userLat, userLng,
          deal.businesses.latitude, deal.businesses.longitude
        );
        return distance <= locationPreferences.local.maxDistance;
      }
      
      return true; // Citywide users can book anywhere
    });

    // Limit choices based on booking frequency
    const behaviorLimits = {
      high: deals.length,
      medium: Math.min(deals.length, 10),  
      low: Math.min(deals.length, 5)
    };

    const maxDeals = behaviorLimits[persona.bookingFrequency];
    
    return faker.helpers.shuffle(filteredDeals).slice(0, maxDeals);
  }

  private generateBookingDateTime(deal: any, userAgeGroup: string): { date: Date; time: string; endTime: string } {
    const startDate = new Date(deal.start_date);
    const endDate = new Date(deal.end_date);  
    const behavior = ageGroupBehavior[userAgeGroup as keyof typeof ageGroupBehavior];
    
    // Generate booking date within deal period, respecting user advance booking patterns
    const advanceTimeRange = behavior.bookingAdvanceTime;
    const advanceDays = faker.datatype.number({ 
      min: advanceTimeRange[0], 
      max: advanceTimeRange[1] 
    });
    
    // Calculate booking date (advance time before a random date in the deal period)
    const targetDate = faker.date.between(startDate, endDate);
    const bookingDate = new Date(targetDate.getTime() - (advanceDays * 24 * 60 * 60 * 1000));
    
    // Ensure booking date is not in the past and not after deal start
    const minBookingDate = new Date();
    const actualBookingDate = new Date(Math.max(minBookingDate.getTime(), bookingDate.getTime()));
    const finalBookingDate = new Date(Math.min(actualBookingDate.getTime(), targetDate.getTime()));

    // Generate time based on user preferences and deal availability
    const preferredTimes = behavior.preferredTimes;
    const availableSlots = deal.time_slots?.schedule || [];
    
    // Find available day
    const dayOfWeek = finalBookingDate.getDay();
    const adjustedDay = dayOfWeek === 0 ? 7 : dayOfWeek; // Convert Sunday from 0 to 7
    
    const daySchedule = availableSlots.find((slot: any) => slot.day === adjustedDay);
    
    let bookingTime: string;
    let endTime: string;
    
    if (daySchedule && daySchedule.time_slots.length > 0) {
      const availableTimeSlot = faker.helpers.arrayElement(daySchedule.time_slots);
      bookingTime = (availableTimeSlot as any).start_time;
      endTime = (availableTimeSlot as any).end_time;
    } else {
      // Fallback to user preferences
      bookingTime = faker.helpers.arrayElement(preferredTimes);
      const startHour = parseInt(bookingTime.split(':')[0]);
      endTime = `${(startHour + 1).toString().padStart(2, '0')}:00`;
    }

    return {
      date: finalBookingDate,
      time: bookingTime,
      endTime: endTime
    };
  }

  private generateQRData(booking: any): any {
    return {
      booking_id: booking.id,
      user_id: booking.user_id,
      deal_id: booking.deal_id,
      business_name: booking.business_name,
      booking_date: booking.booking_date,
      booking_time: booking.booking_time,
      validation_code: faker.datatype.uuid(),
      generated_at: new Date().toISOString()
    };
  }

  public async generateBooking(
    userId: string,
    userMetadata: any,
    availableDeals: any[]
  ): Promise<any> {
    if (availableDeals.length === 0) {
      throw new Error('No available deals for this user');
    }

    // Filter deals based on user preferences
    const suitableDeals = this.filterDealsByUserPreferences(availableDeals, { 
      user_metadata: userMetadata 
    });
    
    if (suitableDeals.length === 0) {
      throw new Error('No suitable deals found for user preferences');
    }

    const selectedDeal = faker.helpers.arrayElement(suitableDeals);
    const bookingDateTime = this.generateBookingDateTime(selectedDeal, userMetadata.age_group);
    
    // Generate booking status (mostly confirmed, some pending)
    const statuses = ['confirmed', 'confirmed', 'confirmed', 'pending']; // 75% confirmed
    const status = faker.helpers.arrayElement(statuses);

    const booking = {
      user_id: userId,
      deal_id: selectedDeal.id,
      booking_date: bookingDateTime.date.toISOString().split('T')[0],
      booking_time: bookingDateTime.time,
      booking_end_time: bookingDateTime.endTime,
      original_price: selectedDeal.original_price,
      discounted_price: selectedDeal.discounted_price,
      discount_percentage: selectedDeal.discount_percentage,
      status,
      qr_data: {}, // Will be filled after creation
      fake: true
    };

    return booking;
  }

  public async generateMultipleBookings(
    dateRange: { start: Date; end: Date },
    count: number,
    cityFilter?: string,
    categoryFilter?: string,
    businessIds?: string[],
    onProgress?: (current: number, total: number, message: string) => void
  ): Promise<FakeDataResult> {
    const result: FakeDataResult = {
      created: 0,
      errors: [],
      warnings: []
    };

    try {
      // Get fake users and available deals
      const [users, deals] = await Promise.all([
        this.getFakeUsers(),
        this.getAvailableDeals(dateRange, cityFilter, categoryFilter)
      ]);

      if (users.length === 0) {
        result.errors.push('No fake users found. Generate users first.');
        return result;
      }

      if (deals.length === 0) {
        result.errors.push('No deals found for the specified criteria.');
        return result;
      }

      // Filter deals by business IDs if specified
      let filteredDeals = deals;
      if (businessIds && businessIds.length > 0) {
        filteredDeals = deals.filter(deal => businessIds.includes(deal.business_id));
      }

      result.warnings.push(`Found ${users.length} users and ${filteredDeals.length} deals`);

      // Generate bookings
      for (let i = 0; i < count; i++) {
        try {
          onProgress?.(i + 1, count, `Creazione prenotazione ${i + 1}`);

          // Select random user
          const user = faker.helpers.arrayElement(users);
          
          // Generate booking
          const booking = await this.generateBooking(
            user.id,
            user.user_metadata,
            filteredDeals
          );

          // Insert booking
          const { data: insertedBooking, error } = await supabase
            .from('bookings')
            .insert(booking)
            .select()
            .single();

          if (error) {
            result.errors.push(`Booking ${i + 1}: ${error.message}`);
            continue;
          }

          // Update QR data
          const qrData = this.generateQRData(insertedBooking);
          await supabase
            .from('bookings')
            .update({ qr_data: qrData })
            .eq('id', insertedBooking.id);

          result.created++;

          // Small delay
          await new Promise(resolve => setTimeout(resolve, 50));

        } catch (error) {
          result.errors.push(`Booking ${i + 1}: ${error}`);
        }
      }

    } catch (error) {
      result.errors.push(`General error: ${error}`);
    }

    return result;
  }

  public async deleteAllFakeBookings(): Promise<FakeDataResult> {
    const result: FakeDataResult = {
      created: 0, // Will use as deleted count
      errors: [],
      warnings: []
    };

    try {
      const { data: deletedBookings, error } = await supabase
        .from('bookings')
        .delete()
        .eq('fake', true)
        .select('id');

      if (error) {
        result.errors.push(`Error deleting bookings: ${error.message}`);
      } else {
        result.created = deletedBookings?.length || 0;
        result.warnings.push(`Deleted ${result.created} fake bookings`);
      }

    } catch (error) {
      result.errors.push(`General error: ${error}`);
    }

    return result;
  }
}