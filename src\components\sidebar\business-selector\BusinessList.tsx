
import { Business } from "../../../types/types";
import { ScrollArea } from "@/components/ui/scroll-area";
import { DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ChevronRight, PackageCheck, Filter } from "lucide-react";

interface BusinessListProps {
  isLoading: boolean;
  filteredBusinesses: Business[];
  selectedBusiness: Business | null;
  showWithDeals: boolean;
  getBusinessInitials: (name: string) => string;
  onSelectBusiness: (business: Business) => void;
  setShowWithDeals: (value: boolean) => void;
  setIsOpen: (value: boolean) => void;
  setSearchTerm: (value: string) => void;
}

export const BusinessList = ({
  isLoading,
  filteredBusinesses,
  selectedBusiness,
  showWithDeals,
  getBusinessInitials,
  onSelectBusiness,
  setShowWithDeals,
  setIsOpen,
  setSearchTerm
}: BusinessListProps) => {
  if (isLoading) {
    return (
      <div className="p-2">
        <div className="flex items-center space-y-2">
          <div className="h-5 w-5 rounded-full bg-gray-200 animate-pulse mr-2"></div>
          <div className="h-4 w-32 bg-gray-200 animate-pulse rounded"></div>
        </div>
      </div>
    );
  }

  if (filteredBusinesses.length === 0) {
    return (
      <div className="p-4 text-sm text-center">
        <div className="flex flex-col items-center justify-center text-muted-foreground">
          <Filter className="h-10 w-10 mb-2 opacity-20" />
          <p>Nessuna attività corrisponde ai filtri selezionati</p>
          {showWithDeals && (
            <Button
              variant="ghost"
              size="sm"
              className="mt-2 text-xs"
              onClick={() => setShowWithDeals(false)}
            >
              Rimuovi filtri
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <ScrollArea className="h-[200px] py-1">
      {filteredBusinesses.map((business) => (
        <DropdownMenuItem 
          key={business.id} 
          className="flex items-center justify-between mx-1 pl-2 pr-1 py-2"
          onClick={() => {
            onSelectBusiness(business);
            setSearchTerm("");
            setIsOpen(false);
          }}
        >
          <div className="flex items-center">
            <Avatar className="h-6 w-6 mr-2 bg-blue-100">
              <AvatarFallback className="text-xs bg-blue-100 text-blue-600">
                {getBusinessInitials(business.name)}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="text-sm">{business.name}</span>
              <span className="text-xs text-muted-foreground">{business.deal_count || 0} offerte</span>
            </div>
          </div>
          <div className="flex items-center gap-1">
            {business.deal_count > 0 && (
              <Badge variant="outline" className="flex items-center h-5 px-1 border-blue-200 bg-blue-50 text-blue-700">
                <PackageCheck className="h-3 w-3 mr-0.5" />
                <span className="text-xs">{business.deal_count}</span>
              </Badge>
            )}
            {selectedBusiness?.id === business.id && (
              <ChevronRight className="h-4 w-4 ml-1 text-muted-foreground" />
            )}
          </div>
        </DropdownMenuItem>
      ))}
    </ScrollArea>
  );
};
