
import { useBusinessStore } from "@/store/businessStore";
import { useAuth } from "@/contexts/AuthContext";
import { SidebarLayout } from "./SidebarLayout";

export interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout = ({ children }: MainLayoutProps) => {


  return (
    <SidebarLayout>{children}</SidebarLayout>

    /*
    <div className="flex h-screen overflow-hidden bg-gray-50">
      <Sidebar isOpen={sidebarOpen} onToggle={() => setSidebarOpen(!sidebarOpen)} />

      <div className={`flex-1 overflow-auto transition-all duration-200 ${sidebarOpen ? 'ml-64' : 'ml-0'}`}>
        <header className="sticky top-0 z-40 bg-white border-b">
          <div className="flex items-center justify-between h-16 px-4">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="mr-4 md:hidden"
              >
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle sidebar</span>
              </Button>
            </div>

            {selectedBusiness && (
              <div className="text-lg font-medium text-gray-700">
                {selectedBusiness.name}
              </div>
            )}
          </div>
        </header>

        <main>
          {children}
        </main>
      </div>
    </div> */
  );
};

export default MainLayout;
