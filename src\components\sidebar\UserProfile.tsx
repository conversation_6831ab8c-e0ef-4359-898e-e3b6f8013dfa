import { useState } from "react";
import { UserDetails } from "../../types/types";
import { supabase } from "@/integrations/supabase/client";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SidebarMenu, SidebarMenuButton, useSidebar } from "../ui/sidebar";
import { SidebarMenuItem } from "../ui/sidebar";
import {
  User,
  CreditCard,
  Bell,
  LogOut,
  ChevronRight,
  Sparkles,
  BadgeCheck,
  ChevronsUpDown,
} from "lucide-react";

interface UserProfileProps {
  userDetails: UserDetails | null;
  isLoading: boolean;
}

export const UserProfile = ({ userDetails, isLoading }: UserProfileProps) => {
  // Handle sign out
  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  // Get user initials for avatar fallback
  const getUserInitials = () => {
    if (!userDetails) return "U";

    if (userDetails.first_name && userDetails.last_name) {
      return `${userDetails.first_name.charAt(0)}${userDetails.last_name.charAt(
        0
      )}`;
    } else if (userDetails.display_name) {
      return userDetails.display_name.charAt(0);
    } else if (userDetails.email) {
      return userDetails.email.charAt(0).toUpperCase();
    }
    return "U";
  };

  // Get display name for the user
  const getDisplayName = () => {
    if (!userDetails) return "User";

    if (userDetails.first_name && userDetails.last_name) {
      return `${userDetails.first_name} ${userDetails.last_name}`;
    } else if (userDetails.display_name) {
      return userDetails.display_name;
    } else if (userDetails.email) {
      return userDetails.email.split("@")[0];
    }
    return "User";
  };
  const { isMobile } = useSidebar();
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarImage
                  src={userDetails?.avatar_url || "/placeholder-avatar.svg"}
                  alt={getDisplayName()}
                />
                <AvatarFallback className="rounded-lg">CN</AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">
                  {getDisplayName()}
                </span>
                <span className="truncate text-xs">{userDetails?.email}</span>
              </div>
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarImage
                    src={userDetails?.avatar_url || "/placeholder-avatar.svg"}
                    alt={getDisplayName()}
                  />
                  <AvatarFallback className="rounded-lg">CN</AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">
                    {getDisplayName()}
                  </span>
                  <span className="truncate text-xs">{userDetails?.email}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem>
                <Sparkles />
                Upgrade to Pro
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem asChild>
                <a href="/settings">
                  <BadgeCheck className="mr-2 h-4 w-4" />
                  Impostazioni
                </a>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <CreditCard className="mr-2 h-4 w-4" />
                Fatturazione
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Bell className="mr-2 h-4 w-4" />
                Notifiche
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleSignOut}>
              <LogOut className="mr-2 h-4 w-4" />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
};
