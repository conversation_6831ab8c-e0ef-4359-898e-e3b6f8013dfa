import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Search, Edit, Trash2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { SubCategoryModal } from "@/components/subcategories/SubCategoryModal";
import { SubCategory } from "@/types/types";
import MainLayout from "@/layouts/MainLayout";

interface SubCategoryWithCategory extends SubCategory {
  category_name?: string;
}

const AdminSubcategories = () => {
  const { toast } = useToast();
  const [subcategories, setSubcategories] = useState<SubCategoryWithCategory[]>([]);
  const [filteredSubcategories, setFilteredSubcategories] = useState<SubCategoryWithCategory[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedSubcategory, setSelectedSubcategory] = useState<SubCategory | null>(null);

  useEffect(() => {
    fetchSubcategories();
  }, []);

  useEffect(() => {
    const filtered = subcategories.filter(subcategory =>
      subcategory.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (subcategory.description && subcategory.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (subcategory.category_name && subcategory.category_name.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    setFilteredSubcategories(filtered);
  }, [subcategories, searchTerm]);

  const fetchSubcategories = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from("categories_sub")
        .select(`
          *,
          categories!inner(name)
        `)
        .order("name");

      if (error) throw error;

      const subcategoriesWithCategory = data?.map(item => ({
        ...item,
        category_name: item.categories?.name
      })) || [];

      setSubcategories(subcategoriesWithCategory);
    } catch (error: any) {
      toast({
        title: "Errore",
        description: "Impossibile caricare le sottocategorie",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (subcategory: SubCategory) => {
    setSelectedSubcategory(subcategory);
    setIsModalOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Sei sicuro di voler eliminare questa sottocategoria?")) {
      return;
    }

    try {
      const { error } = await supabase
        .from("categories_sub")
        .delete()
        .eq("id", id);

      if (error) throw error;

      toast({
        title: "Successo",
        description: "Sottocategoria eliminata con successo",
      });

      fetchSubcategories();
    } catch (error: any) {
      toast({
        title: "Errore",
        description: error.message || "Impossibile eliminare la sottocategoria",
        variant: "destructive",
      });
    }
  };

  const handleCreateNew = () => {
    setSelectedSubcategory(null);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedSubcategory(null);
  };

  const handleModalSuccess = () => {
    fetchSubcategories();
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <MainLayout>
   <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Gestione Sub Categorie</h1>
          
        </div>
 
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Gestione Sottocategorie</CardTitle>
              <CardDescription>
                Gestisci le sottocategorie del sistema. Totale: {subcategories.length}
              </CardDescription>
            </div>
            <Button onClick={handleCreateNew}>
              <Plus className="w-4 h-4 mr-2" />
              Nuova Sottocategoria
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Search className="w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Cerca sottocategorie..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Categoria Principale</TableHead>
                  <TableHead>Descrizione</TableHead>
                  <TableHead>Icona</TableHead>
                  <TableHead>Creata il</TableHead>
                  <TableHead className="text-right">Azioni</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSubcategories.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      {searchTerm ? "Nessuna sottocategoria trovata" : "Nessuna sottocategoria presente"}
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredSubcategories.map((subcategory) => (
                    <TableRow key={subcategory.id}>
                      <TableCell className="font-medium">{subcategory.name}</TableCell>
                      <TableCell>
                        <Badge variant="secondary">{subcategory.category_name}</Badge>
                      </TableCell>
                      <TableCell className="max-w-xs truncate">
                        {subcategory.description || "-"}
                      </TableCell>
                      <TableCell>
                        {subcategory.icon || "-"}
                      </TableCell>
                      <TableCell>
                        {new Date(subcategory.created_at).toLocaleDateString("it-IT")}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(subcategory)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(subcategory.id)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <SubCategoryModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        subcategory={selectedSubcategory}
        onSuccess={handleModalSuccess}
      />
      </div>
       </MainLayout>
  );
};

export default AdminSubcategories;