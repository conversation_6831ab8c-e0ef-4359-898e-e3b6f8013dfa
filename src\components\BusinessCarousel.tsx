
import React from "react";
import { motion } from "framer-motion";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

interface BusinessSlide {
  image: string;
  title: string;
  description: string;
}

const businessSlides: BusinessSlide[] = [
  {
    //image: "https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=1080&fit=max",
    image: "/images/salone di bellezza.jpg",
    title: "Salone di Bellezza",
    description: "I clienti prenotano a qualsiasi ora del giorno e della notte, il tuo team  IA  dedicato,  dà informazioni, riceve le prenotazioni, gestisce le tue pagine social, costruisce campagne marketing, analizza l’andamento delle vendite pubblicando report e statistiche, mentre tu ti dedichi ad altro."
  },
  {
    //image: "https://images.unsplash.com/photo-1562004760-aceed7bb0fe3?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=1080&fit=max",
    image: "/images/parrucchieri.jpg",
    title: "Parrucchieri e Barbieri",
    description: "Tu, dedicati ai clienti, mentre il tuo team IA dedicato pensa al tuo business.Riceve le prenotazioni e dà le informazioni anche quando il negozio è chiuso. Gestisce le tue pagine social, costruisce campagne marketing, analizza l’andamento delle vendite pubblicando per te report e statistiche, mentre tu ti dedichi ad altro."

  },
  {
    //image: "https://images.unsplash.com/photo-1534438327276-14e5300c3a48?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=1080&fit=max",
    image: "/iamges/centro fitness.jpg",
    title: "Centro Fitness",
    description: "Un Team IA dedicato al tuo Business 24 ore al giorno, 7 giorni su 7. Riceve le prenotazioni e dà le informazioni anche quando il centro è chiuso. Gestisce le tue pagine social, costruisce campagne marketing, analizza l’andamento delle vendite pubblicando per te report e statistiche, mentre tu ti dedichi ad altro"
  },
    {
    //image: "https://images.unsplash.com/photo-1540555700478-4be289fbecef?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=1080&fit=max",
    image: "/images/spa.jpg",
    //title: "Centri Benessere e SPA",
    title: "SPA",
    description: "Un Team IA dedicato al tuo Business 24 ore al giorno, 7 giorni su 7. Riceve le prenotazioni e dà le informazioni anche quando la SPA è chiusa. Gestisce le tue pagine social, costruisce campagne marketing, analizza l’andamento delle vendite pubblicando per te report e statistiche, mentre tu ti dedichi ad altro"
  },
    {
    //image: "https://images.unsplash.com/photo-1538655121994-d121b7c32095?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=1080&fit=max",
    image: "/images/parchi divertimento.jpg",
    title: "Parchi divertimento",
    description: "Un Team IA dedicato al tuo Business 24 ore al giorno, 7 giorni su 7. Riceve le prenotazioni e dà le informazioni anche quando il parco è chiuso. Gestisce le pagine social, costruisce campagne marketing, analizza l’andamento delle vendite pubblicando per te report e statistiche, mentre tu ti dedichi ad altro"
  },
  {
    //image: "https://images.unsplash.com/photo-1544148103-0773bf10d330?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=1080&fit=max",
    image: "/images/lounge_bar.jpg",
    title: "Lounge Bar",
    description: "Tu, dedicati ai clienti, mentre il tuo team IA dedicato pensa al tuo business.Riceve le prenotazioni e dà le informazioni anche di notte, sempre. Gestisce le tue pagine social, costruisce campagne marketing, analizza l’andamento delle vendite pubblicando per te report e statistiche, mentre tu ti dedichi ad altro."

  },
  {
   // image: "https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=1080&fit=max",
    image: "/images/coworking.avif",
    title: "Coworking",
    description: "Un Team IA dedicato al tuo Business 24 ore al giorno, 7 giorni su 7. Riceve le prenotazioni e dà le informazioni anche di notte. Gestisce le tue pagine social, costruisce campagne marketing, analizza l’andamento delle vendite pubblicando per te report e statistiche, mentre tu ti dedichi ad altro"
  },
  {
    //image: "https://images.unsplash.com/photo-1563241527-3004b7be0ffd?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=1080&fit=max",
    image: "/images/fiorista.jpg",
    title: "Fiorista",
    description: "Un Team IA dedicato al tuo Business 24 ore al giorno, 7 giorni su 7. Riceve le prenotazioni e dà informazioni anche di notte. Gestisce le tue pagine social, costruisce campagne marketing, analizza l’andamento delle vendite pubblicando per te report e statistiche, mentre tu ti dedichi ad altro."
  },
 {
    image: "https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=1080&fit=max",
    //image: "",
    title: "Hotel & B&B",
    description: "Mai più stanze vuote.I clienti prenotano a qualsiasi ora del giorno e della notte, il tuo team  IA  dedicato,  dà informazioni, riceve le prenotazioni, gestisce le tue pagine social, costruisce campagne marketing, analizza l’andamento delle vendite pubblicando report e statistiche, mentre tu ti dedichi ad altro."

  },
   {
    image: "https://images.unsplash.com/photo-1507924538820-ede94a04019d?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=1080&fit=max",
    //image: "",
    title: "Teatri",
    description: "Posti invenduti?Un Team IA dedicato al tuo Business 24 ore al giorno, 7 giorni su 7. Riceve le prenotazioni e dà le informazioni anche di notte. Gestisce le tue pagine social, costruisce campagne marketing, analizza l’andamento delle vendite pubblicando per te report e statistiche, mentre tu ti dedichi ad altro"

  },
 {
    //image: "https://images.unsplash.com/photo-1594909122845-11baa439b7bf?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=1080&fit=max",
    image: "/images/cinema.jpg",
    title: "Cinema",
    description: "Una sala sempre piena, con un Team IA dedicato al tuo Business 24 ore al giorno, 7 giorni su 7. Riceve le prenotazioni e dà le informazioni anche di notte. Gestisce le tue pagine social, costruisce campagne marketing, analizza l’andamento delle vendite pubblicando per te report e statistiche, mentre tu ti dedichi ad altro"
  }
 

];

const BusinessCarousel = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
      className="w-full mx-auto max-w-3xl"
    >
      <Carousel className="w-full">
        <CarouselContent>
          {businessSlides.map((slide, index) => (
            <CarouselItem key={index}>
              <div className="p-1">
                <div className="overflow-hidden rounded-xl shadow-xl">
                  <div className="relative">
                    <img
                      src={slide.image}
                      alt={slide.title}
                      className="w-full h-64 object-cover"
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-slate-600/80 backdrop-blur-sm p-4 text-white rounded-t-xl border border-white/20">
                      <h3 className="text-xl font-semibold mb-1">{slide.title}</h3>
                      <p className="text-sm text-gray-200">{slide.description}</p>
                    </div>
                  </div>
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="left-1" />
        <CarouselNext className="right-1" />
      </Carousel>
    </motion.div>
  );
};

export default BusinessCarousel;
