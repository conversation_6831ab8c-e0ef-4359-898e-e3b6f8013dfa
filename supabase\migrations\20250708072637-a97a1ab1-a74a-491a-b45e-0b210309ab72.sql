-- Fix RLS policy for email_templates to use security definer function instead of direct auth.users access

-- Drop existing policy
DROP POLICY IF EXISTS "<PERSON><PERSON> can manage email templates" ON public.email_templates;

-- Create new policy using the existing security definer function
CREATE POLICY "Ad<PERSON> can manage email templates" 
ON public.email_templates 
FOR ALL 
USING (
  public.get_user_role(auth.uid()) = 'admin'
);