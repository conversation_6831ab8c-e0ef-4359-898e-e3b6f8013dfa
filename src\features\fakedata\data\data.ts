

// Import composed business names
import { companyNamesByCategory } from './business_names';
// Import composed product names
import { productNamesByCategory } from './product_names';

// Dati italiani per generazione più realistica
export const italianData = {
  // Use the composed business names from the subfolder
  companyNamesByCategory,
  
  companySuffixes: [
    "S.r.l.",
    "S.p.A.",
    "& Figli",
    "di Mario Rossi",
    "Group",
    "S.n.c.",
    "e Figli",
    "Holding",
    "International",
    "Industries",
    "S.a.s.",
    "e Associati",
    "Partners",
    "Di Famiglia",
    "e C.",
    "Consortium",
    "Società Unipersonale",
    "S.r.l.s.",
    "Inc.",
    "Global",
  ],
  // Use the composed product names from the subfolder
  productNames: productNamesByCategory,
  
  streetNames: [
    "Via Roma",
    "Via Dante",
    "Via Garibaldi",
    "Via Mazzini",
    "Via Verdi",
    "Corso Italia",
    "Corso Europa",
    "Piazza Duomo",
    "Viale dei Giardini",
    "Via Leonardo da Vinci",
    "Via Montebello",
    "Via delle Rose",
    "Via Marconi",
    "Piazza San Marco",
    "Viale Vittorio Emanuele",
    "Corso Vittorio Emanuele II",
    "Via Andrea Solari",
    "Via Montenapoleone",
    "Via Torino",
    "Via Brera",
    "Via Paolo Sarpi",
    "Via Borgogna",
    "Corso Como",
    "Via della Spiga",
  ],
  firstNames: [
    "Andrea",
    "Marco",
    "Giuseppe",
    "Antonio",
    "Giovanni",
    "<PERSON>",
    "Luigi",
    "Francesco",
    "Roberto",
    "Paolo",
    "Alessio",
    "Stefano",
    "Davide",
    "Michele",
    "Carlo",
    "Alessandro",
    "Leonardo",
    "Massimo",
    "Vittorio",
    "Riccardo",
  ],
  lastNames: [
    "Rossi",
    "Bianchi",
    "Ferrari",
    "Esposito",
    "Romano",
    "Colombo",
    "Ricci",
    "Marino",
    "Greco",
    "Bruno",
    "Gallo",
    "Conti",
    "De Luca",
    "Costa",
    "Giordano",
    "Mancini",
    "Rizzo",
    "Lombardi",
    "Moretti",
    "Barbieri",
    "Fontana",
    "Santoro",
  ],
  zipCodes: {
    Milano: [
      "20121",
      "20122",
      "20123",
      "20124",
      "20125",
      "20144",
      "20146",
      "20154",
    ],
    Roma: [
      "00100",
      "00118",
      "00121",
      "00122",
      "00123",
      "00125",
      "00126",
      "00132",
    ],
    Napoli: [
      "80121",
      "80122",
      "80123",
      "80124",
      "80125",
      "80126",
      "80127",
      "80128",
    ],
    Firenze: [
      "50121",
      "50122",
      "50123",
      "50124",
      "50125",
      "50126",
      "50127",
      "50128",
    ],
    Torino: [
      "10121",
      "10122",
      "10123",
      "10124",
      "10125",
      "10126",
      "10127",
      "10128",
    ],
    Bologna: [
      "40121",
      "40122",
      "40123",
      "40124",
      "40125",
      "40126",
      "40127",
      "40128",
    ],
    Venezia: [
      "30121",
      "30122",
      "30123",
      "30124",
      "30125",
      "30126",
      "30127",
      "30128",
    ],
  },
  provinces: {
    Milano: "MI",
    Roma: "RM",
    Napoli: "NA",
    Firenze: "FI",
    Torino: "TO",
    Bologna: "BO",
    Venezia: "VE",
  },
  domains: ["it", "com", "net", "org"],
  phoneFormats: [
    "+39 ### ### ####",
    "+39 ## ## ## ##",
    "### ### ####",
    "## ## ## ##",
  ],
  // Pricing ranges by category (min, max in euros)
  categoryPricing: {
    bar: { min: 8, max: 35 },              // Caffè, aperitivi, snack
    ristorante: { min: 25, max: 120 },     // Menu, pizze, piatti
    alimentari: { min: 15, max: 80 },      // Prodotti alimentari, spesa
    bellezza: { min: 20, max: 150 },       // Servizi parrucchiere/estetica
    abbigliamento: { min: 30, max: 250 },  // Vestiti, accessori
    palestra: { min: 25, max: 100 },       // Abbonamenti, corsi
    spa: { min: 40, max: 180 },            // Trattamenti benessere
    cinema: { min: 8, max: 18 },           // Biglietti cinema
    fioraio: { min: 15, max: 85 },         // Bouquet, composizioni
    hotel: { min: 60, max: 350 },          // Camere, servizi hotel
    teatro: { min: 20, max: 120 },         // Biglietti spettacoli
    aperitivo: { min: 12, max: 35 },       // Cocktail, aperitivi
    default: { min: 20, max: 100 }         // Fallback generico
  },

  catchPhrases: [
    "La qualità che merita",
    "Tradizione e innovazione",
    "Il gusto autentico italiano",
    "Dal 1985 al vostro servizio",
    "I sapori della tradizione",
    "L'eccellenza del made in Italy",
    "Artigianalità e passione",
    "Il meglio per i nostri clienti",
  ],
};

//