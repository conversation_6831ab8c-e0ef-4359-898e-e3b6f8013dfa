
import { useState } from 'react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { GitHubIntegration, GitHubRepository, GitHubRepoData } from '@/types/github';

export const useGitHubIntegration = () => {
  const [loading, setLoading] = useState(false);

  const initiateGitHubOAuth = async (businessId: string) => {
    setLoading(true);
    try {
      // GitHub OAuth configuration
      const clientId = import.meta.env.VITE_GITHUB_CLIENT_ID;
      const redirectUri = `${window.location.origin}/integrations/github/callback`;
      const scope = 'repo,read:user,read:org';
      const state = btoa(JSON.stringify({ businessId }));

      const githubAuthUrl = `https://github.com/login/oauth/authorize?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${scope}&state=${state}`;
      
      window.location.href = githubAuthUrl;
    } catch (error) {
      console.error('Error initiating GitHub OAuth:', error);
      toast.error('Errore durante l\'avvio dell\'autorizzazione GitHub');
    } finally {
      setLoading(false);
    }
  };

  const getGitHubIntegration = async (businessId: string): Promise<GitHubIntegration | null> => {
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) return null;

      const { data, error } = await supabase
        .from('github_integrations')
        .select('*')
        .eq('user_id', user.user.id)
        .eq('business_id', businessId)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching GitHub integration:', error);
        return null;
      }

      return data as GitHubIntegration | null;
    } catch (error) {
      console.error('Error fetching GitHub integration:', error);
      return null;
    }
  };

  const getSelectedRepositories = async (integrationId: string): Promise<GitHubRepository[]> => {
    try {
      const { data, error } = await supabase
        .from('github_repositories')
        .select('*')
        .eq('integration_id', integrationId)
        .order('name');

      if (error) {
        console.error('Error fetching repositories:', error);
        return [];
      }

      return data as GitHubRepository[];
    } catch (error) {
      console.error('Error fetching repositories:', error);
      return [];
    }
  };

  const updateRepositorySelection = async (
    integrationId: string,
    accessType: 'all' | 'selected',
    selectedRepos: GitHubRepoData[] = []
  ) => {
    setLoading(true);
    try {
      // Update integration access type
      const { error: updateError } = await supabase
        .from('github_integrations')
        .update({ repository_access_type: accessType })
        .eq('id', integrationId);

      if (updateError) throw updateError;

      // Clear existing repository selections
      const { error: deleteError } = await supabase
        .from('github_repositories')
        .delete()
        .eq('integration_id', integrationId);

      if (deleteError) throw deleteError;

      // Add new selections if selective access
      if (accessType === 'selected' && selectedRepos.length > 0) {
        const repoData = selectedRepos.map(repo => ({
          integration_id: integrationId,
          github_repo_id: repo.id,
          name: repo.name,
          full_name: repo.full_name,
          owner_login: repo.owner.login,
          owner_type: repo.owner.type,
          is_private: repo.private,
          clone_url: repo.clone_url,
          ssh_url: repo.ssh_url,
          default_branch: repo.default_branch,
        }));

        const { error: insertError } = await supabase
          .from('github_repositories')
          .insert(repoData);

        if (insertError) throw insertError;
      }

      toast.success('Selezione repository aggiornata con successo');
      return true;
    } catch (error) {
      console.error('Error updating repository selection:', error);
      toast.error('Errore durante l\'aggiornamento della selezione');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const revokeGitHubIntegration = async (integrationId: string) => {
    setLoading(true);
    try {
      const { error } = await supabase
        .from('github_integrations')
        .update({ is_active: false })
        .eq('id', integrationId);

      if (error) throw error;

      toast.success('Integrazione GitHub revocata con successo');
      return true;
    } catch (error) {
      console.error('Error revoking GitHub integration:', error);
      toast.error('Errore durante la revoca dell\'integrazione');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    initiateGitHubOAuth,
    getGitHubIntegration,
    getSelectedRepositories,
    updateRepositorySelection,
    revokeGitHubIntegration,
  };
};
