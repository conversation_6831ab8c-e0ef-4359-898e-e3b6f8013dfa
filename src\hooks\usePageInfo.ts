import { useLocation } from 'react-router-dom';
import { useBusinessStore } from '@/store/businessStore';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface PageInfo {
  businessName: string | null;
  pageName: string;
  pageUrl: string;
  subPageName?: string;
  subPageUrl?: string;
}

/**
 * Custom hook to get information about the current page and selected business
 * @returns Object containing businessName, pageName, and pageUrl
 */
export const usePageInfo = (): PageInfo => {
  const location = useLocation();
  const selectedBusiness = useBusinessStore((state) => state.selectedBusiness);

  // Map of paths to page names
  const pathToPageName: Record<string, string> = {
    '/dashboard': 'Dashboard',
    '/calendar': 'Calendario',
    '/deals': 'Offerte',
    '/deals/new': 'Nuova Offerta',
    '/documents': 'Documenti',
    '/pricing': 'Gestione Prezzi',
    '/layout': 'Layout',
    '/settings': 'Impostazioni',
    '/usermanager': 'Gestione Utenti',
    '/integrations': 'Integrazioni',
  };

  // Extract the base path and check if it's a detail page
  const pathInfo = (() => {
    const path = location.pathname;

    // Special case for /deals/new which should be treated as its own page
    if (path === '/deals/new') {
      return { basePath: path, isDetailPage: false };
    }

    // For paths like /deals/123, it's a detail page
    const segments = path.split('/');
    if (segments.length > 2 && segments[1] === 'deals' && segments[2] !== 'new') {
      return {
        basePath: `/${segments[1]}`,
        isDetailPage: true,
        detailId: segments[2],
        detailType: segments[1] // 'deals'
      };
    }

    return { basePath: path, isDetailPage: false };
  })();

  // Fetch detail information if needed
  const { data: detailData } = useQuery({
    queryKey: ['detail', pathInfo.detailType, pathInfo.detailId],
    queryFn: async () => {
      if (!pathInfo.isDetailPage) return null;

      if (pathInfo.detailType === 'deals') {
        const { data, error } = await supabase
          .from('deals')
          .select('title')
          .eq('id', pathInfo.detailId)
          .single();

        if (error) throw error;
        return data;
      }

      return null;
    },
    enabled: pathInfo.isDetailPage,
  });

  const pageName = pathToPageName[pathInfo.basePath] || 'Pagina non trovata';

  const result: PageInfo = {
    businessName: selectedBusiness?.name || null,
    pageName,
    pageUrl: pathInfo.basePath,
  };

  // Add subpage info if it's a detail page
  if (pathInfo.isDetailPage && detailData) {
    if (pathInfo.detailType === 'deals') {
      result.subPageName = detailData.title;
      result.subPageUrl = location.pathname;
    }
  }

  return result;
};
