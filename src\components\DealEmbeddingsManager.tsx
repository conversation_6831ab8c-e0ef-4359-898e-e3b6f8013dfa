import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Database, Sparkles, Trash2 } from 'lucide-react';

export const DealEmbeddingsManager: React.FC = () => {
  const [isIndexing, setIsIndexing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();

  const handleBatchIndex = async () => {
    setIsIndexing(true);
    try {
      const { data, error } = await supabase.functions.invoke('semantic-search', {
        body: { action: 'batch_index' }
      });

      if (error) throw error;

      toast({
        title: "Successo!",
        description: `Indicizzati ${data.indexed} deals con embeddings`,
      });
    } catch (error) {
      console.error('Error indexing deals:', error);
      toast({
        title: "Errore",
        description: "Errore durante l'indicizzazione dei deals",
        variant: "destructive",
      });
    } finally {
      setIsIndexing(false);
    }
  };

  const handleTestSearch = async () => {
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    try {
      const { data, error } = await supabase.functions.invoke('semantic-search', {
        body: { 
          action: 'search',
          query: searchQuery,
          filters: {}
        }
      });

      if (error) throw error;

      setSearchResults(data.deals || []);
      toast({
        title: "Ricerca completata",
        description: `Trovati ${data.deals?.length || 0} risultati`,
      });
    } catch (error) {
      console.error('Error searching deals:', error);
      toast({
        title: "Errore",
        description: "Errore durante la ricerca semantica",
        variant: "destructive",
      });
    } finally {
      setIsSearching(false);
    }
  };

  const handleDeleteAll = async () => {
    setIsDeleting(true);
    try {
      const { data, error } = await supabase.functions.invoke('semantic-search', {
        body: { action: 'delete_all' }
      });

      if (error) throw error;

      toast({
        title: "Successo!",
        description: "Tutti gli embeddings sono stati eliminati",
      });

      // Clear search results if any
      setSearchResults([]);
    } catch (error) {
      console.error('Error deleting embeddings:', error);
      toast({
        title: "Errore",
        description: "Errore durante l'eliminazione degli embeddings",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Gestione Embeddings Deals
          </CardTitle>
          <CardDescription>
            Indicizza i deals esistenti per abilitare la ricerca semantica e i piani multi-servizio
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button 
              onClick={handleBatchIndex} 
              disabled={isIndexing || isDeleting}
              className="w-full"
            >
              {isIndexing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Indicizzazione in corso...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  Indicizza tutti i deals
                </>
              )}
            </Button>

            <Button 
              onClick={handleDeleteAll} 
              disabled={isIndexing || isDeleting}
              variant="destructive"
              className="w-full"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Eliminazione in corso...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Elimina tutti gli embeddings
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Test Ricerca Semantica</CardTitle>
          <CardDescription>
            Testa la ricerca semantica sui deals indicizzati
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="es. 'massaggio rilassante', 'cena romantica', 'attività sportive'"
              className="flex-1 px-3 py-2 border rounded-md"
              onKeyPress={(e) => e.key === 'Enter' && handleTestSearch()}
            />
            <Button 
              onClick={handleTestSearch} 
              disabled={isSearching || !searchQuery.trim()}
            >
              {isSearching ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                'Cerca'
              )}
            </Button>
          </div>

          {searchResults.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium">Risultati ({searchResults.length}):</h4>
              <div className="max-h-60 overflow-y-auto space-y-2">
                {searchResults.map((deal, index) => (
                  <div key={index} className="p-3 border rounded-md bg-muted/50">
                    <div className="font-medium">{deal.title}</div>
                    <div className="text-sm text-muted-foreground">
                      {deal.business_name} • {deal.business_city}
                    </div>
                    <div className="text-sm">
                      €{deal.discounted_price} (sconto da €{deal.original_price})
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Similarità: {(deal.similarity * 100).toFixed(1)}%
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};