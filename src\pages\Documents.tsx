import { useState, useEffect, useRef } from 'react';
import { useAuth } from "@/contexts/AuthContext";
import { useBusinessStore } from "@/store/businessStore";
import { supabase } from "@/integrations/supabase/client";
import MainLayout from "@/layouts/MainLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { FileIcon, Trash2, Upload, File, Eye, Info, MessageSquare, X, Send, ChevronLeft, ChevronRight, Copy, ChevronUp, ChevronDown, RotateCcw } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Spinner } from "@/components/ui/spinner";
import { AlertDialog, AlertDialogTrigger, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogCancel, AlertDialogAction } from "@/components/ui/alert-dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { n8n_rag_create, n8n_rag_delete, n8n_rag_query } from '@/settings/n8n';

interface DocumentFile {
  id: string;
  name: string;
  file_url: string;
  file_type: string;
  file_size: number;
  created_at: string;
}

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

const Documents = () => {

  const { user } = useAuth();
  const selectedBusiness = useBusinessStore(state => state.selectedBusiness);
  const { toast } = useToast();
  
  const [documents, setDocuments] = useState<DocumentFile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUploading, setIsUploading] = useState(false); // Fixed: Changed from circular reference to proper initialization
  const [isRagProcessing, setIsRagProcessing] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<DocumentFile | null>(null);
  const [viewDocLoading, setViewDocLoading] = useState<{[key: string]: boolean}>({});
  
  // Chat state
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const STORAGE_BUCKET = 'business_documents';
  
  const [isCollapsed, setIsCollapsed] = useState(false);
  
  useEffect(() => {
    if (selectedBusiness) {
      fetchDocuments();
    }
  }, [selectedBusiness]);
  
  useEffect(() => {
    // Scroll to bottom when messages change
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Chat functionality
  const toggleChat = () => {
    setIsChatOpen(!isChatOpen);
  };

  const resetChat = () => {
    setMessages([]);
    setCurrentMessage('');
    toast({
      title: "Chat resettato",
      description: "La conversazione è stata azzerata",
      duration: 2000,
    });
  };
  
  const handleSendMessage = async () => {
    if (!currentMessage.trim() || !selectedBusiness) return;
    
    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: currentMessage,
      timestamp: new Date()
    };
    
    setMessages([...messages, userMessage]);
    setCurrentMessage('');
    setIsProcessing(true);
    
    try {
      // Call RAG query API
      const response = await fetch(n8n_rag_query, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: userMessage.content,
          business_id: selectedBusiness.id,
          user_id: user?.id
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to query RAG system');
      }
      
      const responseData = await response.json();
      
      // Add AI response message
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: responseData[0].output || "Non sono riuscito a trovare una risposta nei documenti.",
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error querying RAG:', error);
      
      // Add error message
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: "Mi dispiace, si è verificato un errore durante l'elaborazione della tua richiesta. Riprova più tardi.",
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };
  
  const fetchDocuments = async () => {
    if (!selectedBusiness || !user) return;
    
    setIsLoading(true);
    
    try {
      const { data, error } = await supabase
        .from('documents')
        .select('*')
        .eq('business_id', selectedBusiness.id)
        .order('created_at', { ascending: false });
        
      if (error) {
        throw error;
      }
      
      setDocuments(data || []);
    } catch (error) {
      console.error("Error fetching documents:", error);
      toast({
        title: "Errore",
        description: "Impossibile caricare i documenti.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  const generateOneHourSignedUrl = async (document: DocumentFile): Promise<string | null> => {
    try {
      const pathRegex = new RegExp(`${STORAGE_BUCKET}/([^?#]+)`);
      const matches = document.file_url.match(pathRegex);
      
      if (matches && matches[1]) {
        const filePath = matches[1];
        
        const { data, error } = await supabase.storage
          .from(STORAGE_BUCKET)
          .createSignedUrl(filePath, 3600); // 60s*60m
          
        if (error) {
          console.error("Errore nella creazione dell'URL firmato:", error);
          return null;
        }
        
        return data?.signedUrl || null;
      }
      
      return null;
    } catch (error) {
      console.error("Errore nella generazione dell'URL firmato:", error);
      return null;
    }
  };
  
  const viewDocument = async (document: DocumentFile) => {
    try {
      setViewDocLoading(prev => ({ ...prev, [document.id]: true }));
      
      const signedUrl = await generateOneHourSignedUrl(document);
      
      if (!signedUrl) {
        throw new Error("Impossibile generare l'URL firmato per il documento.");
      }
      
      window.open(signedUrl, '_blank');
    } catch (error) {
      console.error("Errore nell'apertura del documento:", error);
      toast({
        title: "Errore",
        description: "Impossibile aprire il documento. Riprova più tardi.",
        variant: "destructive",
      });
    } finally {
      setViewDocLoading(prev => ({ ...prev, [document.id]: false }));
    }
  };
  
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null;
    console.log("File selezionato:", file);
    setSelectedFile(file);
  };
  
  const sanitizeFileName = (fileName: string): string => {
    return fileName
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-zA-Z0-9_.-]/g, '_')
      .replace(/_{2,}/g, '_');
  };
  
  const uploadDocument = async () => {
    console.log("Tentativo di caricamento del documento, file:", selectedFile);
    if (!selectedFile || !selectedBusiness || !user) {
      toast({
        title: "Errore",
        description: "Seleziona un file da caricare.",
        variant: "destructive",
      });
      return;
    }
    
    setIsUploading(true);
    
    try {
      const sanitizedFileName = sanitizeFileName(selectedFile.name);
      
      const timestamp = Date.now();
      const filePath = `${user.id}/${selectedBusiness.id}/${timestamp}_${sanitizedFileName}`;
      
      console.log(`Tentativo di caricamento del file a percorso: ${filePath}`);
      
      const { data: fileData, error: uploadError } = await supabase.storage
        .from(STORAGE_BUCKET)
        .upload(filePath, selectedFile, {
          cacheControl: '3600',
          upsert: false
        });
        
      if (uploadError) {
        console.error("Errore nel caricamento del file:", uploadError);
        throw uploadError;
      }
      
      console.log("File caricato con successo:", fileData);
      
      const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://pnrldflljkfxiefryvsu.supabase.co";
      const fileUrl = `${SUPABASE_URL}/storage/v1/object/public/${STORAGE_BUCKET}/${filePath}`;
      
      const { data: docData, error: insertError } = await supabase
        .from('documents')
        .insert({
          business_id: selectedBusiness.id,
          user_id: user.id,
          name: selectedFile.name,
          file_url: fileUrl,
          file_type: selectedFile.type,
          file_size: selectedFile.size
        })
        .select('*')
        .single();
        
      if (insertError) {
        console.error("Errore nell'inserimento del documento:", insertError);
        throw insertError;
      }
      
      console.log("Documento inserito nel database:", docData);

      // Call RAG creation API
      setIsRagProcessing(true);
      try {
        // Create a document object from docData for cleaner code
        const documentObj: DocumentFile = {
          id: docData.id,
          name: docData.name,
          file_url: docData.file_url,
          file_type: docData.file_type,
          file_size: docData.file_size,
          created_at: docData.created_at
        };
        
        // Generate signed URL with 1-hour expiration
        const signedUrl = await generateOneHourSignedUrl(documentObj);
        
        if (!signedUrl) {
          throw new Error("Impossibile generare l'URL firmato per il documento.");
        }
        
      
        const response = await fetch(n8n_rag_create, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            document: {
              id: documentObj.id,
              name: documentObj.name,
              file_url: documentObj.file_url,
              file_type: documentObj.file_type,
              file_size: documentObj.file_size,
              signed_url: signedUrl
            },
           
          })
        });

        if (!response.ok) {
          throw new Error('Failed to create RAG');
        }
        console.log('RAG creation initiated successfully',response);
      } catch (error) {
        console.error('Error creating RAG:', error);
        // Don't show error toast to user as this is a background process
      } finally {
        setIsRagProcessing(false);
      }
      
      toast({
        title: "Successo",
        description: "Documento caricato con successo.",
      });
      
      setSelectedFile(null);
      const fileInput = document.getElementById('document') as HTMLInputElement;
      if (fileInput) fileInput.value = '';
      
      fetchDocuments();
      
    } catch (error) {
      console.error("Error uploading document:", error);
      toast({
        title: "Errore",
        description: "Impossibile caricare il documento: " + (error.message || error.error_description || "errore sconosciuto"),
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };
  
  const deleteDocument = async (documentId: string, fileUrl: string) => {
    if (!user || !selectedBusiness) return;
    
    try {
      const { error: deleteError } = await supabase
        .from('documents')
        .delete()
        .eq('id', documentId);
        
      if (deleteError) {
        throw deleteError;
      }
      
      const pathRegex = new RegExp(`${STORAGE_BUCKET}/([^?#]+)`);
      const matches = fileUrl.match(pathRegex);
      
      if (matches && matches[1]) {
        const filePath = matches[1];
        console.log(`Tentativo di eliminazione del file: ${filePath}`);
        
        const { error: storageError } = await supabase.storage
          .from(STORAGE_BUCKET)
          .remove([filePath]);
          
        if (storageError) {
          console.error("Error deleting file from storage:", storageError);
        }
      }
     
      // Call RAG deletion API
      try {
        const response = await fetch(n8n_rag_delete, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
          
            document: {
              id: documentId
            }
          })
        });
        
        if (!response.ok) {
          console.error('Error deleting RAG:', await response.text());
        } else {
          console.log('RAG deletion successful');
        }
      } catch (error) {
        console.error('Error calling RAG deletion API:', error);
        // Don't show error toast to user as this is a background process
      }
      
      toast({
        title: "Successo",
        description: "Documento eliminato con successo.",
      });
      
      setDocuments(documents.filter(doc => doc.id !== documentId));
      
    } catch (error) {
      console.error("Error deleting document:", error);
      toast({
        title: "Errore",
        description: "Impossibile eliminare il documento.",
        variant: "destructive",
      });
    }
  };
  
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  const viewDocumentContent = (document: DocumentFile) => {
    setSelectedDocument(document);
  };
  
  return (
    <MainLayout>
      <div className="container mx-auto py-6 px-4 max-w-7xl">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Documenti</h1>
          {selectedBusiness && (
            <h2 className="text-lg text-gray-600">{selectedBusiness.name}</h2>
          )}
        </div>
        
        <div className="flex relative">
          <div className={`flex-1 transition-all duration-300 ${isChatOpen ? 'mr-[400px]' : ''}`}>
            {!selectedBusiness ? (
              <div className="text-center py-10">
                <p className="text-lg text-gray-600">Seleziona un'attività per gestire i documenti.</p>
              </div>
            ) : (
              <>
                <Card className="mb-8">
                  <CardHeader>
                    <CardTitle>Carica un nuovo documento</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <div className="flex-grow">
                        <Label htmlFor="document" className="mb-2 block">Seleziona un file</Label>
                        <Input 
                          id="document" 
                          type="file" 
                          onChange={handleFileChange} 
                          className="cursor-pointer"
                        />
                        {selectedFile && (
                          <p className="mt-2 text-sm text-gray-600">
                            File selezionato: {selectedFile.name} ({formatFileSize(selectedFile.size)})
                          </p>
                        )}
                      </div>
                      <div className="flex items-end">
                        <Button 
                          onClick={uploadDocument} 
                          disabled={isUploading || !selectedFile}
                          className="w-full sm:w-auto"
                        >
                          {isUploading ? <Spinner className="mr-2" size="sm" /> : <Upload className="mr-2 h-4 w-4" />}
                          Carica documento
                        </Button>
                      </div>
                    </div>
                    {isRagProcessing && (
                      <div className="mt-4 p-2 bg-yellow-50 border border-yellow-200 rounded-md flex items-center">
                        <Spinner className="mr-2" size="sm" />
                        <p className="text-sm text-yellow-800">RAG In progress...</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
                
                {isLoading ? (
                  <div className="flex justify-center py-10">
                    <Spinner size="lg" />
                  </div>
                ) : documents.length === 0 ? (
                  <div className="text-center py-10 bg-gray-50 rounded-lg border border-dashed">
                    <File className="mx-auto h-12 w-12 text-gray-400" />
                    <p className="mt-4 text-lg text-gray-600">Non hai ancora caricato nessun documento.</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {documents.map((document) => (
                      <Card key={document.id} className="overflow-hidden">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-lg truncate" title={document.name}>
                            {document.name}
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pb-2">
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <FileIcon className="h-4 w-4" />
                            <span>{document.file_type || 'Documento'}</span>
                            <span>•</span>
                            <span>{formatFileSize(document.file_size)}</span>
                          </div>
                          <p className="text-sm text-gray-500 mt-1">
                            Caricato: {new Date(document.created_at).toLocaleDateString('it-IT')}
                          </p>
                        </CardContent>
                        <CardFooter className="flex justify-between pt-2">
                          <div className="flex gap-4">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <button 
                                    className="text-gray-600 hover:text-primary transition-colors"
                                    onClick={() => viewDocument(document)}
                                    disabled={viewDocLoading[document.id]}
                                  >
                                    {viewDocLoading[document.id] ? (
                                      <Spinner size="sm" />
                                    ) : (
                                      <Eye className="h-5 w-5" />
                                    )}
                                  </button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Visualizza documento</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <button 
                                    className="text-gray-600 hover:text-primary transition-colors"
                                    onClick={() => viewDocumentContent(document)}
                                  >
                                    <Info className="h-5 w-5" />
                                  </button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Informazioni documento</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                          
                          <AlertDialog>
                            <TooltipProvider>
                              <Tooltip>
                                <AlertDialogTrigger asChild>
                                  <TooltipTrigger asChild>
                                    <button className="text-gray-600 hover:text-destructive transition-colors">
                                      <Trash2 className="h-5 w-5" />
                                    </button>
                                  </TooltipTrigger>
                                </AlertDialogTrigger>
                                <TooltipContent>
                                  <p>Elimina documento</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Sei sicuro?</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Questa azione non può essere annullata. Il documento verrà eliminato permanentemente.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Annulla</AlertDialogCancel>
                                <AlertDialogAction onClick={() => deleteDocument(document.id, document.file_url)}>
                                  Elimina
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                )}
              </>
            )}
          </div>
          
          {/* Chat toggle button */}
          <button 
            onClick={toggleChat}
            className="fixed right-0 top-1/2 transform -translate-y-1/2 bg-primary text-white p-2 rounded-l-md shadow-md z-10"
          >
            {isChatOpen ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
          </button>
          
          {/* Chat sidebar */}
          <div 
            className={`fixed top-16 right-0 bottom-0 w-[400px] bg-white shadow-lg z-20 transition-transform duration-300 transform ${
              isChatOpen ? 'translate-x-0' : 'translate-x-full'
            } flex flex-col`}
          >
            <div className="p-4 border-b flex justify-between items-center bg-primary text-white">
              <h2 className="font-bold text-lg flex items-center">
                <MessageSquare className="mr-2 h-5 w-5" />
                Chat con documenti
              </h2>
              <div className="flex items-center gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button 
                        onClick={resetChat}
                        className="p-1.5 hover:bg-primary-dark rounded-md transition-colors"
                      >
                        <RotateCcw className="h-4 w-4 text-white" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="left">
                      <p>Resetta chat</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button 
                        onClick={() => setIsCollapsed(!isCollapsed)}
                        className="p-1.5 hover:bg-primary-dark rounded-md transition-colors"
                      >
                        {isCollapsed ? (
                          <ChevronUp className="h-4 w-4 text-white" />
                        ) : (
                          <ChevronDown className="h-4 w-4 text-white" />
                        )}
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="left">
                      <p>{isCollapsed ? 'Espandi' : 'Comprimi'}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <button 
                  onClick={toggleChat} 
                  className="p-1.5 hover:bg-primary-dark rounded-md transition-colors"
                >
                  <X size={16} className="text-white" />
                </button>
              </div>
            </div>
            
            <div 
              className={`flex-1 overflow-y-auto p-4 bg-gray-50 transition-all duration-300 ${
                isCollapsed ? 'h-0 p-0 overflow-hidden' : ''
              }`}
            >
              {messages.length === 0 ? (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <MessageSquare className="mx-auto h-12 w-12 mb-2" />
                    <p>Inizia una conversazione sui tuoi documenti</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {messages.map((message) => (
                    <div 
                      key={message.id} 
                      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`flex max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse' : ''}`}>
                        <Avatar className={`h-8 w-8 ${message.role === 'user' ? 'ml-2' : 'mr-2'}`}>
                          {message.role === 'assistant' ? (
                            <AvatarImage src="/bot-avatar.png" />
                          ) : (
                            <AvatarImage src={user?.user_metadata?.avatar_url || ''} />
                          )}
                          <AvatarFallback>{message.role === 'user' ? 'U' : 'A'}</AvatarFallback>
                        </Avatar>
                        <div className="relative">
                          <div 
                            className={`p-3 rounded-lg ${
                              message.role === 'user' 
                                ? 'bg-primary text-white rounded-tr-none' 
                                : 'bg-gray-200 text-gray-800 rounded-tl-none'
                            }`}
                          >
                            {message.role === 'user' && (
                              <div className="absolute top-2 left-2 flex gap-1">
                                <button
                                  onClick={() => {
                                    setCurrentMessage(message.content);
                                    handleSendMessage();
                                  }}
                                  className="p-1 hover:bg-primary-dark rounded-sm transition-colors"
                                  title="Ripeti messaggio"
                                >
                                  <MessageSquare className="h-3 w-3 text-white opacity-70 hover:opacity-100" />
                                </button>
                                <button
                                  onClick={() => {
                                    navigator.clipboard.writeText(message.content);
                                    toast({
                                      title: "Copiato",
                                      description: "Messaggio copiato negli appunti",
                                      duration: 2000,
                                    });
                                  }}
                                  className="p-1 hover:bg-primary-dark rounded-sm transition-colors"
                                  title="Copia messaggio"
                                >
                                  <Copy className="h-3 w-3 text-white opacity-70 hover:opacity-100" />
                                </button>
                              </div>
                            )}
                            <p className="text-sm whitespace-pre-wrap pt-4">{message.content}</p>
                            <span className="text-xs opacity-70 block mt-1">
                              {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                  {isProcessing && (
                    <div className="flex justify-start">
                      <div className="flex">
                        <Avatar className="h-8 w-8 mr-2">
                          <AvatarImage src="/bot-avatar.png" />
                          <AvatarFallback>A</AvatarFallback>
                        </Avatar>
                        <div className="p-3 rounded-lg bg-gray-200 text-gray-800 rounded-tl-none">
                          <div className="flex space-x-2">
                            <div className="w-2 h-2 rounded-full bg-gray-600 animate-bounce" style={{ animationDelay: '0ms' }}></div>
                            <div className="w-2 h-2 rounded-full bg-gray-600 animate-bounce" style={{ animationDelay: '300ms' }}></div>
                            <div className="w-2 h-2 rounded-full bg-gray-600 animate-bounce" style={{ animationDelay: '600ms' }}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </div>
            
            <div className={`p-4 border-t transition-all duration-300 ${isCollapsed ? 'hidden' : ''}`}>
              <div className="flex items-end">
                <Textarea
                  placeholder="Fai una domanda sui tuoi documenti..."
                  value={currentMessage}
                  onChange={(e) => setCurrentMessage(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="flex-1 resize-none"
                  rows={2}
                />
                <Button 
                  onClick={handleSendMessage} 
                  disabled={!currentMessage.trim() || isProcessing}
                  className="ml-2" 
                  size="icon"
                >
                  <Send size={20} />
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                Utilizza questo chat per fare domande sui documenti caricati.
              </p>
            </div>
          </div>
        </div>
        
        <AlertDialog 
          open={selectedDocument !== null} 
          onOpenChange={(open) => !open && setSelectedDocument(null)}
        >
          <AlertDialogContent className="max-w-3xl">
            <AlertDialogHeader>
              <AlertDialogTitle className="truncate">
                {selectedDocument?.name}
              </AlertDialogTitle>
              <AlertDialogDescription>
                Informazioni sul documento
              </AlertDialogDescription>
            </AlertDialogHeader>
            
            <div className="mt-4 space-y-2">
              {selectedDocument && (
                <>
                  <p><strong>Nome:</strong> {selectedDocument.name}</p>
                  <p><strong>Tipo:</strong> {selectedDocument.file_type || 'Non specificato'}</p>
                  <p><strong>Dimensione:</strong> {formatFileSize(selectedDocument.file_size)}</p>
                  <p><strong>Data caricamento:</strong> {new Date(selectedDocument.created_at).toLocaleString('it-IT')}</p>
                </>
              )}
            </div>
            
            <AlertDialogFooter>
              <AlertDialogCancel>Chiudi</AlertDialogCancel>
              {selectedDocument && (
                <Button 
                  variant="default"
                  onClick={() => viewDocument(selectedDocument)}
                  disabled={viewDocLoading[selectedDocument.id]}
                >
                  {viewDocLoading[selectedDocument.id] ? (
                    <Spinner size="sm" className="mr-1" />
                  ) : null}
                  Visualizza documento
                </Button>
              )}
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </MainLayout>
  );
};

export default Documents;
