import { useState, useEffect } from "react";
import { Plus, Edit, Trash2, Eye } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import MainLayout from "@/layouts/MainLayout";
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";
import { useToast } from "@/hooks/use-toast";

type AgentDefinition = Database['public']['Tables']['ai_business_agents_profile']['Row'];
type AgentDefinitionInsert = Database['public']['Tables']['ai_business_agents_profile']['Insert'];
type AgentType = Database['public']['Enums']['agent_type'];

const AdminAgentDefinitions = () => {
  const [agents, setAgents] = useState<AgentDefinition[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<AgentDefinition | null>(null);
  const [formData, setFormData] = useState<Partial<AgentDefinitionInsert>>({
    name: '',
    description: '',
    avatar_url: '',
    assistant_id: '',
    voice_id: '',
    agent_type: 'booking'
  });
  const { toast } = useToast();

  useEffect(() => {
    fetchAgents();
  }, []);

  const fetchAgents = async () => {
    try {
      const { data, error } = await supabase
        .from('ai_business_agents_profile')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setAgents(data || []);
    } catch (error) {
      console.error('Error fetching agents:', error);
      toast({
        title: "Errore",
        description: "Impossibile caricare le definizioni degli agenti",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async () => {
    if (!formData.name || !formData.assistant_id || !formData.agent_type) {
      toast({
        title: "Errore",
        description: "Nome, ID Assistente e Tipo Agente sono obbligatori",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('ai_business_agents_profile')
        .insert([formData as AgentDefinitionInsert]);

      if (error) throw error;

      toast({
        title: "Successo",
        description: "Definizione agente creata con successo",
      });

      setIsCreateDialogOpen(false);
      setFormData({
        name: '',
        description: '',
        avatar_url: '',
        assistant_id: '',
        voice_id: '',
        agent_type: 'booking'
      });
      fetchAgents();
    } catch (error) {
      console.error('Error creating agent:', error);
      toast({
        title: "Errore",
        description: "Impossibile creare la definizione dell'agente",
        variant: "destructive",
      });
    }
  };

  const handleUpdate = async () => {
    if (!selectedAgent || !formData.name || !formData.assistant_id || !formData.agent_type) {
      toast({
        title: "Errore",
        description: "Nome, ID Assistente e Tipo Agente sono obbligatori",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('ai_business_agents_profile')
        .update({
          name: formData.name,
          description: formData.description,
          image_url: formData.avatar_url,
          assistant_id: formData.assistant_id,
          voice_id: formData.voice_id,
          agent_type: formData.agent_type,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedAgent.id);

      if (error) throw error;

      toast({
        title: "Successo",
        description: "Definizione agente aggiornata con successo",
      });

      setIsEditDialogOpen(false);
      setSelectedAgent(null);
      setFormData({
        name: '',
        description: '',
        avatar_url: '',
        assistant_id: '',
        voice_id: '',
        agent_type: 'booking'
      });
      fetchAgents();
    } catch (error) {
      console.error('Error updating agent:', error);
      toast({
        title: "Errore",
        description: "Impossibile aggiornare la definizione dell'agente",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (agent: AgentDefinition) => {
    if (!confirm(`Sei sicuro di voler eliminare la definizione "${agent.name}"?`)) {
      return;
    }

    try {
      const { error } = await supabase
        .from('ai_business_agents_profile')
        .delete()
        .eq('id', agent.id);

      if (error) throw error;

      toast({
        title: "Successo",
        description: "Definizione agente eliminata con successo",
      });

      fetchAgents();
    } catch (error) {
      console.error('Error deleting agent:', error);
      toast({
        title: "Errore",
        description: "Impossibile eliminare la definizione dell'agente",
        variant: "destructive",
      });
    }
  };

  const openEditDialog = (agent: AgentDefinition) => {
    setSelectedAgent(agent);
    setFormData({
      name: agent.name,
      description: agent.description,
      avatar_url: agent.avatar_url,
      assistant_id: agent.assistant_id,
      voice_id: agent.voice_id,
      agent_type: agent.agent_type
    });
    setIsEditDialogOpen(true);
  };

  const openViewDialog = (agent: AgentDefinition) => {
    setSelectedAgent(agent);
    setIsViewDialogOpen(true);
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          <div className="animate-pulse">
            <div className="h-8 w-64 bg-gray-300 rounded mb-2"></div>
            <div className="h-4 w-96 bg-gray-300 rounded mb-8"></div>
            <div className="h-64 bg-gray-300 rounded"></div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Definizioni Agenti AI</h1>
            <p className="text-gray-600">Gestisci le definizioni degli agenti AI disponibili nel sistema</p>
          </div>
          
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Nuova Definizione
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Crea Nuova Definizione Agente</DialogTitle>
                <DialogDescription>
                  Aggiungi una nuova definizione di agente AI al sistema
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="create-name">Nome *</Label>
                  <Input
                    id="create-name"
                    value={formData.name || ''}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="Nome dell'agente"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="create-agent-type">Tipo Agente *</Label>
                  <Select
                    value={formData.agent_type}
                    onValueChange={(value: AgentType) => setFormData({ ...formData, agent_type: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleziona tipo agente" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="booking">Prenotazioni</SelectItem>
                      <SelectItem value="customer_support">Supporto Clienti</SelectItem>
                      <SelectItem value="sales">Vendite</SelectItem>
                      <SelectItem value="marketing">Marketing</SelectItem>
                      <SelectItem value="data_analysis">Analisi Dati</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="create-description">Descrizione</Label>
                  <Textarea
                    id="create-description"
                    value={formData.description || ''}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Descrizione dell'agente"
                    rows={3}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="create-assistant-id">ID Assistente *</Label>
                  <Input
                    id="create-assistant-id"
                    value={formData.assistant_id || ''}
                    onChange={(e) => setFormData({ ...formData, assistant_id: e.target.value })}
                    placeholder="ID dell'assistente"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="create-voice-id">ID Voce</Label>
                  <Input
                    id="create-voice-id"
                    value={formData.voice_id || ''}
                    onChange={(e) => setFormData({ ...formData, voice_id: e.target.value })}
                    placeholder="ID della voce"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="create-image-url">URL Immagine</Label>
                  <Input
                    id="create-image-url"
                    value={formData.avatar_url || ''}
                    onChange={(e) => setFormData({ ...formData, avatar_url: e.target.value })}
                    placeholder="URL dell'immagine dell'agente"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Annulla
                </Button>
                <Button onClick={handleCreate}>
                  Crea Definizione
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Definizioni Agenti</CardTitle>
            <CardDescription>
              Lista di tutte le definizioni di agenti AI disponibili
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Agente</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>ID Assistente</TableHead>
                  <TableHead>ID Voce</TableHead>
                  <TableHead>Creato</TableHead>
                  <TableHead className="text-right">Azioni</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {agents.map((agent) => (
                  <TableRow key={agent.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={agent.avatar_url || undefined} />
                          <AvatarFallback>
                            {agent.name.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{agent.name}</div>
                          {agent.description && (
                            <div className="text-sm text-gray-500 truncate max-w-xs">
                              {agent.description}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="default">
                        {agent.agent_type === 'booking' && 'Prenotazioni'}
                        {agent.agent_type === 'customer_support' && 'Supporto Clienti'}
                        {agent.agent_type === 'sales' && 'Vendite'}
                        {agent.agent_type === 'marketing' && 'Marketing'}
                        {agent.agent_type === 'data_analysis' && 'Analisi Dati'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary" className="font-mono text-xs">
                        {agent.assistant_id}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {agent.voice_id ? (
                        <Badge variant="outline" className="font-mono text-xs">
                          {agent.voice_id}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">—</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {new Date(agent.created_at).toLocaleDateString('it-IT')}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex gap-2 justify-end">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openViewDialog(agent)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openEditDialog(agent)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(agent)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {agents.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <p>Nessuna definizione di agente trovata</p>
                <p className="text-sm">Clicca "Nuova Definizione" per aggiungerne una</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Edit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Modifica Definizione Agente</DialogTitle>
              <DialogDescription>
                Modifica i dettagli della definizione dell'agente
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Nome *</Label>
                <Input
                  id="edit-name"
                  value={formData.name || ''}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Nome dell'agente"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-agent-type">Tipo Agente *</Label>
                <Select
                  value={formData.agent_type}
                  onValueChange={(value: AgentType) => setFormData({ ...formData, agent_type: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Seleziona tipo agente" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="booking">Prenotazioni</SelectItem>
                    <SelectItem value="customer_support">Supporto Clienti</SelectItem>
                    <SelectItem value="sales">Vendite</SelectItem>
                    <SelectItem value="marketing">Marketing</SelectItem>
                    <SelectItem value="data_analysis">Analisi Dati</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-description">Descrizione</Label>
                <Textarea
                  id="edit-description"
                  value={formData.description || ''}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Descrizione dell'agente"
                  rows={3}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-assistant-id">ID Assistente *</Label>
                <Input
                  id="edit-assistant-id"
                  value={formData.assistant_id || ''}
                  onChange={(e) => setFormData({ ...formData, assistant_id: e.target.value })}
                  placeholder="ID dell'assistente"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-voice-id">ID Voce</Label>
                <Input
                  id="edit-voice-id"
                  value={formData.voice_id || ''}
                  onChange={(e) => setFormData({ ...formData, voice_id: e.target.value })}
                  placeholder="ID della voce"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-image-url">URL Immagine</Label>
                <Input
                  id="edit-image-url"
                  value={formData.avatar_url || ''}
                  onChange={(e) => setFormData({ ...formData, avatar_url: e.target.value })}
                  placeholder="URL dell'immagine dell'agente"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Annulla
              </Button>
              <Button onClick={handleUpdate}>
                Salva Modifiche
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* View Dialog */}
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Dettagli Definizione Agente</DialogTitle>
            </DialogHeader>
            {selectedAgent && (
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={selectedAgent.avatar_url || undefined} />
                    <AvatarFallback className="text-lg">
                      {selectedAgent.name.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="text-lg font-semibold">{selectedAgent.name}</h3>
                    {selectedAgent.description && (
                      <p className="text-gray-600 text-sm">{selectedAgent.description}</p>
                    )}
                  </div>
                </div>
                <div className="space-y-3">
                  <div>
                    <Label className="text-sm font-medium">Tipo Agente</Label>
                    <p className="text-sm">
                      {selectedAgent.agent_type === 'booking' && 'Prenotazioni'}
                      {selectedAgent.agent_type === 'customer_support' && 'Supporto Clienti'}
                      {selectedAgent.agent_type === 'sales' && 'Vendite'}
                      {selectedAgent.agent_type === 'marketing' && 'Marketing'}
                      {selectedAgent.agent_type === 'data_analysis' && 'Analisi Dati'}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">ID Assistente</Label>
                    <p className="font-mono text-sm bg-gray-100 p-2 rounded">
                      {selectedAgent.assistant_id}
                    </p>
                  </div>
                  {selectedAgent.voice_id && (
                    <div>
                      <Label className="text-sm font-medium">ID Voce</Label>
                      <p className="font-mono text-sm bg-gray-100 p-2 rounded">
                        {selectedAgent.voice_id}
                      </p>
                    </div>
                  )}
                  <div>
                    <Label className="text-sm font-medium">Creato il</Label>
                    <p className="text-sm">
                      {new Date(selectedAgent.created_at).toLocaleString('it-IT')}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Ultimo aggiornamento</Label>
                    <p className="text-sm">
                      {new Date(selectedAgent.updated_at).toLocaleString('it-IT')}
                    </p>
                  </div>
                </div>
              </div>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
                Chiudi
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  );
};

export default AdminAgentDefinitions;