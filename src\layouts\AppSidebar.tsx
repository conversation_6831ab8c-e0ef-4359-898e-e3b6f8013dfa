import { useEffect, useState } from "react";
import {
  CalendarDays,
  DollarSign,
  List,
  SettingsIcon,
  FileText,
  Handshake,
  Zap,
  LayoutDashboard,
  Layout,
  Tags,
  Tag,
  Bot,
  CreditCard,
  Building2,
  Database,
  Mail,
  Palette,
  Users,
  MessageCircle,
  ChevronDown,
} from "lucide-react";
import { useBusinessStore } from "@/store/businessStore";
import { useAuth } from "@/contexts/AuthContext";
import { useUserStore } from "@/store/userStore";
import {
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";

import {
  Sidebar,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { BusinessSelector } from "@/components/sidebar/BusinessSelector";
import { UserProfile } from "@/components/sidebar/UserProfile";
import { useUserDetails } from "@/components/sidebar/hooks/useUserDetails";

import { adminItems, aiAgentsItems, items, leadsItems, categoriesItems } from "./menudata";

export function AppSidebar() {
  const { userDetails, isLoading: userLoading } = useUserDetails();
  const { userData } = useUserStore();
  const businesses = useBusinessStore((state) => state.businesses);
  const selectedBusiness = useBusinessStore((state) => state.selectedBusiness);
  const isLoading = useBusinessStore((state) => state.isLoading);
  const selectBusiness = useBusinessStore((state) => state.selectBusiness);
  const [isLeadsOpen, setIsLeadsOpen] = useState(false);
  const [isCategoriesOpen, setIsCategoriesOpen] = useState(false);
  // Add debug logging
  useEffect(() => {
    console.log("Current userDetails state:", userDetails);
    console.log("Current loading state:", userLoading);
    console.log("Current businesses state:", businesses);
    console.log("Current selectedBusiness state:", selectedBusiness);
  }, [userDetails, userLoading, businesses, selectedBusiness]);

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader>
        <BusinessSelector
          businesses={businesses}
          selectedBusiness={selectedBusiness}
          isLoading={isLoading}
          onSelectBusiness={selectBusiness}
        />
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Gestione</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {items.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <a href={item.url}>
                      <item.icon />
                      <span>{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>AI Voice Agents</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {aiAgentsItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <a href={item.url}>
                      <item.icon />
                      <span>{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        {userData?.role === "admin" && (
          <SidebarGroup>
            <SidebarGroupLabel>Admin</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem>
                  <Collapsible open={isLeadsOpen} onOpenChange={setIsLeadsOpen}>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuButton className="w-full">
                        <Users />
                        <span>Gestione Leads</span>
                        <ChevronDown className={`ml-auto h-4 w-4 transition-transform ${isLeadsOpen ? 'rotate-180' : ''}`} />
                      </SidebarMenuButton>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <SidebarMenu className="ml-4">
                        {leadsItems.map((item) => (
                          <SidebarMenuItem key={item.title}>
                            <SidebarMenuButton asChild>
                              <a href={item.url}>
                                <item.icon />
                                <span>{item.title}</span>
                              </a>
                            </SidebarMenuButton>
                          </SidebarMenuItem>
                        ))}
                      </SidebarMenu>
                    </CollapsibleContent>
                  </Collapsible>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <Collapsible open={isCategoriesOpen} onOpenChange={setIsCategoriesOpen}>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuButton className="w-full">
                        <Tags />
                        <span>Gestione Categorie</span>
                        <ChevronDown className={`ml-auto h-4 w-4 transition-transform ${isCategoriesOpen ? 'rotate-180' : ''}`} />
                      </SidebarMenuButton>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <SidebarMenu className="ml-4">
                        {categoriesItems.map((item) => (
                          <SidebarMenuItem key={item.title}>
                            <SidebarMenuButton asChild>
                              <a href={item.url}>
                                <item.icon />
                                <span>{item.title}</span>
                              </a>
                            </SidebarMenuButton>
                          </SidebarMenuItem>
                        ))}
                      </SidebarMenu>
                    </CollapsibleContent>
                  </Collapsible>
                </SidebarMenuItem>
                {adminItems.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild>
                      <a href={item.url}>
                        <item.icon />
                        <span>{item.title}</span>
                      </a>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}
      </SidebarContent>
      <SidebarFooter>
        <UserProfile userDetails={userDetails} isLoading={userLoading} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
