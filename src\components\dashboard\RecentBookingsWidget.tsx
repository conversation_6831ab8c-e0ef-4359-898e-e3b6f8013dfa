
import React, { useEffect } from 'react';
import { Widget as WidgetType, WidgetType as WidgetTypes } from '@/types/widget';
import WidgetComponent from '@/components/dashboard/Widget';
import { Calendar, TrendingUp, TrendingDown } from 'lucide-react';
import { formatNumberWithCommas } from '@/utils/formatters';
import { cn } from '@/lib/utils';
import { supabase } from '@/integrations/supabase/client';
import { useBusinessStore } from '@/store/businessStore';

interface RecentBookingsWidgetProps {
  widget: WidgetType;
  className?: string;
  onRemove?: () => void;
}

const RecentBookingsWidget: React.FC<RecentBookingsWidgetProps> = ({ widget, className, onRemove }) => {
  const { selectedBusiness } = useBusinessStore();

  useEffect(() => {
    // Funzione per verificare direttamente i dati delle prenotazioni recenti
    const checkRecentBookingsData = async () => {
      if (!selectedBusiness) return;
      
      console.log('RecentBookingsWidget - Verifico dati per business:', selectedBusiness.id);
      
      try {
        // Prima controlla le offerte disponibili
        const { data: deals, error: dealsError } = await supabase
          .from('deals')
          .select('id, title')
          .eq('business_id', selectedBusiness.id);
          
        if (dealsError) {
          console.error('Errore nel recupero delle offerte:', dealsError);
          return;
        }
        
        if (!deals || deals.length === 0) {
          console.log('Nessuna offerta trovata per il business:', selectedBusiness.id);
          return;
        }
        
        console.log('Offerte trovate:', deals.length, 'IDs:', deals.map(d => d.id));
        
        // Calcola le date per il periodo corrente (ultimi 30 giorni)
        const now = new Date();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(now.getDate() - 30);
        
        const sixtyDaysAgo = new Date();
        sixtyDaysAgo.setDate(now.getDate() - 60);
        
        // Formatta le date per la query SQL
        const nowFormatted = now.toISOString().split('T')[0];
        const thirtyDaysAgoFormatted = thirtyDaysAgo.toISOString().split('T')[0];
        const sixtyDaysAgoFormatted = sixtyDaysAgo.toISOString().split('T')[0];
        
        console.log('Date per il filtraggio:', {
          now: nowFormatted,
          thirtyDaysAgo: thirtyDaysAgoFormatted,
          sixtyDaysAgo: sixtyDaysAgoFormatted
        });
        
        // Ottieni le prenotazioni per il periodo corrente
        const { data: currentPeriodBookings, error: currentError } = await supabase
          .from('bookings')
          .select('id, deal_id, booking_date, created_at')
          .in('deal_id', deals.map(d => d.id))
          .gte('booking_date', thirtyDaysAgoFormatted)
          .lte('booking_date', nowFormatted);
          
        if (currentError) {
          console.error('Errore nel recupero delle prenotazioni per il periodo corrente:', currentError);
          return;
        }
        
        console.log('Prenotazioni periodo corrente (ultimi 30 giorni):', {
          count: currentPeriodBookings?.length || 0,
          bookings: currentPeriodBookings?.slice(0, 5).map(b => ({
            id: b.id,
            deal_id: b.deal_id,
            booking_date: b.booking_date,
            created_at: b.created_at
          }))
        });
        
        // Ottieni le prenotazioni per il periodo precedente
        const { data: previousPeriodBookings, error: previousError } = await supabase
          .from('bookings')
          .select('id, deal_id, booking_date, created_at')
          .in('deal_id', deals.map(d => d.id))
          .gte('booking_date', sixtyDaysAgoFormatted)
          .lt('booking_date', thirtyDaysAgoFormatted);
          
        if (previousError) {
          console.error('Errore nel recupero delle prenotazioni per il periodo precedente:', previousError);
          return;
        }
        
        console.log('Prenotazioni periodo precedente (60-30 giorni fa):', {
          count: previousPeriodBookings?.length || 0,
          bookings: previousPeriodBookings?.slice(0, 5).map(b => ({
            id: b.id,
            deal_id: b.deal_id,
            booking_date: b.booking_date,
            created_at: b.created_at
          }))
        });
        
        // Calcola la variazione percentuale
        const currentCount = currentPeriodBookings?.length || 0;
        const previousCount = previousPeriodBookings?.length || 0;
        
        let percentChange = 0;
        if (previousCount > 0) {
          percentChange = ((currentCount - previousCount) / previousCount) * 100;
        } else if (currentCount > 0) {
          percentChange = 100;
        }
        
        console.log('Calcolo variazione percentuale prenotazioni:', {
          currentCount,
          previousCount,
          percentChange
        });
        
      } catch (e) {
        console.error('Errore durante la verifica dei dati delle prenotazioni recenti:', e);
      }
    };
    
    checkRecentBookingsData();
  }, [selectedBusiness]);

  if (widget.type !== WidgetTypes.RECENT_BOOKINGS) {
    console.error('Tipo di widget errato passato a RecentBookingsWidget');
    return null;
  }

  const { count, percentChange, period } = widget.data;
  
  // Log per debug
  console.log('RecentBookingsWidget rendering:', { 
    count, 
    percentChange, 
    period, 
    isLoading: widget.isLoading,
    widgetData: widget.data,
    widgetId: widget.id
  });
  
  const isPositiveChange = percentChange === undefined ? undefined : percentChange >= 0;

  return (
    <WidgetComponent
      widget={widget}
      className={className}
      onRemove={onRemove}
      actionLabel="Visualizza prenotazioni"
      actionLink="/calendar"
    >
      <div className="h-full flex flex-col justify-between">
        <div className="mt-2">
          <div className="text-3xl font-bold text-right">
            {widget.isLoading ? (
              <div className="animate-pulse h-8 w-24 bg-gray-200 rounded"></div>
            ) : (
              formatNumberWithCommas(count)
            )}
          </div>
          <div className="text-sm text-muted-foreground mt-1 text-right">
            {widget.isLoading ? (
              <div className="animate-pulse h-3 w-32 bg-gray-200 rounded"></div>
            ) : (
              `Prenotazioni negli ultimi ${period || '30 giorni'}`
            )}
          </div>
        </div>

        {percentChange !== undefined && (
          <div className={cn(
            "flex items-center gap-1 mt-4 text-sm font-medium",
            isPositiveChange ? "text-green-600" : "text-red-600"
          )}>
            {isPositiveChange ? (
              <TrendingUp className="h-4 w-4" />
            ) : (
              <TrendingDown className="h-4 w-4" />
            )}
            <span>
              {isPositiveChange ? '+' : ''}{percentChange.toFixed(1)}% rispetto al periodo precedente
            </span>
          </div>
        )}
      </div>
    </WidgetComponent>
  );
};

export default RecentBookingsWidget;
