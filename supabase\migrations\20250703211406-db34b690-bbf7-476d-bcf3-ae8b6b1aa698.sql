-- Create basic subscriptions for all business owners and their default agents

-- First, create basic subscriptions for all businesses that don't have one yet
INSERT INTO public.business_subscriptions (business_id, tier_type, is_yearly, status, started_at, expires_at)
SELECT 
  b.id as business_id,
  'basic'::pricing_tier_type as tier_type,
  false as is_yearly,
  'active' as status,
  now() as started_at,
  now() + INTERVAL '1 month' as expires_at
FROM businesses b
WHERE b.owner_id IS NOT NULL  -- Only businesses with owners
  AND NOT EXISTS (
    SELECT 1 FROM business_subscriptions bs 
    WHERE bs.business_id = b.id
  );

-- Create default Basic tier agents (booking and customer_support) for each business
-- Booking agents
INSERT INTO public.business_ai_agents (
  business_id, 
  agent_type, 
  name, 
  description, 
  voice_id, 
  personality_style, 
  instructions, 
  is_active
)
SELECT 
  b.id as business_id,
  'booking'::agent_type as agent_type,
  'Assistente Prenotazioni ' || b.name as name,
  'Assistente AI specializzato nella gestione delle prenotazioni per ' || b.name as description,
  'alloy' as voice_id,  -- Default ElevenLabs voice
  'professionale e cortese' as personality_style,
  'Sei un assistente AI specializzato nelle prenotazioni. Aiuta i clienti a prenotare servizi, fornisci informazioni su disponibilità e prezzi, e gestisci le richieste con cortesia e professionalità.' as instructions,
  true as is_active
FROM businesses b
WHERE b.owner_id IS NOT NULL
  AND EXISTS (
    SELECT 1 FROM business_subscriptions bs 
    WHERE bs.business_id = b.id 
    AND bs.tier_type = 'basic'
  )
  AND NOT EXISTS (
    SELECT 1 FROM business_ai_agents ba 
    WHERE ba.business_id = b.id 
    AND ba.agent_type = 'booking'
  );

-- Customer Support agents
INSERT INTO public.business_ai_agents (
  business_id, 
  agent_type, 
  name, 
  description, 
  voice_id, 
  personality_style, 
  instructions, 
  is_active
)
SELECT 
  b.id as business_id,
  'customer_support'::agent_type as agent_type,
  'Supporto Clienti ' || b.name as name,
  'Assistente AI per il supporto clienti di ' || b.name as description,
  'nova' as voice_id,  -- Different voice for customer support
  'empatico e disponibile' as personality_style,
  'Sei un assistente AI per il supporto clienti. Risolvi problemi, rispondi a domande, gestisci reclami e fornisci assistenza con empatia e competenza. Il tuo obiettivo è la soddisfazione del cliente.' as instructions,
  true as is_active
FROM businesses b
WHERE b.owner_id IS NOT NULL
  AND EXISTS (
    SELECT 1 FROM business_subscriptions bs 
    WHERE bs.business_id = b.id 
    AND bs.tier_type = 'basic'
  )
  AND NOT EXISTS (
    SELECT 1 FROM business_ai_agents ba 
    WHERE ba.business_id = b.id 
    AND ba.agent_type = 'customer_support'
  );

-- Display summary of what was created
DO $$
DECLARE
  subscription_count INTEGER;
  booking_agent_count INTEGER;
  support_agent_count INTEGER;
BEGIN
  -- Count subscriptions created
  SELECT COUNT(*) INTO subscription_count
  FROM business_subscriptions 
  WHERE tier_type = 'basic' 
  AND created_at >= now() - INTERVAL '1 minute';
  
  -- Count booking agents created
  SELECT COUNT(*) INTO booking_agent_count
  FROM business_ai_agents 
  WHERE agent_type = 'booking' 
  AND created_at >= now() - INTERVAL '1 minute';
  
  -- Count support agents created  
  SELECT COUNT(*) INTO support_agent_count
  FROM business_ai_agents 
  WHERE agent_type = 'customer_support' 
  AND created_at >= now() - INTERVAL '1 minute';
  
  RAISE NOTICE 'Setup completato:';
  RAISE NOTICE '- % sottoscrizioni Basic create', subscription_count;
  RAISE NOTICE '- % agenti Prenotazioni creati', booking_agent_count;
  RAISE NOTICE '- % agenti Supporto Clienti creati', support_agent_count;
END $$;