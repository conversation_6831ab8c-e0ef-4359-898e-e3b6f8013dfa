import React from 'react';
import Widget from './Widget';
import { BookingsWidget as BookingsWidgetType } from '@/types/widget';
import { Calendar } from 'lucide-react';
import { Link } from 'react-router-dom';

interface BookingsWidgetProps {
  widget: BookingsWidgetType;
  className?: string;
  onRemove?: () => void;
}

const BookingsWidget: React.FC<BookingsWidgetProps> = React.memo(({ widget, className, onRemove }) => {
  return (
    <Widget
      widget={widget}
      className={className}
      actionLabel="Visualizza Calendario"
      actionLink="/calendar"
      onRemove={onRemove}
    >
      <div className="w-full py-2">
        <div className="flex flex-col items-center justify-center text-center">
          <span className="text-4xl font-bold text-primary-600">
            {widget.isLoading ? "-" : widget.data.count}
          </span>
          <span className="text-sm text-gray-500 mt-1">
            {widget.data.count === 1 ? "prenotazione" : "prenotazioni"} in arrivo
          </span>
        </div>
      </div>
    </Widget>
  );
});

export default BookingsWidget;
