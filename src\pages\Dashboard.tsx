import { Package } from "lucide-react";
import { Button } from "@/components/ui/button";
import MainLayout from "@/layouts/MainLayout";
import { useBusinessStore } from "@/store/businessStore";
import { useWidgetStore } from "@/store/widgetStore";
import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { debugDashboardMetrics } from "@/utils/dashboardDebug";
import WidgetFactory from "@/components/dashboard/WidgetFactory";
import AddWidgetButton from "@/components/dashboard/AddWidgetButton";
import { WidgetType } from "@/types/widget";
import { useFinancialWidgets } from "@/hooks/useFinancialWidgets";
import { useDashboardConfig } from "@/hooks/useDashboardConfig";
import ScrollToTop from "@/components/scrolltop/ScrollToTop";

const Dashboard = () => {
  const selectedBusiness = useBusinessStore(state => state.selectedBusiness);
  const [businessStats, setBusinessStats] = useState({
    dealsCount: 0,
    activeDealsCount: 0,
    expiredDealsCount: 0
  });
  const [bookingsStats, setBookingsStats] = useState({
    count: 0
  });
  const [clientsStats, setClientsStats] = useState({
    count: 0
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingBookings, setIsLoadingBookings] = useState(false);
  const [isLoadingClients, setIsLoadingClients] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Utilizza l'hook per aggiornare i widget finanziari
  const { financialMetrics } = useFinancialWidgets();
  
  // Utilizza l'hook per la gestione della configurazione dashboard
  const { isLoading: isLoadingConfig, isSaving } = useDashboardConfig();

  // Funzione per aggiornare manualmente le metriche
  const handleRefreshMetrics = async () => {
    if (!selectedBusiness) return;

    setIsRefreshing(true);
    try {
      // Ricarica i dati direttamente senza aggiornare la vista materializzata
      await fetchBusinessStats();

      // Debug delle metriche dirette
      if (selectedBusiness) {
        const metrics = await debugDashboardMetrics(selectedBusiness.id);
        console.log('Metriche dirette:', metrics);
        toast.success("Metriche aggiornate con successo");
      }
    } catch (error) {
      console.error("Errore nell'aggiornamento:", error);
      toast.error("Errore nell'aggiornamento delle metriche");
    } finally {
      setIsRefreshing(false);
    }
  };

  // Funzione per ottenere le statistiche dell'attività dalla vista materializzata
  const fetchBusinessStats = async () => {
    if (!selectedBusiness) return;

    setIsLoading(true);
    setIsLoadingBookings(true);
    setIsLoadingClients(true);

    try {
      console.log('Recupero statistiche da mv_business_availability per business ID:', selectedBusiness.id);
      
      // Utilizziamo la vista materializzata per ottenere tutte le statistiche in una sola query
      const { data, error } = await supabase
        .from('mv_business_availability')
        .select('deal_count, active_deals_count, expired_deals_count, booking_count, unique_clients_count')
        .eq('business_id', selectedBusiness.id)
        .single();

      if (error) {
        console.error('Errore nella query alla vista materializzata:', error);
        throw error;
      }

      console.log('Dati recuperati dalla vista materializzata:', data);

      // Aggiorniamo tutti i dati delle statistiche con i valori dalla vista materializzata
      setBusinessStats({
        dealsCount: data?.deal_count || 0,
        activeDealsCount: data?.active_deals_count || 0,
        expiredDealsCount: data?.expired_deals_count || 0
      });

      setBookingsStats({
        count: data?.booking_count || 0
      });

      // Se il conteggio dei clienti è zero ma ci sono prenotazioni, probabilmente c'è un errore
      // nella vista materializzata, quindi facciamo una query specifica
      if ((data?.unique_clients_count === 0 || data?.unique_clients_count === null) && data?.booking_count > 0) {
        console.log('Conteggio clienti è zero ma ci sono prenotazioni, utilizzo fallback...');
        await fetchClientsCountDirectly();
      } else {
        setClientsStats({
          count: data?.unique_clients_count || 0
        });
        setIsLoadingClients(false);
      }

    } catch (error) {
      console.error('Errore nel caricamento delle statistiche:', error);
      toast.error('Errore nel caricamento delle statistiche');

      // Fallback: proviamo ad ottenere i dati dalle tabelle originali
      console.log('Utilizzo fallback per il recupero delle statistiche...');
      fetchFallbackStats();
    } finally {
      setIsLoading(false);
      setIsLoadingBookings(false);
    }
  };

  // Nuova funzione per ottenere direttamente il conteggio dei clienti
  const fetchClientsCountDirectly = async () => {
    if (!selectedBusiness) return;

    try {
      // Prima otteniamo gli ID delle offerte dell'attività
      const { data: deals, error: dealsError } = await supabase
        .from('deals')
        .select('id')
        .eq('business_id', selectedBusiness.id);

      if (dealsError) throw dealsError;

      if (deals && deals.length > 0) {
        console.log('ID offerte recuperati per il calcolo diretto dei clienti:', deals.map(d => d.id));
        
        // Otteniamo gli utenti unici che hanno prenotazioni per queste offerte
        const { data: uniqueClients, error: clientsError } = await supabase
          .from('bookings')
          .select('user_id')
          .in('deal_id', deals.map(d => d.id))
          .not('user_id', 'is', null);

        if (clientsError) throw clientsError;

        // Contiamo gli utenti unici
        const uniqueClientIds = new Set();
        if (uniqueClients) {
          uniqueClients.forEach(booking => {
            if (booking.user_id) {
              uniqueClientIds.add(booking.user_id);
            }
          });
        }

        console.log('Clienti unici trovati con calcolo diretto:', uniqueClientIds.size);
        
        setClientsStats({
          count: uniqueClientIds.size
        });
      }
    } catch (error) {
      console.error('Errore nel recupero diretto dei clienti:', error);
    } finally {
      setIsLoadingClients(false);
    }
  };

  // Funzione di fallback che utilizza le query originali nel caso la vista materializzata non contenga dati
  const fetchFallbackStats = async () => {
    if (!selectedBusiness) return;

    try {
      // Ottieni il numero totale di offerte
      const { data: deals, error } = await supabase
        .from('deals')
        .select('id, end_date')
        .eq('business_id', selectedBusiness.id);

      if (error) throw error;

      // Calcola quante offerte sono attive e quante scadute
      const now = new Date();
      const active = deals.filter(deal => new Date(deal.end_date) >= now);
      const expired = deals.filter(deal => new Date(deal.end_date) < now);

      setBusinessStats({
        dealsCount: deals.length,
        activeDealsCount: active.length,
        expiredDealsCount: expired.length
      });

      // Ottieni il numero di prenotazioni
      if (deals.length > 0) {
        const { data: bookingsData, error: bookingsError } = await supabase
          .from('bookings')
          .select('id', { count: 'exact', head: true })
          .in('deal_id', deals.map(d => d.id));

        if (!bookingsError) {
          setBookingsStats({
            count: bookingsData?.length || 0
          });
        }

        // Ottieni il numero di clienti unici
        const { data: bookingsWithUsers, error: clientsError } = await supabase
          .from('bookings')
          .select('user_id')
          .in('deal_id', deals.map(d => d.id))
          .not('user_id', 'is', null);

        if (!clientsError && bookingsWithUsers) {
          // Conta i clienti unici utilizzando un Set
          const uniqueClients = new Set();
          bookingsWithUsers.forEach(booking => {
            if (booking.user_id) {
              uniqueClients.add(booking.user_id);
            }
          });

          setClientsStats({
            count: uniqueClients.size
          });
        }
      }

    } catch (error) {
      console.error('Errore nel fallback delle statistiche:', error);
      toast.error('Errore nel caricamento delle statistiche');
    }
  };

  // Carica le statistiche quando cambia il business selezionato
  useEffect(() => {
    if (selectedBusiness) {
      console.log('Caricamento statistiche per business:', selectedBusiness.id);
      fetchBusinessStats();
    } else {
      // Reset delle statistiche quando non c'è un business selezionato
      setBusinessStats({ dealsCount: 0, activeDealsCount: 0, expiredDealsCount: 0 });
      setBookingsStats({ count: 0 });
      setClientsStats({ count: 0 });
    }
  }, [selectedBusiness]);

  // Ottieni i widget dalla dashboard configuration
  const { dashboardConfig, updateWidgetData, setWidgetLoading, removeWidget } = useWidgetStore();

  // Aggiorna i dati dei widget quando cambiano le statistiche
  useEffect(() => {
    // Evita di eseguire l'effetto se non c'è un business selezionato
    if (!selectedBusiness) return;

    // Memorizza gli ID dei widget per evitare di cercarli ad ogni render
    const bookingsWidgetId = dashboardConfig.widgets.find(w => w.type === WidgetType.BOOKINGS)?.id;
    const dealsWidgetId = dashboardConfig.widgets.find(w => w.type === WidgetType.DEALS)?.id;
    const clientsWidgetId = dashboardConfig.widgets.find(w => w.type === WidgetType.CLIENTS)?.id;

    // Aggiorna il widget delle prenotazioni
    if (bookingsWidgetId) {
      setWidgetLoading(bookingsWidgetId, isLoadingBookings);
      if (!isLoadingBookings) {
        updateWidgetData(bookingsWidgetId, { count: bookingsStats.count });
      }
    }

    // Aggiorna il widget delle offerte
    if (dealsWidgetId) {
      setWidgetLoading(dealsWidgetId, isLoading);
      if (!isLoading) {
        updateWidgetData(dealsWidgetId, {
          dealsCount: businessStats.dealsCount,
          activeDealsCount: businessStats.activeDealsCount,
          expiredDealsCount: businessStats.expiredDealsCount
        });
      }
    }

    // Aggiorna il widget dei clienti
    if (clientsWidgetId) {
      setWidgetLoading(clientsWidgetId, isLoadingClients);
      if (!isLoadingClients) {
        updateWidgetData(clientsWidgetId, { count: clientsStats.count });
      }
    }

    // Rimuoviamo dashboardConfig.widgets dalle dipendenze per evitare loop infiniti
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [businessStats, bookingsStats, clientsStats, isLoading, isLoadingBookings, isLoadingClients, selectedBusiness, updateWidgetData, setWidgetLoading]);

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-1">Gestisci le tue prenotazioni e disponibilità</p>
            {isSaving && <p className="text-xs text-blue-600 mt-1">Salvataggio in corso...</p>}
          </div>
          <div className="flex gap-3">
            <Button
              onClick={handleRefreshMetrics}
              variant="outline"
              disabled={isRefreshing || !selectedBusiness}
              className="flex items-center gap-2 h-10"
            >
              <span>{isRefreshing ? 'Aggiornamento...' : 'Aggiorna Metriche'}</span>
            </Button>
            <AddWidgetButton />
          </div>
        </div>

        {!selectedBusiness ? (
          <div className="flex flex-col items-center justify-center py-16 text-gray-500 bg-gray-50 rounded-lg border border-gray-200">
            <Package className="h-12 w-12 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium mb-2">Nessuna attività selezionata</h3>
            <p className="text-gray-500 max-w-md text-center">Seleziona un'attività dal menu laterale per visualizzare le statistiche e gestire le tue prenotazioni.</p>
          </div>
        ) : (
          <>
            {isLoadingConfig ? (
              <div className="flex justify-center py-10">
                <div className="animate-pulse flex flex-col items-center">
                  <div className="h-6 w-32 bg-gray-300 rounded mb-4"></div>
                  <div className="grid grid-cols-1 md:grid-cols-6 gap-4 w-full max-w-4xl">
                    <div className="col-span-2 h-64 bg-gray-300 rounded"></div>
                    <div className="col-span-2 h-64 bg-gray-300 rounded"></div>
                    <div className="col-span-2 h-64 bg-gray-300 rounded"></div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                {dashboardConfig.widgets
                  .filter(widget => widget.isVisible !== false)
                  .sort((a, b) => a.position - b.position)
                  .map(widget => (
                    <WidgetFactory 
                      key={widget.id} 
                      widget={widget} 
                      onRemove={() => removeWidget(widget.id)} 
                    />
                  ))}
              </div>
            )}
          </>
        )}
      </div>
      <ScrollToTop />
    </MainLayout>
  );
};

export default Dashboard;
