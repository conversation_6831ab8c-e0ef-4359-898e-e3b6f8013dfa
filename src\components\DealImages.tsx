import { ImageIcon, X } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

interface DealImagesProps {
  images: File[];
  existingImages?: string[];
  onImagesChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onImageRemove: (index: number) => void;
  onExistingImageRemove?: (index: number) => void;
}

const DealImages = ({ 
  images, 
  existingImages, 
  onImagesChange, 
  onImageRemove,
  onExistingImageRemove 
}: DealImagesProps) => {
  const totalImages = (existingImages?.length || 0) + images.length;

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-2">
          Foto dell'offerta ({totalImages}/5)
        </label>
        <div 
          onClick={() => document.getElementById('image-upload')?.click()}
          className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center cursor-pointer hover:border-primary/50 transition-colors"
        >
          <input
            id="image-upload"
            type="file"
            multiple
            accept="image/*"
            className="hidden"
            onChange={onImagesChange}
          />
          <div className="flex flex-col items-center gap-2">
            <ImageIcon className="h-8 w-8 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">
                Clicca per caricare le foto
              </p>
              <p className="text-xs text-muted-foreground">
                Formato: JPG, PNG. Max 5MB per foto
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Existing Images */}
      {existingImages && existingImages.length > 0 && (
        <div>
          <p className="text-sm font-medium mb-2">Immagini esistenti</p>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {existingImages.map((url, index) => (
              <div key={index} className="relative group">
                <img
                  src={url}
                  alt={`Immagine esistente ${index + 1}`}
                  className="w-full h-24 object-cover rounded-lg border"
                />
                {onExistingImageRemove && (
                  <Button
                    type="button"
                    onClick={() => onExistingImageRemove(index)}
                    variant="destructive"
                    size="sm"
                    className="absolute -top-2 -right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* New Images */}
      {images.length > 0 && (
        <div>
          <p className="text-sm font-medium mb-2">Nuove immagini</p>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {images.map((file, index) => (
              <div key={index} className="relative group">
                <img
                  src={URL.createObjectURL(file)}
                  alt={`Nuova immagine ${index + 1}`}
                  className="w-full h-24 object-cover rounded-lg border"
                />
                <Button
                  type="button"
                  onClick={() => onImageRemove(index)}
                  variant="destructive"
                  size="sm"
                  className="absolute -top-2 -right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DealImages;
