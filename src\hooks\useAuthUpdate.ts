
import { useState } from 'react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

export const useAuthUpdate = () => {
  const [isUpdating, setIsUpdating] = useState(false);

  const updateEmail = async (newEmail: string) => {
    setIsUpdating(true);
    try {
      const { error } = await supabase.auth.updateUser({ 
        email: newEmail 
      });

      if (error) throw error;

      toast.success("Email di conferma inviato al nuovo indirizzo");
      return true;
    } catch (error) {
      console.error('Error updating email:', error);
      toast.error("Errore durante l'aggiornamento dell'email");
      return false;
    } finally {
      setIsUpdating(false);
    }
  };

  const updatePassword = async (newPassword: string) => {
    setIsUpdating(true);
    try {
      const { error } = await supabase.auth.updateUser({ 
        password: newPassword 
      });

      if (error) throw error;

      toast.success("Password aggiornata con successo");
      return true;
    } catch (error) {
      console.error('Error updating password:', error);
      toast.error("Errore durante l'aggiornamento della password");
      return false;
    } finally {
      setIsUpdating(false);
    }
  };

  return {
    isUpdating,
    updateEmail,
    updatePassword
  };
};
