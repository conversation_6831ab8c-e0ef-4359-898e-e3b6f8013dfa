
import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { UserDetails } from "../../../types/types";

export const useUserDetails = () => {
  const auth = useAuth();
  const { user, isLoading: authLoading } = auth;
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // If auth is still loading, we need to wait
  useEffect(() => {
    if (authLoading) {
      console.log("Auth is still loading...");
      return;
    }
    
    // If auth is done loading but no user, create a demo user for development
    if (!authLoading && !user) {
      console.log("Auth loaded but no user found, using demo user");
      const demoUser: UserDetails = {
        id: "demo-user",
        email: "<EMAIL>",
        first_name: "<PERSON><PERSON>",
        last_name: "User",
        avatar_url: null,
        display_name: "Demo User",
        phone_number: null,
        registered_at: null,
        business_mode: false,
        default_business_id: null
      };
      setUserDetails(demoUser);
      setIsLoading(false);
    }
  }, [authLoading, user]);

  // Fetch user details
  useEffect(() => {
    // Skip if auth is still loading or we already have user details
    if (authLoading || !user || userDetails) {
      return;
    }
    
    const fetchUserDetails = async () => {
      console.log("Auth user state:", user); // Debug log for auth user

      try {
        console.log("Fetching user details for ID:", user.id);
        
        // Try to fetch from users_with_details view
        const { data, error } = await supabase
          .from('users_with_details')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error fetching user details:', error);
          
          // Create a minimal user details object from auth user
          const minimalUserDetails: UserDetails = {
            id: user.id,
            email: user.email || null,
            first_name: null,
            last_name: null,
            avatar_url: null,
            display_name: user.email?.split('@')[0] || 'User',
            phone_number: null,
            registered_at: null,
            business_mode: false,
            default_business_id: null
          };
          
          console.log("Using minimal user details from auth:", minimalUserDetails);
          setUserDetails(minimalUserDetails);
        } else {
          console.log("User details fetched successfully:", data);
          setUserDetails(data);
        }
      } catch (error) {
        console.error('Error in fetchUserDetails:', error);
        
        // Fallback to basic user info from auth
        if (user) {
          const fallbackUserDetails: UserDetails = {
            id: user.id,
            email: user.email || null,
            first_name: null,
            last_name: null,
            avatar_url: null,
            display_name: user.email?.split('@')[0] || 'User',
            phone_number: null,
            registered_at: null,
            business_mode: false,
            default_business_id: null
          };
          
          console.log("Using fallback user details:", fallbackUserDetails);
          setUserDetails(fallbackUserDetails);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserDetails();
  }, [authLoading, user, userDetails]);

  return { userDetails, isLoading, user: auth.user };
};
