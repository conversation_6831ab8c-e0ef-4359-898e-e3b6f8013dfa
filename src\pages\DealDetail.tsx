import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronDown, ChevronRight, Copy, X, Plus } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import MainLayout from "@/layouts/MainLayout";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

const DealForm = ({ deal, isEditing, onSubmit }: { deal: any, isEditing: boolean, onSubmit: (data: any) => void }) => {
  const [formData, setFormData] = useState({
    title: deal?.title || "",
    description: deal?.description || "",
    original_price: deal?.original_price || "",
    discount_percentage: deal?.discount_percentage || "",
    discounted_price: deal?.discounted_price || "",
    start_date: deal?.start_date ? new Date(deal.start_date).toISOString().split('T')[0] : "",
    end_date: deal?.end_date ? new Date(deal.end_date).toISOString().split('T')[0] : "",
    images: deal?.images || [],
    time_slots: deal?.time_slots || { schedule: [], exceptions: [] }
  });

  const handleSubmit = () => {
    onSubmit(formData);
  };

  return (
    <div className="space-y-6 mb-8">
      {formData.images && formData.images.length > 0 && (
        <div className="mb-8">
          <Carousel className="w-full max-w-xl mx-auto">
            <CarouselContent>
              {formData.images.map((image: string, index: number) => (
                <CarouselItem key={index}>
                  <div className="aspect-video w-full overflow-hidden rounded-lg">
                    <img
                      src={image}
                      alt={`Immagine ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious />
            <CarouselNext />
          </Carousel>
        </div>
      )}

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="title">Nome Offerta</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            readOnly={!isEditing}
            className={!isEditing ? "bg-gray-50" : ""}
          />
        </div>
        <div className="space-y-2">
          <Label>Nome Attività</Label>
          <div className="h-10 px-3 py-2 text-base bg-gray-50 border rounded-md">
            {deal?.businesses?.name || ""}
          </div>
        </div>
        <div className="col-span-2 space-y-2">
          <Label htmlFor="description">Descrizione</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            readOnly={!isEditing}
            className={`w-full px-3 py-2 border rounded-md h-32 ${!isEditing ? "bg-gray-50" : ""}`}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="startDate">Data Inizio</Label>
          <Input
            id="startDate"
            type="date"
            value={formData.start_date}
            onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
            readOnly={!isEditing}
            className={!isEditing ? "bg-gray-50" : ""}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="endDate">Data Fine</Label>
          <Input
            id="endDate"
            type="date"
            value={formData.end_date}
            onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
            readOnly={!isEditing}
            className={!isEditing ? "bg-gray-50" : ""}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="originalPrice">Prezzo Originale</Label>
          <Input
            id="originalPrice"
            type="number"
            value={formData.original_price}
            onChange={(e) => setFormData({ ...formData, original_price: e.target.value })}
            readOnly={!isEditing}
            className={!isEditing ? "bg-gray-50" : ""}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="discountPercentage">Sconto (%)</Label>
          <Input
            id="discountPercentage"
            type="number"
            value={formData.discount_percentage}
            onChange={(e) => setFormData({ ...formData, discount_percentage: e.target.value })}
            readOnly={!isEditing}
            className={!isEditing ? "bg-gray-50" : ""}
          />
        </div>
        <div className="space-y-2">
          <Label>Prezzo Scontato</Label>
          <div className="h-10 px-3 py-2 text-base bg-gray-50 border rounded-md">
            {formData.discounted_price}
          </div>
        </div>
      </div>

      {isEditing && (
        <div className="flex justify-end mt-6">
          <Button onClick={handleSubmit}>
            Salva Modifiche
          </Button>
        </div>
      )}
    </div>
  );
};

type TimeRange = {
  start: string;
  end: string;
  available_slots?: number;
};

type DaySchedule = {
  day: string;
  enabled: boolean;
  timeRanges: TimeRange[];
};

const daysOfWeek = [
  { day: "Domenica", enabled: false },
  { day: "Lunedì", enabled: true },
  { day: "Martedì", enabled: true },
  { day: "Mercoledì", enabled: true },
  { day: "Giovedì", enabled: true },
  { day: "Venerdì", enabled: true },
  { day: "Sabato", enabled: false },
];

const TimeSlots = ({ deal, updateTimeSlots, isUpdating }: { deal: any, updateTimeSlots: (timeSlots: any) => void, isUpdating: boolean }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [days, setDays] = useState<DaySchedule[]>(() => {
    console.log("Time slots by day:", deal.time_slots);
    
    const defaultDays = daysOfWeek.map(day => ({ 
      ...day, 
      timeRanges: [{ start: "09:00", end: "17:00", available_slots: 10 }] 
    }));
    
    if (!deal?.time_slots) return defaultDays;
    
    if (deal.time_slots.schedule && Array.isArray(deal.time_slots.schedule)) {
      return defaultDays.map((defaultDay, index) => {
        const dayIndex = index === 0 ? 7 : index;
        const scheduleItem = deal.time_slots.schedule.find((item: any) => 
          item.day === dayIndex
        );
        
        if (!scheduleItem) {
          return {
            ...defaultDay,
            enabled: defaultDay.enabled
          };
        }
        
        const isEnabled = Array.isArray(scheduleItem.time_slots) && scheduleItem.time_slots.length > 0;
        
        if (!isEnabled) {
          return {
            ...defaultDay,
            enabled: false,
            timeRanges: []
          };
        }
        
        const timeRanges: TimeRange[] = [];
        
        if (scheduleItem.time_slots && scheduleItem.time_slots.length > 0) {
          scheduleItem.time_slots.forEach((slot: any) => {
            if (slot && typeof slot === 'object') {
              timeRanges.push({
                start: slot.start_time || slot.start || "",
                end: slot.end_time || slot.end || "",
                available_slots: slot.available_seats || slot.available_slots || 10
              });
            }
          });
          
          if (timeRanges.length === 0) {
            timeRanges.push({ start: "09:00", end: "17:00", available_slots: 10 });
          }
        }
        
        return {
          ...defaultDay,
          enabled: isEnabled,
          timeRanges: timeRanges
        };
      });
    } 
    
    // eslint-disable-next-line no-dupe-else-if
    else if (deal.time_slots.schedule && Array.isArray(deal.time_slots.schedule)) {
      return defaultDays.map((defaultDay, index) => {
        const scheduleItem = deal.time_slots.schedule.find((item: any) => 
          (typeof item.day === 'number' && item.day === index)
        );
        
        if (!scheduleItem) {
          return {
            ...defaultDay,
            enabled: defaultDay.enabled
          };
        }
        
        const isEnabled = Array.isArray(scheduleItem.time_slots) && scheduleItem.time_slots.length > 0;
        
        if (!isEnabled) {
          return {
            ...defaultDay,
            enabled: false,
            timeRanges: []
          };
        }
        
        const timeRanges: TimeRange[] = [];
        
        if (scheduleItem.time_slots && scheduleItem.time_slots.length > 0) {
          try {
            scheduleItem.time_slots.forEach((slot: any) => {
              if (slot && typeof slot === 'object' && slot.start && slot.end) {
                timeRanges.push({
                  start: slot.start,
                  end: slot.end,
                  available_slots: slot.available_slots || 10
                });
              } else if (typeof slot === 'string') {
                const parts = slot.split('-');
                if (parts.length === 2) {
                  timeRanges.push({
                    start: parts[0].trim(),
                    end: parts[1].trim(),
                    available_slots: 10
                  });
                }
              }
            });
          } catch (error) {
            console.error("Error processing time slots:", error);
          }
          
          if (timeRanges.length === 0) {
            timeRanges.push({ start: "09:00", end: "17:00", available_slots: 10 });
          }
        }
        
        return {
          ...defaultDay,
          enabled: isEnabled,
          timeRanges: timeRanges
        };
      });
    } 
    
    else if (Array.isArray(deal.time_slots)) {
      const timeSlotsByDay = deal.time_slots.reduce((acc: any, slot: any) => {
        acc[slot.day] = {
          day: slot.day,
          enabled: true,
          timeRanges: [{ 
            start: slot.start_time, 
            end: slot.end_time,
            available_slots: slot.available_slots || 10
          }]
        };
        return acc;
      }, {});
    
      return defaultDays.map(day => ({
        ...day,
        ...timeSlotsByDay[day.day],
        timeRanges: timeSlotsByDay[day.day]?.timeRanges || day.timeRanges
      }));
    }
    
    return defaultDays;
  });

  const [exceptions, setExceptions] = useState<string[]>(() => {
    if (deal?.time_slots?.exceptions && Array.isArray(deal.time_slots.exceptions)) {
      return deal.time_slots.exceptions.map((exception: any) => {
        if (typeof exception === 'string') {
          return exception;
        } else if (exception && typeof exception === 'object') {
          if (Object.keys(exception).length === 1 && typeof Object.values(exception)[0] === 'string') {
            return Object.values(exception)[0] as string;
          }
          return exception.date || exception.day || JSON.stringify(exception);
        }
        return String(exception);
      });
    }
    return [];
  });

  const [newException, setNewException] = useState<string>("");

  console.log("Processed days:", days);
  console.log("Exceptions:", exceptions);

  const toggleDayEnabled = (index: number) => {
    if (!isEditing) return;
    
    setDays(prevDays => {
      const newDays = [...prevDays];
      newDays[index] = {
        ...newDays[index],
        enabled: !newDays[index].enabled,
        timeRanges: !newDays[index].enabled && newDays[index].timeRanges.length === 0 
          ? [{ start: "09:00", end: "17:00" }] 
          : newDays[index].timeRanges
      };
      return newDays;
    });
  };

  const updateTimeRange = (dayIndex: number, rangeIndex: number, field: 'start' | 'end' | 'available_slots', value: string | number) => {
    if (!isEditing) return;
    
    setDays(prevDays => {
      const newDays = [...prevDays];
      if (newDays[dayIndex].timeRanges[rangeIndex]) {
        newDays[dayIndex].timeRanges[rangeIndex] = {
          ...newDays[dayIndex].timeRanges[rangeIndex],
          [field]: value
        };
      }
      return newDays;
    });
  };

  const addTimeRange = (dayIndex: number) => {
    if (!isEditing) return;
    
    setDays(prevDays => {
      const newDays = [...prevDays];
      newDays[dayIndex].timeRanges.push({ start: "09:00", end: "17:00", available_slots: 10 });
      return newDays;
    });
  };

  const removeTimeRange = (dayIndex: number, rangeIndex: number) => {
    if (!isEditing) return;
    
    setDays(prevDays => {
      const newDays = [...prevDays];
      newDays[dayIndex].timeRanges = newDays[dayIndex].timeRanges.filter((_, idx) => idx !== rangeIndex);
      return newDays;
    });
  };

  const addException = () => {
    if (!isEditing || !newException) return;
    
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(newException)) {
      toast.error("Il formato della data deve essere YYYY-MM-DD");
      return;
    }
    
    if (exceptions.includes(newException)) {
      toast.error("Questa data è già presente nelle eccezioni");
      return;
    }
    
    setExceptions(prev => [...prev, newException]);
    setNewException("");
  };

  const removeException = (index: number) => {
    if (!isEditing) return;
    
    setExceptions(prev => prev.filter((_, idx) => idx !== index));
  };

  const saveChanges = async () => {
    if (!isEditing) return;
    
    try {
      const formattedSchedule = days.map((day, index) => {
        const dayNumber = index === 0 ? 7 : index;
        
        return {
          day: dayNumber,
          day_name: day.day.toLowerCase(),
          time_slots: day.enabled ? day.timeRanges.map(range => ({
            start_time: range.start,
            end_time: range.end,
            available_seats: range.available_slots || 10,
            booked_seats: 0
          })) : []
        };
      });
      
      const timeSlots = {
        schedule: formattedSchedule,
        exceptions: exceptions
      };
      
      await updateTimeSlots(timeSlots);
      
      setIsEditing(false);
    } catch (error) {
      console.error("Error saving time slots:", error);
    }
  };

  const cancelEditing = () => {
    setDays(() => {
      const defaultDays = daysOfWeek.map(day => ({ 
        ...day, 
        timeRanges: [{ start: "09:00", end: "17:00", available_slots: 10 }] 
      }));
      
      if (!deal?.time_slots) return defaultDays;
      
      if (deal.time_slots.schedule && Array.isArray(deal.time_slots.schedule)) {
        return defaultDays.map((defaultDay, index) => {
          const scheduleItem = deal.time_slots.schedule.find((item: any) => 
            (typeof item.day === 'number' && item.day === index)
          );
          
          if (!scheduleItem) {
            return {
              ...defaultDay,
              enabled: defaultDay.enabled
            };
          }
          
          const isEnabled = Array.isArray(scheduleItem.time_slots) && scheduleItem.time_slots.length > 0;
          
          if (!isEnabled) {
            return {
              ...defaultDay,
              enabled: false,
              timeRanges: []
            };
          }
          
          const timeRanges: TimeRange[] = [];
          
          if (scheduleItem.time_slots && scheduleItem.time_slots.length > 0) {
            if (scheduleItem.time_slots.length === 2) {
              try {
                scheduleItem.time_slots.forEach((slot: any) => {
                  if (slot && typeof slot === 'object' && slot.start && slot.end) {
                    timeRanges.push({
                      start: slot.start,
                      end: slot.end,
                      available_slots: slot.available_slots || 10
                    });
                  } else if (typeof slot === 'string') {
                    const parts = slot.split('-');
                    if (parts.length === 2) {
                      timeRanges.push({
                        start: parts[0].trim(),
                        end: parts[1].trim(),
                        available_slots: 10
                      });
                    }
                  }
                });
              } catch (error) {
                console.error("Error processing time slots:", error);
              }
            }
            
            if (timeRanges.length === 0) {
              timeRanges.push({ start: "09:00", end: "17:00", available_slots: 10 });
            }
          }
          
          return {
            ...defaultDay,
            enabled: isEnabled,
            timeRanges: timeRanges
          };
        });
      } 
      
      else if (Array.isArray(deal.time_slots)) {
        const timeSlotsByDay = deal.time_slots.reduce((acc: any, slot: any) => {
          acc[slot.day] = {
            day: slot.day,
            enabled: true,
            timeRanges: [{ 
              start: slot.start_time, 
              end: slot.end_time,
              available_slots: slot.available_slots || 10
            }]
          };
          return acc;
        }, {});
      
        return defaultDays.map(day => ({
          ...day,
          ...timeSlotsByDay[day.day],
          timeRanges: timeSlotsByDay[day.day]?.timeRanges || day.timeRanges
        }));
      }
      
      return defaultDays;
    });
    
    setExceptions(() => {
      if (deal?.time_slots?.exceptions && Array.isArray(deal.time_slots.exceptions)) {
        return deal.time_slots.exceptions.map((exception: any) => {
          if (typeof exception === 'string') {
            return exception;
          }
          else if (exception && typeof exception === 'object') {
            if (Object.keys(exception).length === 1 && typeof Object.values(exception)[0] === 'string') {
              return Object.values(exception)[0] as string;
            }
            return exception.date || exception.day || JSON.stringify(exception);
          }
          return String(exception);
        });
      }
      return [];
    });
    
    setNewException("");
    setIsEditing(false);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-medium">Orari Settimanali</h2>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <Button 
                variant="outline" 
                onClick={cancelEditing}
                disabled={isUpdating}
              >
                Annulla
              </Button>
              <Button 
                onClick={saveChanges}
                disabled={isUpdating}
              >
                {isUpdating ? "Salvataggio..." : "Salva Modifiche"}
              </Button>
            </>
          ) : (
            <Button onClick={() => setIsEditing(true)}>
              Modifica Orari
            </Button>
          )}
        </div>
      </div>
      
      {days.map((day, index) => (
        <div key={day.day} className="p-4 bg-gray-50 rounded-lg">
          <div className="flex items-start gap-4">
            <div className="flex items-center gap-4">
              <Switch 
                checked={day.enabled}
                disabled={!isEditing}
                onCheckedChange={() => toggleDayEnabled(index)}
              />
              <span className="w-28">{day.day}</span>
            </div>
            <div className="flex-1">
              {day.enabled && day.timeRanges.length > 0 && (
                <div className="space-y-2">
                  {day.timeRanges.map((timeRange, idx) => (
                    <div key={idx} className="flex items-center gap-2">
                      <input
                        type="time"
                        value={timeRange.start}
                        onChange={(e) => updateTimeRange(index, idx, 'start', e.target.value)}
                        readOnly={!isEditing}
                        className={`w-24 px-3 py-1.5 border rounded ${isEditing ? 'bg-white' : 'bg-gray-50'}`}
                      />
                      <span className="text-gray-500">-</span>
                      <input
                        type="time"
                        value={timeRange.end}
                        onChange={(e) => updateTimeRange(index, idx, 'end', e.target.value)}
                        readOnly={!isEditing}
                        className={`w-24 px-3 py-1.5 border rounded ${isEditing ? 'bg-white' : 'bg-gray-50'}`}
                      />
                      
                      <div className="ml-4 flex items-center">
                        <span className="text-sm mr-2">Posti:</span>
                        <input
                          type="number"
                          min="1"
                          max="100"
                          value={timeRange.available_slots || 10}
                          onChange={(e) => updateTimeRange(index, idx, 'available_slots', parseInt(e.target.value))}
                          readOnly={!isEditing}
                          className={`w-16 px-3 py-1.5 border rounded ${isEditing ? 'bg-white' : 'bg-gray-50'}`}
                        />
                      </div>
                      
                      {isEditing && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => removeTimeRange(index, idx)}
                          className="text-red-500"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                  {isEditing && day.enabled && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => addTimeRange(index)}
                      className="mt-2"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Aggiungi Fascia Oraria
                    </Button>
                  )}
                </div>
              )}
              {(!day.enabled || day.timeRanges.length === 0) && (
                <div className="text-gray-500 italic">Nessun orario disponibile</div>
              )}
            </div>
          </div>
        </div>
      ))}

      <div className="mt-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium">Eccezioni Date</h2>
        </div>
        <p className="text-gray-500 mb-4">
          Date con disponibilità diverse rispetto alle ore giornaliere
        </p>
        
        {isEditing && (
          <div className="flex gap-2 mb-4">
            <Input
              type="date"
              value={newException}
              onChange={(e) => setNewException(e.target.value)}
              placeholder="YYYY-MM-DD"
              className="w-64"
            />
            <Button onClick={addException}>
              Aggiungi Eccezione
            </Button>
          </div>
        )}
        
        {exceptions.length > 0 ? (
          <div className="space-y-4">
            {exceptions.map((exception, index) => (
              <div key={index} className="p-4 bg-gray-50 rounded-lg flex justify-between items-center">
                <div className="font-medium">
                  {exception}
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-gray-500 italic">
                    Nessuna disponibilità in questa data
                  </div>
                  {isEditing && (
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => removeException(index)}
                      className="text-red-500"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-gray-500 italic">Nessuna eccezione configurata</div>
        )}
      </div>
    </div>
  );
};

const DealDetail = () => {
  const [isEditing, setIsEditing] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const queryClient = useQueryClient();

  const { data: deal, isLoading, error } = useQuery({
    queryKey: ['deal', id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('deals')
        .select(`
          *,
          businesses(name)
        `)
        .eq('id', id)
        .single();

      if (error) {
        toast.error("Errore nel caricamento dell'offerta");
        throw error;
      }

      return data;
    },
  });

  const updateDealMutation = useMutation({
    mutationFn: async (data: any) => {
      const { data: updatedDeal, error } = await supabase
        .from('deals')
        .update(data)
        .eq('id', id as string)
        .select();
      
      if (error) {
        throw error;
      }
      
      return updatedDeal;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['deal', id] });
      toast.success("Offerta aggiornata con successo");
      setIsEditing(false);
    },
    onError: (error) => {
      console.error("Error updating deal:", error);
      toast.error("Errore durante l'aggiornamento dell'offerta");
    }
  });

  const updateTimeSlotsData = useMutation({
    mutationFn: async (timeSlots: any) => {
      const { data, error } = await supabase
        .from('deals')
        .update({ time_slots: timeSlots })
        .eq('id', id as string)
        .select();
      
      if (error) {
        throw error;
      }
      
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['deal', id] });
      toast.success("Orari aggiornati con successo");
    },
    onError: (error) => {
      console.error("Error updating time slots:", error);
      toast.error("Errore durante il salvataggio degli orari");
    }
  });

  const handleDealUpdate = (formData: any) => {
    updateDealMutation.mutate({
      title: formData.title,
      description: formData.description,
      original_price: formData.original_price,
      discount_percentage: formData.discount_percentage,
      start_date: formData.start_date,
      end_date: formData.end_date,
      images: formData.images
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    console.error("Errore nel caricamento:", error);
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-semibold mb-4">Si è verificato un errore</h2>
          <Button onClick={() => navigate('/deals')}>Torna alle offerte</Button>
        </div>
      </div>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="flex justify-between items-center mb-6">
          <Button 
            variant="ghost" 
            onClick={() => navigate('/deals')}
            className="gap-2"
          >
            <ChevronLeft className="h-5 w-5" />
            Torna alle offerte
          </Button>
          <Button
            onClick={() => setIsEditing(!isEditing)}
          >
            {isEditing ? "Annulla Modifiche" : "Modifica Offerta"}
          </Button>
        </div>

        <div className="bg-white rounded-lg shadow">
          <div className="p-6">
            <DealForm 
              deal={deal} 
              isEditing={isEditing} 
              onSubmit={handleDealUpdate}
            />
          </div>
          <div className="border-t">
            <div className="p-6">
              <TimeSlots 
                deal={deal} 
                updateTimeSlots={(timeSlots: any) => updateTimeSlotsData.mutate(timeSlots)}
                isUpdating={updateTimeSlotsData.isPending}
              />
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default DealDetail;
