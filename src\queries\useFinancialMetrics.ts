
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { subDays, format } from 'date-fns';

// Tipi per i filtri finanziari
export interface FinancialFilters {
  dateRange: 'last7days' | 'last30days' | 'last90days' | 'all';
}

// Tipi per i dati finanziari
export interface FinancialMetrics {
  totalRevenue: number;
  estimatedProfit: number;
  profitMargin: number;
  avgBookingPrice: number;
  revenuePerClient: number;
  totalBookings: number;
  uniqueClients: number;
  // Variazioni percentuali
  totalRevenueChange: number;
  estimatedProfitChange: number;
  avgBookingPriceChange: number;
  revenuePerClientChange: number;
}

// Valori di filtro predefiniti
export const defaultFinancialFilters: FinancialFilters = {
  dateRange: 'last30days',
};

/**
 * Hook per ottenere le metriche finanziarie
 */
export const useFinancialMetrics = (
  businessId: string | undefined,
  filters: FinancialFilters = defaultFinancialFilters
) => {
  return useQuery({
    queryKey: ['financialMetrics', businessId, filters.dateRange],
    queryFn: async () => {
      if (!businessId) {
        throw new Error('Business ID is required');
      }

      // Calcola la data di inizio in base al filtro
      let startDate = new Date();
      let previousPeriodStartDate = new Date();
      let previousPeriodEndDate = new Date();

      switch (filters.dateRange) {
        case 'last7days':
          startDate = subDays(new Date(), 7);
          previousPeriodStartDate = subDays(new Date(), 14);
          previousPeriodEndDate = subDays(new Date(), 7);
          break;
        case 'last30days':
          startDate = subDays(new Date(), 30);
          previousPeriodStartDate = subDays(new Date(), 60);
          previousPeriodEndDate = subDays(new Date(), 30);
          break;
        case 'last90days':
          startDate = subDays(new Date(), 90);
          previousPeriodStartDate = subDays(new Date(), 180);
          previousPeriodEndDate = subDays(new Date(), 90);
          break;
        case 'all':
          // Non limitare per data
          startDate = new Date(0); // 1970-01-01
          previousPeriodStartDate = new Date(0); // Non c'è un periodo precedente per 'all'
          previousPeriodEndDate = new Date(0);
          break;
      }

      const formattedStartDate = format(startDate, 'yyyy-MM-dd');
      const formattedPreviousPeriodStartDate = format(previousPeriodStartDate, 'yyyy-MM-dd');
      const formattedPreviousPeriodEndDate = format(previousPeriodEndDate, 'yyyy-MM-dd');

      console.log('Parametri di ricerca finanziari:', {
        businessId,
        dateRange: filters.dateRange,
        formattedStartDate,
        formattedPreviousPeriodStartDate, 
        formattedPreviousPeriodEndDate
      });

      // Ottieni le prenotazioni per il business selezionato
      const { data: deals, error: dealsError } = await supabase
        .from('deals')
        .select('id, business_id')
        .eq('business_id', businessId);

      if (dealsError) throw dealsError;

      if (!deals || deals.length === 0) {
        console.log('Nessuna offerta trovata per il business ID:', businessId);
        // Se non ci sono offerte, restituisci valori predefiniti
        return {
          totalRevenue: 0,
          estimatedProfit: 0,
          profitMargin: 0,
          avgBookingPrice: 0,
          revenuePerClient: 0,
          totalBookings: 0,
          uniqueClients: 0,
          totalRevenueChange: 0,
          estimatedProfitChange: 0,
          avgBookingPriceChange: 0,
          revenuePerClientChange: 0
        };
      }

      const dealIds = deals.map(deal => deal.id);
      console.log('Deal IDs trovati:', dealIds);

      // Ottieni le prenotazioni per le offerte del business per il periodo corrente
      const { data: bookings, error: bookingsError } = await supabase
        .from('bookings')
        .select('id, user_id, discounted_price, original_price, booking_date')
        .in('deal_id', dealIds)
        .gte('booking_date', formattedStartDate);

      if (bookingsError) throw bookingsError;
      console.log('Prenotazioni trovate per il periodo corrente:', bookings?.length || 0);

      // Ottieni le prenotazioni per il periodo precedente
      const { data: previousBookings, error: previousBookingsError } = await supabase
        .from('bookings')
        .select('id, user_id, discounted_price, original_price, booking_date')
        .in('deal_id', dealIds)
        .gte('booking_date', formattedPreviousPeriodStartDate)
        .lt('booking_date', formattedPreviousPeriodEndDate);

      if (previousBookingsError) throw previousBookingsError;
      console.log('Prenotazioni trovate per il periodo precedente:', previousBookings?.length || 0);

      // Valori predefiniti
      const defaultMetrics = {
        totalRevenue: 0,
        estimatedProfit: 0,
        profitMargin: 0,
        avgBookingPrice: 0,
        revenuePerClient: 0,
        totalBookings: 0,
        uniqueClients: 0,
        totalRevenueChange: 0,
        estimatedProfitChange: 0,
        avgBookingPriceChange: 0,
        revenuePerClientChange: 0,
      };

      if (!bookings || bookings.length === 0) {
        console.log('Nessuna prenotazione trovata per il periodo corrente');
        // Se non ci sono prenotazioni, restituisci valori predefiniti
        return defaultMetrics;
      }

      // Debug dei prezzi delle prenotazioni
      console.log('Prezzi delle prenotazioni:', bookings.map(b => ({
        id: b.id,
        discounted_price: b.discounted_price,
        original_price: b.original_price
      })));

      // Calcola le metriche finanziarie per il periodo corrente
      // Aggiungiamo un filtro più permissivo - accettiamo prezzi 0 o positivi
      const validBookings = bookings.filter(booking => {
        // Controllare se almeno uno dei prezzi è valido e non negativo
        const hasValidDiscountedPrice = booking.discounted_price !== null && 
                                       booking.discounted_price !== undefined && 
                                       booking.discounted_price >= 0;
        
        const hasValidOriginalPrice = booking.original_price !== null && 
                                     booking.original_price !== undefined && 
                                     booking.original_price >= 0;
        
        // Utilizziamo il prezzo scontato se disponibile, altrimenti quello originale
        return hasValidDiscountedPrice || hasValidOriginalPrice;
      });
      
      console.log('Prenotazioni valide trovate:', validBookings.length);
      console.log('Prenotazioni non valide escluse:', bookings.length - validBookings.length);
      
      // Se non ci sono prenotazioni valide, restituisci valori predefiniti
      if (validBookings.length === 0) {
        console.log('Nessuna prenotazione con prezzi validi trovata');
        return defaultMetrics;
      }
      
      // Per ogni prenotazione valida, prendi il prezzo scontato se disponibile, altrimenti quello originale
      const totalRevenue = validBookings.reduce((sum, booking) => {
        const price = (booking.discounted_price !== null && booking.discounted_price !== undefined && booking.discounted_price >= 0) 
          ? booking.discounted_price 
          : (booking.original_price || 0);
        return sum + price;
      }, 0);
      
      console.log('Ricavo totale calcolato:', totalRevenue);

      // Assumiamo un margine di profitto del 30% per il calcolo del profitto stimato
      const profitMargin = 0.3;
      const estimatedProfit = totalRevenue * profitMargin;

      const totalBookings = validBookings.length;
      const avgBookingPrice = totalBookings > 0 ? totalRevenue / totalBookings : 0;
      console.log('Prezzo medio di prenotazione calcolato:', avgBookingPrice);

      // Calcola i clienti unici (solo quelli con user_id non nullo)
      const uniqueClientIds = new Set(validBookings
        .filter(booking => booking.user_id !== null && booking.user_id !== undefined)
        .map(booking => booking.user_id));
      
      const uniqueClients = uniqueClientIds.size;
      console.log('Clienti unici calcolati:', uniqueClients);

      // Calcola il ricavo per cliente (evita divisione per zero)
      const revenuePerClient = uniqueClients > 0 ? totalRevenue / uniqueClients : 0;
      console.log('Ricavo per cliente calcolato:', revenuePerClient);

      // Calcola le metriche finanziarie per il periodo precedente
      // Utilizziamo la stessa logica per il prezzo come per il periodo corrente
      const validPreviousBookings = previousBookings?.filter(booking => {
        const hasValidDiscountedPrice = booking.discounted_price !== null && 
                                       booking.discounted_price !== undefined && 
                                       booking.discounted_price >= 0;
        
        const hasValidOriginalPrice = booking.original_price !== null && 
                                     booking.original_price !== undefined && 
                                     booking.original_price >= 0;
        
        return hasValidDiscountedPrice || hasValidOriginalPrice;
      }) || [];
      
      const previousTotalRevenue = validPreviousBookings.reduce((sum, booking) => {
        const price = (booking.discounted_price !== null && booking.discounted_price !== undefined && booking.discounted_price >= 0) 
          ? booking.discounted_price 
          : (booking.original_price || 0);
        return sum + price;
      }, 0);
      
      const previousEstimatedProfit = previousTotalRevenue * profitMargin;
      const previousTotalBookings = validPreviousBookings.length;
      const previousAvgBookingPrice = previousTotalBookings > 0 ? previousTotalRevenue / previousTotalBookings : 0;

      // Calcola i clienti unici per il periodo precedente (solo quelli con user_id non nullo)
      const previousUniqueClientIds = new Set(validPreviousBookings
        .filter(booking => booking.user_id !== null && booking.user_id !== undefined)
        .map(booking => booking.user_id));
      
      const previousUniqueClients = previousUniqueClientIds.size;

      // Calcola il ricavo per cliente per il periodo precedente (evita divisione per zero)
      const previousRevenuePerClient = previousUniqueClients > 0 ? previousTotalRevenue / previousUniqueClients : 0;

      // Calcola le variazioni percentuali
      const calculatePercentChange = (current: number, previous: number): number => {
        if (previous === 0) return current > 0 ? 100 : 0;
        return ((current - previous) / previous) * 100;
      };

      const totalRevenueChange = calculatePercentChange(totalRevenue, previousTotalRevenue);
      const estimatedProfitChange = calculatePercentChange(estimatedProfit, previousEstimatedProfit);
      const avgBookingPriceChange = calculatePercentChange(avgBookingPrice, previousAvgBookingPrice);
      const revenuePerClientChange = calculatePercentChange(revenuePerClient, previousRevenuePerClient);

      // Metriche finali
      const metrics = {
        totalRevenue,
        estimatedProfit,
        profitMargin,
        avgBookingPrice,
        revenuePerClient,
        totalBookings,
        uniqueClients,
        totalRevenueChange,
        estimatedProfitChange,
        avgBookingPriceChange,
        revenuePerClientChange,
      };
      
      console.log('Metriche finanziarie calcolate:', metrics);
      return metrics;
    },
    enabled: !!businessId,
    // Disabilita il refetch automatico in background
    refetchOnWindowFocus: false,
    // Imposta un staleTime più lungo per evitare richieste inutili
    staleTime: 5 * 60 * 1000, // 5 minuti
  });
};
