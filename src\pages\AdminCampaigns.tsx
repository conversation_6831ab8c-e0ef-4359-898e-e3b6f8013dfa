import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { Plus, Edit, Trash2, Mail, Calendar, Eye, Send } from "lucide-react";
import MainLayout from "@/layouts/MainLayout";
import { format } from "date-fns";

interface Campaign {
  id: string;
  name: string;
  description: string | null;
  email_subject: string | null;
  email_content: string | null;
  created_at: string;
  updated_at: string;
}

const AdminCampaigns = () => {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    email_subject: "",
    email_content: "",
  });

  useEffect(() => {
    fetchCampaigns();
  }, []);

  const fetchCampaigns = async () => {
    try {
      const { data, error } = await supabase
        .from("campaigns")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw error;
      setCampaigns(data || []);
    } catch (error) {
      console.error("Error fetching campaigns:", error);
      toast.error("Errore nel caricamento delle campagne");
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async () => {
    try {
      const { error } = await supabase
        .from("campaigns")
        .insert([formData]);

      if (error) throw error;

      toast.success("Campagna creata con successo");
      setIsCreateOpen(false);
      resetForm();
      fetchCampaigns();
    } catch (error) {
      console.error("Error creating campaign:", error);
      toast.error("Errore nella creazione della campagna");
    }
  };

  const handleUpdate = async () => {
    if (!selectedCampaign) return;

    try {
      const { error } = await supabase
        .from("campaigns")
        .update(formData)
        .eq("id", selectedCampaign.id);

      if (error) throw error;

      toast.success("Campagna aggiornata con successo");
      setIsEditOpen(false);
      resetForm();
      fetchCampaigns();
    } catch (error) {
      console.error("Error updating campaign:", error);
      toast.error("Errore nell'aggiornamento della campagna");
    }
  };

  const handleDelete = async (id: string) => {
    try {
      const { error } = await supabase
        .from("campaigns")
        .delete()
        .eq("id", id);

      if (error) throw error;

      toast.success("Campagna eliminata con successo");
      fetchCampaigns();
    } catch (error) {
      console.error("Error deleting campaign:", error);
      toast.error("Errore nell'eliminazione della campagna");
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      email_subject: "",
      email_content: "",
    });
    setSelectedCampaign(null);
  };

  const openEditDialog = (campaign: Campaign) => {
    setSelectedCampaign(campaign);
    setFormData({
      name: campaign.name,
      description: campaign.description || "",
      email_subject: campaign.email_subject || "",
      email_content: campaign.email_content || "",
    });
    setIsEditOpen(true);
  };

  const openPreviewDialog = (campaign: Campaign) => {
    setSelectedCampaign(campaign);
    setIsPreviewOpen(true);
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Gestione Campagne
            </h1>
            <p className="text-muted-foreground text-lg">
              Crea e gestisci le tue campagne email marketing
            </p>
          </div>
          
          <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
            <DialogTrigger asChild>
              <Button size="lg" className="shadow-lg hover:shadow-xl transition-all duration-300">
                <Plus className="h-5 w-5 mr-2" />
                Nuova Campagna
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle className="text-2xl font-bold">Crea Nuova Campagna</DialogTitle>
                <DialogDescription>
                  Crea una nuova campagna email per i tuoi utenti
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-6 py-4">
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-sm font-semibold">Nome Campagna</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="Es. Offerte Black Friday 2024"
                    className="h-12"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description" className="text-sm font-semibold">Descrizione</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Descrivi l'obiettivo di questa campagna..."
                    className="min-h-[80px]"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email_subject" className="text-sm font-semibold">Oggetto Email</Label>
                  <Input
                    id="email_subject"
                    value={formData.email_subject}
                    onChange={(e) => setFormData({ ...formData, email_subject: e.target.value })}
                    placeholder="Oggetto dell'email..."
                    className="h-12"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email_content" className="text-sm font-semibold">Contenuto Email</Label>
                  <Textarea
                    id="email_content"
                    value={formData.email_content}
                    onChange={(e) => setFormData({ ...formData, email_content: e.target.value })}
                    placeholder="Scrivi il contenuto dell'email in HTML..."
                    className="min-h-[120px] font-mono text-sm"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => { setIsCreateOpen(false); resetForm(); }}>
                  Annulla
                </Button>
                <Button onClick={handleCreate} disabled={!formData.name}>
                  <Mail className="h-4 w-4 mr-2" />
                  Crea Campagna
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="border-l-4 border-l-primary shadow-md hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-muted-foreground">Totale Campagne</CardTitle>
              <CardDescription className="text-3xl font-bold text-primary">
                {campaigns.length}
              </CardDescription>
            </CardHeader>
          </Card>
          <Card className="border-l-4 border-l-secondary shadow-md hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-muted-foreground">Campagne Attive</CardTitle>
              <CardDescription className="text-3xl font-bold text-secondary">
                {campaigns.filter(c => c.email_content).length}
              </CardDescription>
            </CardHeader>
          </Card>
          <Card className="border-l-4 border-l-accent shadow-md hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-muted-foreground">Ultima Creazione</CardTitle>
              <CardDescription className="text-lg font-semibold">
                {campaigns.length > 0 ? format(new Date(campaigns[0].created_at), "dd/MM/yyyy") : "N/A"}
              </CardDescription>
            </CardHeader>
          </Card>
        </div>

        {/* Campaigns Table */}
        <Card className="shadow-lg">
          <CardHeader className="border-b">
            <CardTitle className="text-xl font-bold flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Lista Campagne
            </CardTitle>
            <CardDescription>
              Gestisci tutte le tue campagne email marketing
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="bg-muted/50">
                  <TableHead className="font-semibold">Nome</TableHead>
                  <TableHead className="font-semibold">Descrizione</TableHead>
                  <TableHead className="font-semibold">Oggetto</TableHead>
                  <TableHead className="font-semibold">Status</TableHead>
                  <TableHead className="font-semibold">Data Creazione</TableHead>
                  <TableHead className="text-right font-semibold">Azioni</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {campaigns.map((campaign) => (
                  <TableRow key={campaign.id} className="hover:bg-muted/30 transition-colors">
                    <TableCell className="font-medium">{campaign.name}</TableCell>
                    <TableCell className="max-w-xs truncate">
                      {campaign.description || "Nessuna descrizione"}
                    </TableCell>
                    <TableCell className="max-w-xs truncate">
                      {campaign.email_subject || "Nessun oggetto"}
                    </TableCell>
                    <TableCell>
                      <Badge variant={campaign.email_content ? "default" : "secondary"}>
                        {campaign.email_content ? "Completa" : "Bozza"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {format(new Date(campaign.created_at), "dd/MM/yyyy HH:mm")}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openPreviewDialog(campaign)}
                          className="hover:bg-primary/10"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openEditDialog(campaign)}
                          className="hover:bg-secondary/10"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="ghost" size="sm" className="hover:bg-destructive/10 text-destructive">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Elimina Campagna</AlertDialogTitle>
                              <AlertDialogDescription>
                                Sei sicuro di voler eliminare la campagna "{campaign.name}"? 
                                Questa azione non può essere annullata.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Annulla</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDelete(campaign.id)}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                              >
                                Elimina
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {campaigns.length === 0 && (
              <div className="text-center py-12">
                <Mail className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-lg font-medium text-muted-foreground">Nessuna campagna trovata</p>
                <p className="text-sm text-muted-foreground">Crea la tua prima campagna per iniziare</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Edit Dialog */}
        <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle className="text-2xl font-bold">Modifica Campagna</DialogTitle>
              <DialogDescription>
                Aggiorna i dettagli della campagna
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-6 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name" className="text-sm font-semibold">Nome Campagna</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="h-12"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-description" className="text-sm font-semibold">Descrizione</Label>
                <Textarea
                  id="edit-description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="min-h-[80px]"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-email_subject" className="text-sm font-semibold">Oggetto Email</Label>
                <Input
                  id="edit-email_subject"
                  value={formData.email_subject}
                  onChange={(e) => setFormData({ ...formData, email_subject: e.target.value })}
                  className="h-12"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-email_content" className="text-sm font-semibold">Contenuto Email</Label>
                <Textarea
                  id="edit-email_content"
                  value={formData.email_content}
                  onChange={(e) => setFormData({ ...formData, email_content: e.target.value })}
                  className="min-h-[120px] font-mono text-sm"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => { setIsEditOpen(false); resetForm(); }}>
                Annulla
              </Button>
              <Button onClick={handleUpdate}>
                <Edit className="h-4 w-4 mr-2" />
                Aggiorna Campagna
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Preview Dialog */}
        <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
          <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-2xl font-bold flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Anteprima Campagna
              </DialogTitle>
            </DialogHeader>
            {selectedCampaign && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-semibold text-muted-foreground">Nome</Label>
                    <p className="text-lg font-medium">{selectedCampaign.name}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-semibold text-muted-foreground">Data Creazione</Label>
                    <p className="text-lg">{format(new Date(selectedCampaign.created_at), "dd/MM/yyyy HH:mm")}</p>
                  </div>
                </div>
                
                {selectedCampaign.description && (
                  <div>
                    <Label className="text-sm font-semibold text-muted-foreground">Descrizione</Label>
                    <p className="text-base mt-1">{selectedCampaign.description}</p>
                  </div>
                )}
                
                {selectedCampaign.email_subject && (
                  <div>
                    <Label className="text-sm font-semibold text-muted-foreground">Oggetto Email</Label>
                    <p className="text-base mt-1 font-medium">{selectedCampaign.email_subject}</p>
                  </div>
                )}
                
                {selectedCampaign.email_content && (
                  <div>
                    <Label className="text-sm font-semibold text-muted-foreground">Contenuto Email</Label>
                    <div className="mt-2 p-4 border rounded-lg bg-muted/30">
                      <div 
                        dangerouslySetInnerHTML={{ __html: selectedCampaign.email_content }}
                        className="prose prose-sm max-w-none"
                      />
                    </div>
                  </div>
                )}
              </div>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsPreviewOpen(false)}>
                Chiudi
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  );
};

export default AdminCampaigns;