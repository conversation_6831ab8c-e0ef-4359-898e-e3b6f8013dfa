-- Add RLS policies for categories table to allow authenticated users to manage categories
CREATE POLICY "Authenticated users can create categories" 
  ON public.categories 
  FOR INSERT 
  WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Authenticated users can update categories" 
  ON public.categories 
  FOR UPDATE 
  USING (auth.uid() IS NOT NULL);

CREATE POLICY "Authenticated users can delete categories" 
  ON public.categories 
  FOR DELETE 
  USING (auth.uid() IS NOT NULL);