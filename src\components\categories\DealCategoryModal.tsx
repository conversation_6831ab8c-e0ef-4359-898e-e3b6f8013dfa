import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Category } from "@/types/types";

interface DealCategory {
  id: string;
  name: string;
  category_id: string;
  created_at: string;
}

interface DealCategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  dealCategory?: DealCategory | null;
  onSuccess: () => void;
}

interface FormData {
  name: string;
  category_id: string;
}

export const DealCategoryModal = ({ isOpen, onClose, dealCategory, onSuccess }: DealCategoryModalProps) => {
  const { toast } = useToast();
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const { register, handleSubmit, reset, setValue, watch, formState: { errors } } = useForm<FormData>({
    defaultValues: {
      name: "",
      category_id: ""
    }
  });

  const categoryId = watch("category_id");

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    if (dealCategory) {
      setValue("name", dealCategory.name);
      setValue("category_id", dealCategory.category_id);
    } else {
      reset();
    }
  }, [dealCategory, setValue, reset]);

  const fetchCategories = async () => {
    const { data, error } = await supabase
      .from("categories")
      .select("*")
      .order("name");

    if (error) {
      toast({
        title: "Errore",
        description: "Impossibile caricare le categorie",
        variant: "destructive",
      });
      return;
    }

    setCategories(data || []);
  };

  const onSubmit = async (data: FormData) => {
    setIsLoading(true);
    
    try {
      if (dealCategory) {
        // Update existing deal category
        const { error } = await supabase
          .from("deal_categories")
          .update({
            name: data.name,
            category_id: data.category_id,
          })
          .eq("id", dealCategory.id);

        if (error) throw error;

        toast({
          title: "Successo",
          description: "Categoria deal aggiornata con successo",
        });
      } else {
        // Create new deal category
        const { error } = await supabase
          .from("deal_categories")
          .insert({
            name: data.name,
            category_id: data.category_id,
          });

        if (error) throw error;

        toast({
          title: "Successo",
          description: "Categoria deal creata con successo",
        });
      }

      onSuccess();
      onClose();
      reset();
    } catch (error: any) {
      toast({
        title: "Errore",
        description: error.message || "Si è verificato un errore",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {dealCategory ? "Modifica Categoria Deal" : "Nuova Categoria Deal"}
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="category_id">Categoria Principale *</Label>
            <Select value={categoryId} onValueChange={(value) => setValue("category_id", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Seleziona una categoria" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.category_id && (
              <p className="text-sm text-destructive">La categoria è obbligatoria</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="name">Nome *</Label>
            <Input
              id="name"
              {...register("name", { required: "Il nome è obbligatorio" })}
              placeholder="Nome della categoria deal"
            />
            {errors.name && (
              <p className="text-sm text-destructive">{errors.name.message}</p>
            )}
          </div>

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
              Annulla
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Salvando..." : dealCategory ? "Aggiorna" : "Crea"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};