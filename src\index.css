
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 262 83% 58%;
    --radius: 0.75rem;


    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
 
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 262 83% 58%;


    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 0 0% 98%;
    --sidebar-primary-foreground: 240 5.9% 10%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }




  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  .font-inter {
    font-family: 'Inter', sans-serif;
  }
}

/* Custom styles for places-ui-kit */
.places-ui-kit {
  display: grid;
  grid-template-columns: 400px 1fr;
  width: 100%;
  height: 100%;
}

/* Responsive design for mobile */
@media (max-width: 768px) {
  .places-ui-kit {
    grid-template-columns: 1fr;
    grid-template-rows: 300px 1fr;
  }
  
  .places-ui-kit .place-list-wrapper {
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .places-ui-kit .place-list-container {
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }
}

.places-ui-kit .place-list-wrapper {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.places-ui-kit .place-list-container {
  background-color: white;
  height: 100%;
  overflow-y: auto;
  border-right: 1px solid #e5e7eb;
}

.places-ui-kit .map-container {
  position: relative;
}

.places-ui-kit .autocomplete-wrapper {
  position: absolute;
  background-color: white;
  top: 24px;
  left: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 8px;
  box-shadow:
    0 3px 6px rgba(0, 0, 0, 0.16),
    0 3px 6px rgba(0, 0, 0, 0.23);
}

.places-ui-kit #detailsSize {
  margin-bottom: 8px;
}

[class$='alpha-banner'] {
  right: auto;
  left: 50%;
  translate: -50% 0;
  top: auto;
  bottom: 1.5rem;
  max-width: 600px;
  font-size: 0.875em;
  border-radius: 10px;
}



/* Custom styles for financial simulator */
.financial-simulator {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.metric-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.chart-container {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.table-row-positive {
  background-color: rgba(34, 197, 94, 0.05);
}

.table-row-negative {
  background-color: rgba(239, 68, 68, 0.05);
}

.parameter-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.header-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.kpi-value {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.kpi-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.scenario-button {
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.scenario-button:hover {
  border-color: #667eea;
  background-color: rgba(102, 126, 234, 0.05);
}

.tab-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.recharts-tooltip-wrapper {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.recharts-default-tooltip {
  border-radius: 8px;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsive improvements */
@media (max-width: 768px) {
  .kpi-value {
    font-size: 1.875rem;
  }
  
  .parameter-section {
    padding: 16px;
  }
  
  .chart-container {
    padding: 16px;
  }
}

/* Animation for metric cards */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.metric-card {
  animation: slideInUp 0.6s ease-out;
}

.metric-card:nth-child(1) { animation-delay: 0.1s; }
.metric-card:nth-child(2) { animation-delay: 0.2s; }
.metric-card:nth-child(3) { animation-delay: 0.3s; }
.metric-card:nth-child(4) { animation-delay: 0.4s; }

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Professional color scheme for charts */
.recharts-area-chart .recharts-area {
  fill-opacity: 0.8;
}

.recharts-line-chart .recharts-line {
  stroke-width: 3;
}

.recharts-bar-chart .recharts-bar {
  fill-opacity: 0.9;
}

/* Custom scrollbar for tables */
.table-scroll::-webkit-scrollbar {
  height: 8px;
}

.table-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-scroll::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-scroll::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

