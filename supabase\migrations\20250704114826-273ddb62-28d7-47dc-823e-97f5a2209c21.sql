-- Fix RLS policies for ai_business_agents_definition to use security definer function instead of direct auth.users access

-- Drop existing policies
DROP POLICY IF EXISTS "Only admins can create agent definitions" ON public.ai_business_agents_definition;
DROP POLICY IF EXISTS "Only admins can update agent definitions" ON public.ai_business_agents_definition;
DROP POLICY IF EXISTS "Only admins can delete agent definitions" ON public.ai_business_agents_definition;

-- Create new policies using the existing security definer function
CREATE POLICY "Only admins can create agent definitions" 
ON public.ai_business_agents_definition 
FOR INSERT 
WITH CHECK (
  public.get_user_role(auth.uid()) = 'admin'
);

CREATE POLICY "Only admins can update agent definitions" 
ON public.ai_business_agents_definition 
FOR UPDATE 
USING (
  public.get_user_role(auth.uid()) = 'admin'
)
WITH CHECK (
  public.get_user_role(auth.uid()) = 'admin'
);

CREATE POLICY "Only admins can delete agent definitions" 
ON public.ai_business_agents_definition 
FOR DELETE 
USING (
  public.get_user_role(auth.uid()) = 'admin'
);