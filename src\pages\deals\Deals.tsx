
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, CalendarX, FilterX } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import MainLayout from "@/layouts/MainLayout";
import { toast } from "sonner";
import { useBusinessStore } from "@/store/businessStore";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  // getPaginationRowModel non è più necessario con la paginazione manuale
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
} from "@tanstack/react-table";
import { DataTablePagination } from "@/components/DataTablePagination";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";

interface DealType {
  id: string;
  title: string;
  business: {
    name: string;
  };
  original_price: number;
  discounted_price: number;
  discount_percentage: number;
  end_date: string;
  start_date: string;
}

const Deals = () => {
  const navigate = useNavigate();
  const selectedBusiness = useBusinessStore(state => state.selectedBusiness);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [statusFilter, setStatusFilter] = useState<string>("active");
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [totalCount, setTotalCount] = useState(0);

  const { data, isLoading, isFetching } = useQuery({
    queryKey: ['deals', selectedBusiness?.id, statusFilter, pagination.pageIndex, pagination.pageSize],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) throw new Error("Utente non autenticato");
      if (!selectedBusiness) return { data: [], count: 0 };

      // Costruisci la query di base
      let query = supabase
        .from('deals')
        .select(`
          *,
          business:businesses(*)
        `, { count: 'exact' })
        .eq('business_id', selectedBusiness.id);

      // Applica il filtro per stato (attivo/scaduto)
      const now = new Date().toISOString();
      if (statusFilter === "active") {
        query = query.gte('end_date', now);
      } else if (statusFilter === "expired") {
        query = query.lt('end_date', now);
      }

      // Applica paginazione
      const from = pagination.pageIndex * pagination.pageSize;
      const to = from + pagination.pageSize - 1;
      query = query.range(from, to);

      // Esegui la query
      const { data, error, count } = await query;

      if (error) throw error;

      // Aggiorna il conteggio totale
      if (count !== null) {
        setTotalCount(count);
      }

      return { data: data || [], count: count || 0 };
    },
    enabled: !!selectedBusiness // Only run query if we have a selected business
  });

  // Estrai i dati dalle deal
  const deals: DealType[] = data?.data || [];

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR'
    }).format(price);
  };

  const isExpired = (endDate: string) => {
    return new Date(endDate) < new Date();
  };

  const columns: ColumnDef<DealType>[] = [
    {
      accessorKey: "title",
      header: "Titolo",
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.getValue("title")}</div>
          <div className="text-sm text-muted-foreground">{row.original.business.name}</div>
        </div>
      ),
    },
    {
      accessorKey: "original_price",
      header: "Prezzo Originale",
      cell: ({ row }) => formatPrice(row.getValue("original_price")),
    },
    {
      accessorKey: "discounted_price",
      header: "Prezzo Scontato",
      cell: ({ row }) => formatPrice(row.getValue("discounted_price")),
    },
    {
      accessorKey: "discount_percentage",
      header: "Sconto %",
      cell: ({ row }) => `${row.getValue("discount_percentage")}%`,
    },
    {
      accessorKey: "end_date",
      header: "Scadenza",
      cell: ({ row }) => {
        const endDate = row.getValue("end_date") as string;
        const expired = isExpired(endDate);
        return (
          <div className="flex items-center gap-2">
            <span>{new Date(endDate).toLocaleDateString('it-IT')}</span>
            {expired && (
              <Badge variant="outline" className="bg-gray-100 text-gray-700 border-gray-300">
                <CalendarX className="h-3 w-3 mr-1" /> Scaduta
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        return (
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              // Evita la propagazione dell'evento
              e.stopPropagation();
              navigate(`/deals/${row.original.id}`);
            }}
          >
            Dettagli
          </Button>
        );
      },
    },
  ];

  // Non è più necessario filtrare lato client poiché lo facciamo lato server
  const filteredDeals = deals;

  const table = useReactTable({
    data: filteredDeals,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true, // Usiamo la paginazione manuale (lato server)
    pageCount: Math.ceil(totalCount / pagination.pageSize),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onPaginationChange: setPagination,
    state: {
      sorting,
      columnFilters,
      pagination,
    },
  });

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-semibold">Le Mie Offerte</h1>
            <p className="text-gray-600">Gestisci le tue offerte speciali</p>
          </div>
          <Button
            onClick={() => {
              if (!selectedBusiness) {
                toast.error("Seleziona un'attività prima di creare una nuova offerta");
                return;
              }
              navigate('/deals/upsert');
            }}
            className="bg-primary-600 hover:bg-primary-700 text-white"
          >
            <Plus className="mr-2 h-4 w-4" /> Nuova Offerta
          </Button>
        </div>

        {!selectedBusiness ? (
          <div className="text-center py-8">
            Seleziona un'attività dal menu per visualizzare le relative offerte
          </div>
        ) : isLoading || isFetching ? (
          <div className="text-center py-8">Caricamento offerte...</div>
        ) : deals.length === 0 ? (
          <div className="text-center py-8">
            Nessuna offerta trovata per questa attività
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Input
                  placeholder="Cerca offerte..."
                  value={(table.getColumn("title")?.getFilterValue() as string) ?? ""}
                  onChange={(e) => table.getColumn("title")?.setFilterValue(e.target.value)}
                  className="max-w-sm"
                  disabled={isFetching}
                />
                <Button
                  variant="outline"
                  className="h-10"
                  onClick={() => {
                    table.resetColumnFilters();
                    setStatusFilter("active");
                    // Reset pagination when filters change
                    setPagination({ pageIndex: 0, pageSize: pagination.pageSize });
                  }}
                  disabled={isFetching}
                >
                  <FilterX className="h-4 w-4 mr-2" />
                  Resetta
                </Button>
              </div>
              <Select
                value={statusFilter}
                onValueChange={(value: string) => {
                  setStatusFilter(value);
                  // Reset pagination when filters change
                  setPagination({ pageIndex: 0, pageSize: pagination.pageSize });
                }}
                disabled={isFetching}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filtra per stato" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tutte le offerte</SelectItem>
                  <SelectItem value="active">Offerte attive</SelectItem>
                  <SelectItem value="expired">Offerte scadute</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <TableHead key={header.id}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </TableHead>
                      ))}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {table.getRowModel().rows?.length ? (
                    table.getRowModel().rows.map((row) => (
                      <TableRow
                        key={row.id}
                        data-state={row.getIsSelected() && "selected"}
                        className={isExpired(row.original.end_date) ? 'bg-gray-50' : ''}
                      >
                        {row.getVisibleCells().map((cell) => (
                          <TableCell key={cell.id}>
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={columns.length} className="h-24 text-center">
                        Nessun risultato.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            <div className={isFetching ? "opacity-60 pointer-events-none" : ""}>
              <DataTablePagination table={table} />
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default Deals;
