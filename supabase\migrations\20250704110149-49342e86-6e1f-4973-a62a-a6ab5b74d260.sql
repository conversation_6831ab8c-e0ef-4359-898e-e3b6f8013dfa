-- Update RLS policies for ai_business_agents_definition to restrict CUD to admins

-- Drop existing policy
DROP POLICY IF EXISTS "Enable read access for all users" ON public.ai_business_agents_definition;

-- Create new policies
-- Everyone can read agent definitions
CREATE POLICY "Anyone can view agent definitions" 
ON public.ai_business_agents_definition 
FOR SELECT 
USING (true);

-- Only admins can create agent definitions
CREATE POLICY "Only admins can create agent definitions" 
ON public.ai_business_agents_definition 
FOR INSERT 
WITH CHECK (
  EXISTS (
    SELECT 1 FROM auth.users 
    WHERE auth.users.id = auth.uid() 
    AND (auth.users.raw_user_meta_data->>'role')::text = 'admin'
  )
);

-- Only admins can update agent definitions
CREATE POLICY "Only admins can update agent definitions" 
ON public.ai_business_agents_definition 
FOR UPDATE 
USING (
  EXISTS (
    SELECT 1 FROM auth.users 
    WHERE auth.users.id = auth.uid() 
    AND (auth.users.raw_user_meta_data->>'role')::text = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM auth.users 
    WHERE auth.users.id = auth.uid() 
    AND (auth.users.raw_user_meta_data->>'role')::text = 'admin'
  )
);

-- Only admins can delete agent definitions
CREATE POLICY "Only admins can delete agent definitions" 
ON public.ai_business_agents_definition 
FOR DELETE 
USING (
  EXISTS (
    SELECT 1 FROM auth.users 
    WHERE auth.users.id = auth.uid() 
    AND (auth.users.raw_user_meta_data->>'role')::text = 'admin'
  )
);