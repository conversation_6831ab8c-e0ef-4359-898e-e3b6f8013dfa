


// City coordinate ranges for realistic address generation
type CityCoordinates = {
  name: string;
  latitude: { min: number; max: number };
  longitude: { min: number; max: number };
};

// Enhanced city data with more realistic coordinate ranges and demographics
export const cities: CityCoordinates[] = [
  {
    name: "Milano",
    latitude: { min: 45.4, max: 45.5 },
    longitude: { min: 9.1, max: 9.2 },
  },
  {
    name: "Roma",
    latitude: { min: 41.8, max: 41.95 },
    longitude: { min: 12.4, max: 12.6 },
  },
  {
    name: "Napoli",
    latitude: { min: 40.8, max: 40.9 },
    longitude: { min: 14.2, max: 14.3 },
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    latitude: { min: 43.7, max: 43.8 },
    longitude: { min: 11.2, max: 11.3 },
  },
  {
    name: "Torino",
    latitude: { min: 45.0, max: 45.1 },
    longitude: { min: 7.6, max: 7.7 },
  },
  {
    name: "Bologna",
    latitude: { min: 44.4, max: 44.5 },
    longitude: { min: 11.3, max: 11.4 },
  },
  {
    name: "Vene<PERSON>",
    latitude: { min: 45.4, max: 45.5 },
    longitude: { min: 12.3, max: 12.4 },
  },
];

// City-specific business density and pricing multipliers
export const cityCharacteristics = {
  Milano: { priceMultiplier: 1.3, businessDensity: 'high', touristLevel: 'medium' },
  Roma: { priceMultiplier: 1.2, businessDensity: 'high', touristLevel: 'high' },
  Napoli: { priceMultiplier: 0.9, businessDensity: 'medium', touristLevel: 'high' },
  Firenze: { priceMultiplier: 1.15, businessDensity: 'medium', touristLevel: 'very_high' },
  Torino: { priceMultiplier: 1.0, businessDensity: 'medium', touristLevel: 'low' },
  Bologna: { priceMultiplier: 1.05, businessDensity: 'medium', touristLevel: 'medium' },
  Venezia: { priceMultiplier: 1.4, businessDensity: 'low', touristLevel: 'very_high' },
} as const;

// Realistic street names for each city
export const cityStreets = {
  Milano: [
    "Via Montenapoleone", "Corso Buenos Aires", "Via Brera", "Via Paolo Sarpi",
    "Corso Magenta", "Via Torino", "Via Dante", "Corso di Porta Ticinese",
    "Via Garibaldi", "Corso Como", "Via della Spiga", "Via Sant'Andrea"
  ],
  Roma: [
    "Via del Corso", "Via Nazionale", "Via Veneto", "Via del Tritone",
    "Via Cola di Rienzo", "Via Ottaviano", "Via Giulia", "Via dei Cappuccini",
    "Via Sistina", "Via Margutta", "Via del Babuino", "Via Frattina"
  ],
  Napoli: [
    "Via Toledo", "Via Chiaia", "Corso Umberto I", "Via dei Tribunali",
    "Via San Gregorio Armeno", "Via Caracciolo", "Via Partenope", "Via Roma",
    "Spaccanapoli", "Via Santa Lucia", "Via Posillipo", "Corso Garibaldi"
  ],
  Firenze: [
    "Via de' Tornabuoni", "Via del Corso", "Via Roma", "Via Calzaiuoli",
    "Borgo San Lorenzo", "Via Santo Spirito", "Via Maggio", "Via de' Bardi",
    "Piazza della Signoria", "Via Ghibellina", "Via de' Neri", "Borgo Ognissanti"
  ],
  Torino: [
    "Via Roma", "Via Po", "Via Garibaldi", "Via Pietro Micca",
    "Corso Vittorio Emanuele II", "Via Lagrange", "Via Barbaroux", "Via Carlo Alberto",
    "Corso Re Umberto", "Via Cavour", "Via Bogino", "Via Monte di Pietà"
  ],
  Bologna: [
    "Via del Pratello", "Via Zamboni", "Via Rizzoli", "Via dell'Indipendenza",
    "Via Ugo Bassi", "Via Oberdan", "Via Santo Stefano", "Via Marsala",
    "Via D'Azeglio", "Via Farini", "Via Altabella", "Via de' Musei"
  ],
  Venezia: [
    "Calle Larga XXII Marzo", "Rio Terà San Leonardo", "Strada Nova", "Fondamenta delle Zattere",
    "Calle del Forno", "Campo Santa Margherita", "Rio Terà dei Pensieri", "Fondamenta San Giobbe",
    "Calle della Bissa", "Campo San Polo", "Fondamenta Cannaregio", "Lista di Spagna"
  ]
} as const;

export const zipCodes = {
  Milano: ["20121", "20122", "20123", "20124", "20125", "20144", "20146", "20154"],
  Roma: ["00100", "00118", "00121", "00122", "00123", "00125", "00126", "00132"],
  Napoli: ["80121", "80122", "80123", "80124", "80125", "80126", "80127", "80128"],
  Firenze: ["50121", "50122", "50123", "50124", "50125", "50126", "50127", "50128"],
  Torino: ["10121", "10122", "10123", "10124", "10125", "10126", "10127", "10128"],
  Bologna: ["40121", "40122", "40123", "40124", "40125", "40126", "40127", "40128"],
  Venezia: ["30121", "30122", "30123", "30124", "30125", "30126", "30127", "30128"],
} as const;

export const provinces = {
  Milano: "MI",
  Roma: "RM", 
  Napoli: "NA",
  Firenze: "FI",
  Torino: "TO",
  Bologna: "BO",
  Venezia: "VE",
} as const;