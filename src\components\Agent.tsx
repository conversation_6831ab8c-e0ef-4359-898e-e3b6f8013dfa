import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Phone, PhoneOff } from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { assistantAvatar } from "@/assets/assistant-avatar";

import { CreateAssistantDTO } from "@vapi-ai/web/dist/api";
import { voiceService } from "@/services";


export const interviewer: CreateAssistantDTO = {
  name: "Interviewer",
  firstMessage:
    "Ciao, sono Cami. Benvenuto sulla landing page di CatchUp. Come posso aiutarti?",
  transcriber: {
    provider: "deepgram",
    model: "nova-2",
    language: "en",
  },
   voice: {
     provider: "vapi",
     voiceId: "Hana",
    // language: "it",
  //   // stability: 0.4,
  //   // similarityBoost: 0.8,
     speed: 0.9,
  //   //style: 0.5,
    //useSpeakerBoost: true,
   },
  model: {
    provider: "google",
    model: "gemini-2.0-flash",
    messages: [
      {
        role: "system",
        content: `<role>
    <identity>Sei un assistente vocale AI per la landing page di CatchUp.</identity>
    <primary_goal>
        Il tuo obiettivo è accogliere i visitatori, spiegare cos’è CatchUp e guidarli in modo coinvolgente alla scoperta dei benefici della piattaforma, rispondendo in italiano a tutte le loro domande.
        Spingi gentilmente verso l’iscrizione o la richiesta di contatto.
    </primary_goal>
</role>

<static_context>
    <background_information>
        CatchUp è un marketplace B2C dove sia consumatori che aziende sono rappresentati da agenti AI.
        Questi agenti negoziano in tempo reale per offrire ai consumatori esattamente ciò che vogliono – quando, dove e come lo vogliono –
        e aiutano le aziende a massimizzare il flusso di clienti nei momenti a bassa affluenza con offerte, sconti e promozioni su misura.
    </background_information>
    <domain_details>
        I consumatori possono ottenere offerte iper-personalizzate con tempi rapidi e grande flessibilità.
        Le aziende possono impostare fasce orarie, condizioni e prezzi speciali per attrarre nuovi clienti in modo smart.
        L'assistente vocale deve rispondere sempre in italiano in modo naturale e accattivante.
    </domain_details>
</static_context>

<rules_specific_instructions>
    <do>
        - Rispondi nella lingua del cliente.
        - Mantieni un tono caloroso, vivace e professionale
        - Offri sempre un’opzione per essere ricontattati o iscriversi alla piattaforma
        - Evidenzia i vantaggi concreti per aziende e consumatori
    </do>
    <dont>
        - Non usare linguaggio tecnico o troppo complesso
        - Non fornire promesse non realistiche
    </dont>
</rules_specific_instructions>

<capabilities_tools>
    <tool_list>
        - Sistema di riconoscimento vocale (Voice Input API)
        - Database FAQ CatchUp
        - Lead Capture Form API
    </tool_list>
    <usage_instructions>
        - Usa il riconoscimento vocale per comprendere la richiesta dell’utente
        - Consulta il database FAQ per fornire risposte
        - Offri la possibilità di lasciare il numero/email per essere ricontattati
    </usage_instructions>
</capabilities_tools>

<chain_of_thought_process>
    <process_list>
        - Analisi dell’input
        - Identificazione dell’intento (business o consumatore)
        - Risposta mirata con valore aggiunto
        - Invito all’azione (es. “Vuoi ricevere un’offerta su misura?”)
        - Raccolta contatti
    </process_list>
    <process_usage_instructions>
        <input_analysis>
            Comprendi se chi parla è un potenziale cliente o un’azienda.
        </input_analysis>
        <decomposition>
            Identifica cosa vuole sapere: come funziona CatchUp? Come si iscrive? Quali vantaggi ha?
        </decomposition>
        <exploratory_reasoning>
            Crea una risposta su misura che valorizzi i benefici chiave.
        </exploratory_reasoning>
        <synthesis>
            Le tue risposte sono sembre brevi, con voce calda, coinvolgente, e sempre chiudendo con una call to action.
        </synthesis>
        <evaluation_and_refinement>
            Assicurati che la risposta sia utile, chiara e stimoli l’utente a continuare la conversazione.
        </evaluation_and_refinement>
    </process_usage_instructions>
</chain_of_thought_process>

<style_guidelines>
    <tone>Voce calda, professionale e dinamica. Usa metafore semplici per spiegare concetti innovativi. Risposte brevi e chiare.</tone>
</style_guidelines>`,
      },
    ],
  },
};

enum CallStatus {
  INACTIVE = "INACTIVE",
  CONNECTING = "CONNECTING",
  ACTIVE = "ACTIVE",
  FINISHED = "FINISHED",
}

interface SavedMessage {
  role: "user" | "system" | "assistant";
  content: string;
}

const Agent = () => {

  const [callStatus, setCallStatus] = useState<CallStatus>(CallStatus.INACTIVE);
  const [messages, setMessages] = useState<SavedMessage[]>([]);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isUserSpeaking, setIsUserSpeaking] = useState(false);

  useEffect(() => {
    const onCallStart = () => {
      setCallStatus(CallStatus.ACTIVE);
    };

    const onCallEnd = () => {
      setCallStatus(CallStatus.FINISHED);
    };

    const onMessage = (message: Message) => {
      if (message.type === "transcript") {
        // Se è un transcript dall'utente, significa che l'utente sta parlando
        if (message.role === "user") {
          setIsUserSpeaking(true);

          // Imposta un timer per disattivare l'indicatore dopo un breve periodo di inattività
          const timer = setTimeout(() => {
            setIsUserSpeaking(false);
          }, 1500); // 1.5 secondi di timeout

          // Pulisci il timer precedente se esiste
          return () => clearTimeout(timer);
        }

        // Se è un transcript finale, aggiungiamo il messaggio
        if (message.transcriptType === "final") {
          const newMessage = { role: message.role, content: message.transcript };
          setMessages((prev) => [...prev, newMessage]);
        }
      }
    };

    const onSpeechStart = () => {
      console.log("speech start");
      setIsSpeaking(true);
    };

    const onSpeechEnd = () => {
      console.log("speech end");
      setIsSpeaking(false);
    };



    const onError = (error: Error) => {
      console.log("Error:", error);
    };

    voiceService.on("call-start", onCallStart);
    voiceService.on("call-end", onCallEnd);
    voiceService.on("message", onMessage);
    voiceService.on("speech-start", onSpeechStart);
    voiceService.on("speech-end", onSpeechEnd);
    voiceService.on("error", onError);

    return () => {
      voiceService.off("call-start", onCallStart);
      voiceService.off("call-end", onCallEnd);
      voiceService.off("message", onMessage);
      voiceService.off("speech-start", onSpeechStart);
      voiceService.off("speech-end", onSpeechEnd);
      voiceService.off("error", onError);
    };
  }, []);
  const handleCall = async () => {
    setCallStatus(CallStatus.CONNECTING);

      await voiceService.start(interviewer);

  };

  const handleDisconnect = () => {
    setCallStatus(CallStatus.FINISHED);
    voiceService.stop();
  };



  useEffect(() => {
    const onCallStart = () => {
      setCallStatus(CallStatus.ACTIVE);
    };

    const onCallEnd = () => {
      setCallStatus(CallStatus.FINISHED);
    };

    const onMessage = (message: Message) => {
      if (message.type === "transcript" && message.transcriptType === "final") {
        const newMessage = { role: message.role, content: message.transcript };
        setMessages((prev) => [...prev, newMessage]);
      }
    };

    const onSpeechStart = () => {
      console.log("speech start");
      setIsSpeaking(true);
    };

    const onSpeechEnd = () => {
      console.log("speech end");
      setIsSpeaking(false);
    };

    const onError = (error: Error) => {
      console.log("Error:", error);
    };

    voiceService.on("call-start", onCallStart);
    voiceService.on("call-end", onCallEnd);
    voiceService.on("message", onMessage);
    voiceService.on("speech-start", onSpeechStart);
    voiceService.on("speech-end", onSpeechEnd);
    voiceService.on("error", onError);

    return () => {
      voiceService.off("call-start", onCallStart);
      voiceService.off("call-end", onCallEnd);
      voiceService.off("message", onMessage);
      voiceService.off("speech-start", onSpeechStart);
      voiceService.off("speech-end", onSpeechEnd);
      voiceService.off("error", onError);
    };
  }, []);


  // Effetto di pulsazione per attirare l'attenzione sul componente
  useEffect(() => {
    if (callStatus === CallStatus.INACTIVE) {
      const pulseTimer = setTimeout(() => {
        // Aggiungi un toast o un effetto visivo dopo che la pagina è caricata
        const event = new CustomEvent('showAssistantToast');
        window.dispatchEvent(event);
      }, 2000);

      return () => clearTimeout(pulseTimer);
    }
  }, [callStatus]);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      whileHover={{ scale: 1.02 }}
      className="relative w-full"
    >
      <Card className="overflow-hidden shadow-xl rounded-2xl border border-gray-100 w-full max-w-md mx-auto">
        <CardContent className="p-0">
          <div className="bg-white rounded-t-lg p-4 flex flex-wrap sm:flex-nowrap items-center gap-2">
            <div className="rounded-full overflow-hidden w-8 h-8 border border-gray-200">
              <img src={assistantAvatar.url} alt="Assistente" className="w-full h-full object-cover" />
            </div>
            <div className="flex-1 ml-3">
              <h4 className="font-medium text-sm">Assistente CatchUp</h4>
              <p className="text-xs text-gray-500">Sempre attivo</p>
            </div>
            <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-medium shrink-0">
              Online 24/7
            </span>
          </div>

          <div className="py-6 sm:py-10 px-4 sm:px-8 flex flex-col items-center justify-center text-center h-[380px] sm:h-[420px] relative overflow-hidden">
            {/* Indicatore pulsante animato intorno al microfono */}
            <div className="relative mb-10">
              {/* Effetto quando l'assistente sta parlando - onde animate blu */}
              {isSpeaking && (
                <div className="absolute inset-0 rounded-full scale-125 z-10">
                  <div className="absolute inset-0 rounded-full bg-blue-400 opacity-30 animate-ping" style={{ animationDuration: '1s' }}></div>
                  <div className="absolute inset-0 rounded-full bg-blue-300 opacity-40 animate-ping" style={{ animationDuration: '1.5s' }}></div>
                  <div className="absolute inset-0 rounded-full bg-blue-200 opacity-50 animate-ping" style={{ animationDuration: '2s' }}></div>
                  <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-300 to-blue-500 opacity-20 animate-pulse" style={{ animationDuration: '0.8s' }}></div>
                </div>
              )}

              {/* Effetto quando l'utente sta parlando - onde animate verdi */}
              {isUserSpeaking && (
                <div className="absolute inset-0 rounded-full scale-125 z-10">
                  <div className="absolute inset-0 rounded-full bg-green-400 opacity-30 animate-ping" style={{ animationDuration: '1s' }}></div>
                  <div className="absolute inset-0 rounded-full bg-green-300 opacity-40 animate-ping" style={{ animationDuration: '1.5s' }}></div>
                  <div className="absolute inset-0 rounded-full bg-green-200 opacity-50 animate-ping" style={{ animationDuration: '2s' }}></div>
                  <div className="absolute inset-0 rounded-full bg-gradient-to-r from-green-300 to-green-500 opacity-20 animate-pulse" style={{ animationDuration: '0.8s' }}></div>
                </div>
              )}

              {/* Effetto quando c'è silenzio e l'assistente è in attesa - pulsazione leggera viola */}
              {callStatus === CallStatus.ACTIVE && !isSpeaking && !isUserSpeaking && (
                <div className="absolute inset-0 rounded-full scale-110 z-10">
                  <div className="absolute inset-0 rounded-full bg-purple-200 opacity-40 animate-pulse" style={{ animationDuration: '3s' }}></div>
                  <div className="absolute inset-0 rounded-full bg-purple-300 opacity-20 animate-pulse" style={{ animationDuration: '2s' }}></div>
                </div>
              )}

              {/* Sfondo base sempre presente */}
              <div className="absolute inset-0 rounded-full bg-purple-100 scale-110"></div>

              {/* Bordo colorato che cambia in base allo stato */}
              <div className={`relative z-20 rounded-full overflow-hidden w-28 h-28 sm:w-36 sm:h-36 border-4 ${isSpeaking ? 'border-blue-500 shadow-lg shadow-blue-200' : isUserSpeaking ? 'border-green-500 shadow-lg shadow-green-200' : callStatus === CallStatus.ACTIVE ? 'border-purple-400' : 'border-purple-300'}`}>
                <img src={assistantAvatar.url} alt="Assistente" className="w-full h-full object-cover" />
              </div>
            </div>

            {/* Indicatore testuale dello stato - posizionato in modo assoluto per non spostare gli altri elementi */}
            <div className="absolute top-4 right-4 z-20">
              {callStatus === CallStatus.ACTIVE && (
                <span className={`inline-block px-4 py-1.5 rounded-full text-xs font-medium shadow-sm ${isSpeaking ? 'bg-blue-100 text-blue-800 animate-pulse' : isUserSpeaking ? 'bg-green-100 text-green-800 animate-pulse' : 'bg-purple-100 text-purple-800'}`}>
                  {isSpeaking ? 'Parlando...' : isUserSpeaking ? 'In ascolto...' : 'In attesa...'}
                </span>
              )}
            </div>

            <div className="flex-1 flex flex-col items-center justify-center mb-6">
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                Assistente Vocale Intelligente
              </h3>
              <p className="text-gray-600 mb-6 max-w-md mx-auto leading-relaxed">
                Parla con i tuoi clienti come una persona vera, gestisce prenotazioni e promuove i tuoi servizi.
              </p>
            </div>

            {callStatus !== CallStatus.ACTIVE ? (
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.98 }}
                className={callStatus === CallStatus.INACTIVE ? 'relative' : ''}
              >
                {/* Effetto spotlight solo quando inattivo */}
                {callStatus === CallStatus.INACTIVE && (
                  <div className="absolute -inset-3 bg-gradient-to-r from-primary-300 to-primary-400 rounded-full opacity-30 blur-lg animate-pulse"></div>
                )}
                <Button
                  size="lg"
                  onClick={() => handleCall()}
                  disabled={callStatus === CallStatus.CONNECTING}
                  className={cn(
                    "px-10 py-4 gap-3 transition-all relative z-10 shadow-lg text-base rounded-full w-full max-w-xs",
                    callStatus === CallStatus.CONNECTING ? "animate-pulse" : "bg-primary-600 hover:bg-primary-700 text-white font-medium"
                  )}
                >
                  <Phone className="w-5 h-5" />
                  {callStatus === CallStatus.INACTIVE || callStatus === CallStatus.FINISHED
                    ? "Prova l'Assistente Vocale"
                    : "Connessione..."}
                </Button>
              </motion.div>
            ) : (
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  variant="destructive"
                  size="lg"
                  onClick={() => handleDisconnect()}
                  className="px-10 py-4 gap-3 transition-all relative z-10 shadow-lg text-base rounded-full w-full max-w-xs font-medium"
                >
                  <PhoneOff className="w-5 h-5" />
                  Chiudi la chiamata
                </Button>
              </motion.div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default Agent;
