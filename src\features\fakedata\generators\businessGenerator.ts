import { faker } from '@faker-js/faker';
import { 
  BusinessCategory, 
  BusinessLifecycle, 
  CityCoordinates,
  FakeDataResult 
} from '../types';
import { 
  cities, 
  cityCharacteristics, 
  cityStreets, 
  zipCodes 
} from '../data/cities';
import { 
  businessNames, 
  businessSuffixes, 
  businessAgeDistribution,
  successLevelCharacteristics,
  businessDescriptions,
  italianFirstNames,
  italianLastNames,
  emailDomains,
  phoneFormats
} from '../data/businesses';
import { categoryOperatingHours } from '../data/daysNames';
import { supabase } from '@/integrations/supabase/client';
import { mapCategoryNameToType } from './utils/mapCategoryNameToType';

// Note: Setting faker locale to Italian (locale is set per method call)
// faker.setLocale('it') is not available in newer versions

export class BusinessGenerator {
  private categoryMapping: Record<string, BusinessCategory> = {};

  constructor() {
    this.initializeCategoryMapping();
  }

  private async initializeCategoryMapping() {
    try {
      const { data: categories } = await supabase
        .from('categories')
        .select('id, name');
      
      if (categories) {
        categories.forEach(cat => {
          this.categoryMapping[cat.id] = mapCategoryNameToType(cat.name);
        });
      }
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  }


  private generateBusinessLifecycle(): BusinessLifecycle {
    const rand = Math.random();
    let ageCategory: keyof typeof businessAgeDistribution;
    
    if (rand < businessAgeDistribution.new.weight) {
      ageCategory = 'new';
    } else if (rand < businessAgeDistribution.new.weight + businessAgeDistribution.established.weight) {
      ageCategory = 'established';  
    } else {
      ageCategory = 'mature';
    }

    const yearRange = businessAgeDistribution[ageCategory].yearRange;
    const establishedYear = faker.datatype.number({
      min: yearRange[0],
      max: yearRange[1]
    });

    // Success level based on age (newer businesses more likely to struggle)
    let successLevel: 'low' | 'medium' | 'high';
    const successRand = Math.random();
    
    if (ageCategory === 'new') {
      successLevel = successRand < 0.5 ? 'low' : successRand < 0.8 ? 'medium' : 'high';
    } else if (ageCategory === 'established') {
      successLevel = successRand < 0.2 ? 'low' : successRand < 0.7 ? 'medium' : 'high';
    } else { // mature
      successLevel = successRand < 0.1 ? 'low' : successRand < 0.6 ? 'medium' : 'high';
    }

    return {
      establishedYear,
      businessAge: ageCategory,
      successLevel
    };
  }

  private generateRealisticLocation(cityName: string): { lat: number; lng: number; address: string } {
    const city = cities.find(c => c.name === cityName);
    if (!city) {
      throw new Error(`City ${cityName} not found`);
    }

    // Generate coordinates within city bounds
    const lat = faker.datatype.float({
      min: city.latitude.min,
      max: city.latitude.max,
      precision: 0.0001
    });
    
    const lng = faker.datatype.float({
      min: city.longitude.min, 
      max: city.longitude.max,
      precision: 0.0001
    });

    // Generate realistic address
    const streets = cityStreets[cityName as keyof typeof cityStreets] || ['Via Roma', 'Via Centrale'];
    const street = faker.helpers.arrayElement(streets);
    const number = faker.datatype.number({ min: 1, max: 200 });
    const address = `${street}, ${number}`;

    return { lat, lng, address };
  }

  private generateBusinessName(categoryType: BusinessCategory): string {
    const names = businessNames[categoryType] || businessNames.default;
    const name = faker.helpers.arrayElement(names);
    
    // 30% chance to add suffix
    if (Math.random() < 0.3) {
      const suffix = faker.helpers.arrayElement(businessSuffixes);
      return `${name} ${suffix}`;
    }
    
    return name;
  }

  private generateContact(companyName: string): { email: string; phone: string; website: string } {
    const simplifiedName = companyName
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '.')
      .toLowerCase();

    const domain = faker.helpers.arrayElement(emailDomains);
    const email = `info@${simplifiedName}.${domain}`;
    
    const phoneFormat = faker.helpers.arrayElement(phoneFormats);
    const phone = faker.phone.number(phoneFormat);
    
    const websiteName = simplifiedName.replace(/\./g, '-');
    const website = `https://www.${websiteName}.${domain}`;

    return { email, phone, website };
  }

  private generateOperatingHours(categoryType: BusinessCategory) {
    const hours = categoryOperatingHours[categoryType] || categoryOperatingHours.bar;
    
    // Create schedule based on category defaults with some variation
    const schedule = [];
    
    for (let day = 1; day <= 7; day++) {
      if (hours.closedDays.includes(day)) {
        schedule.push({
          day,
          day_name: ['Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato', 'Domenica'][day - 1],
          time_slots: []
        });
        continue;
      }

      const isWeekend = day === 6 || day === 7; // Saturday or Sunday
      const dayHours = isWeekend ? hours.weekends : hours.weekdays;
      
      // Add some variation to opening hours (±30 minutes)
      const openHour = dayHours.open;
      const closeHour = dayHours.close;
      
      schedule.push({
        day,
        day_name: ['Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato', 'Domenica'][day - 1],
        time_slots: [{
          start_time: openHour,
          end_time: closeHour,
          available_seats: faker.datatype.number({ min: 5, max: 50 })
        }]
      });
    }

    return {
      schedule,
      exceptions: []
    };
  }

  public async generateBusiness(categoryId: string, cityName: string): Promise<any> {
    const categoryType = this.categoryMapping[categoryId] || 'default';
    const lifecycle = this.generateBusinessLifecycle();
    const location = this.generateRealisticLocation(cityName);
    const name = this.generateBusinessName(categoryType);
    const contact = this.generateContact(name);
    
    // City-specific pricing
    const cityChar = cityCharacteristics[cityName as keyof typeof cityCharacteristics];
    const priceMultiplier = cityChar?.priceMultiplier || 1.0;

    // Generate description based on success level
    const descriptions = businessDescriptions[categoryType] || businessDescriptions.default;
    let description = faker.helpers.arrayElement(descriptions);
    
    // Enhance description based on success level
    const successLevel = lifecycle.successLevel;
    if (successLevel === 'high') {
      description += " Locale di prestigio con servizio di eccellenza.";
    } else if (successLevel === 'low') {
      description += " Prezzi convenienti e atmosfera familiare.";
    }

    // Generate photos (more for successful businesses)
    const photoCount = faker.datatype.number({
      min: successLevelCharacteristics[successLevel].photoCount[0],
      max: successLevelCharacteristics[successLevel].photoCount[1]
    });
    
    const photos = Array.from({ length: photoCount }, () => 
      faker.image.business(640, 480, true)
    );

    // Generate realistic zip code
    const cityZips = zipCodes[cityName as keyof typeof zipCodes] || ['00100'];
    const zipCode = faker.helpers.arrayElement(cityZips);

    return {
      name,
      description,
      address: location.address,
      city: cityName,
      state: cityChar ? Object.keys(cityCharacteristics).find(k => k === cityName) : 'Italia',
      zip_code: zipCode,
      country: 'Italia',
      latitude: location.lat,
      longitude: location.lng,
      email: contact.email,
      phone: contact.phone,
      website: contact.website,
      category_id: categoryId,
      photos,
      fake: true,
      // Additional metadata for internal use
      _lifecycle: lifecycle,
      _success_level: successLevel,
      _price_multiplier: priceMultiplier
    };
  }

  public async generateMultipleBusinesses(
    categoryId: string, 
    cityName: string, 
    count: number,
    onProgress?: (current: number, total: number) => void
  ): Promise<FakeDataResult> {
    const result: FakeDataResult = {
      created: 0,
      errors: [],
      warnings: []
    };

    for (let i = 0; i < count; i++) {
      try {
        const business = await this.generateBusiness(categoryId, cityName);
        
        const { error } = await supabase
          .from('businesses')
          .insert(business);

        if (error) {
          result.errors.push(`Business ${i + 1}: ${error.message}`);
        } else {
          result.created++;
        }

        onProgress?.(i + 1, count);
        
        // Add small delay to prevent overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        result.errors.push(`Business ${i + 1}: ${error}`);
      }
    }

    return result;
  }
}