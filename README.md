# Welcome to your Lovable project
https://showtime-ai.lovable.app/movie-management
## Project info

**URL**: https://lovable.dev/projects/15d3a627-7fe5-438c-bd32-c4e03a538512

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/15d3a627-7fe5-438c-bd32-c4e03a538512) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with .

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/15d3a627-7fe5-438c-bd32-c4e03a538512) and click on Share -> Publish.

## I want to use a custom domain - is that possible?

We don't support custom domains (yet). If you want to deploy your project under your own domain then we recommend using Netlify. Visit our docs for more details: [Custom domains](https://docs.lovable.dev/tips-tricks/custom-domain/)

## Features

### Deal Management

The application provides a comprehensive deal management system that allows business owners to create, view, edit, and manage special offers or deals for their customers.

#### Creating a New Deal

The `NewDeal` component (`src/pages/NewDeal.tsx`) provides a user-friendly interface for creating new deals with the following features:

1. **Basic Deal Information**:
   - Title and description
   - Original price and discount percentage
   - Automatically calculated discounted price (read-only)
   - Start and end dates
   - Business selection (automatically selected if user has only one business)

2. **Pricing Model**:
   - Users input the original price and discount percentage
   - The system automatically calculates the discounted price
   - The discounted price is displayed as read-only
   - This ensures consistency in pricing calculations

3. **Image Management**:
   - Add multiple images via URLs
   - Preview images in a grid layout
   - Remove images as needed

4. **Time Slot Configuration**:
   - Enable/disable days of the week
   - Add multiple time ranges for each day
   - Configure start and end times for each range

The form includes validation to ensure all required fields are filled before submission. Upon successful creation, the user is redirected to the deal detail page.

#### Viewing and Managing Deals

The `Deals` component (`src/pages/Deals.tsx`) displays all deals owned by the current user, showing key information such as:
- Deal title
- Business name
- Original and discounted prices
- Expiration date

Users can click on a deal card to view its details or click the "Nuova Offerta" button to create a new deal.

## Data Structures

### Time Slots

The application uses a specific data structure for representing time slots in deals. This structure is used in the `DealDetail` component to display weekly schedules and exceptions.

```typescript
// Time slots data structure
{
  schedule: [
    { day: 0, day_name: "Domenica", time_slots: Array(0) },
    { day: 1, day_name: "Lunedì", time_slots: Array(2) },
    { day: 2, day_name: "Martedì", time_slots: Array(0) },
    { day: 3, day_name: "Mercoledì", time_slots: Array(1) },
    { day: 4, day_name: "Giovedì", time_slots: Array(0) },
    { day: 5, day_name: "Venerdì", time_slots: Array(0) },
    { day: 6, day_name: "Sabato", time_slots: Array(0) }
  ],
  exceptions: [
    "2005-12-12"  // or { "0": "2005-12-12" }
  ]
}
```

- `schedule`: An array of objects representing each day of the week (0 = Sunday, 1 = Monday, etc.)
  - `day`: Number representing the day of the week (0-6)
  - `day_name`: String name of the day in Italian
  - `time_slots`: Array of time slots for that day. If empty, the day is considered disabled.

- `exceptions`: An array of dates that are exceptions to the regular schedule. These dates have no availability.

This structure is used in the `TimeSlots` component to render the weekly schedule and exceptions for each deal.

### Editing Time Slots

The application provides a user interface for editing time slots in the `DealDetail` component. Users can:

1. **Enable/Disable Days**: Toggle the availability of each day of the week.
2. **Add/Remove Time Ranges**: For each enabled day, add or remove specific time ranges (e.g., 9:00-12:00, 14:00-18:00).
3. **Add/Remove Exception Dates**: Specify dates that have no availability, regardless of the weekly schedule.

#### Implementation Details

The time slots editing feature is implemented using:

- **React State Management**: Local state for managing the UI during editing.
- **React Query Mutations**: For updating the time slots data in the database.
- **Supabase**: For persisting the changes to the database.

The editing workflow is as follows:

1. User clicks "Modifica Orari" button to enter edit mode.
2. User makes changes to the time slots configuration.
3. User clicks "Salva Modifiche" to save changes or "Annulla" to discard changes.
4. On save, the component formats the data and sends it to the server using a React Query mutation.
5. On success, the UI is updated to reflect the changes and the user is shown a success message.

#### Component Props

The `TimeSlots` component accepts the following props:

```typescript
interface TimeSlotsProps {
  deal: any; // The deal object containing time_slots data
  updateTimeSlots: (timeSlots: any) => void; // Function to update time slots in the database
  isUpdating: boolean; // Whether the time slots are currently being updated
}
```

## Authentication and Route Protection

The application implements a comprehensive authentication system with route protection to ensure that only authenticated users can access protected routes.

### Authentication Flow

The authentication system is built using Supabase Auth and consists of the following components:

1. **AuthContext** (`src/contexts/AuthContext.tsx`): A React context that provides authentication state and functions to the entire application.
   - Manages the current user state
   - Handles authentication loading state
   - Listens for authentication state changes

2. **Auth Page** (`src/pages/Auth.tsx`): Handles user sign-in and sign-up.
   - Provides a form for email/password authentication
   - Redirects authenticated users to the dashboard or the page they were trying to access
   - Handles authentication errors and success messages

### Route Protection

The application uses a `ProtectedRoute` component to guard routes that require authentication:

1. **ProtectedRoute Component** (`src/components/ProtectedRoute.tsx`): A wrapper component that checks if the user is authenticated.
   - If the user is authenticated, it renders the child routes
   - If the user is not authenticated, it redirects to the login page
   - Preserves the original URL the user was trying to access for post-login redirection
   - Shows a loading indicator while checking authentication status

2. **Implementation in App.tsx**: The router configuration in `src/App.tsx` uses the `ProtectedRoute` component to protect routes.

```tsx
// Example of protected routes in App.tsx
<Routes>
  {/* Public routes */}
  <Route path="/" element={<Index />} />
  <Route path="/auth" element={<Auth />} />
  
  {/* Protected routes */}
  <Route element={<ProtectedRoute />}>
    <Route path="/dashboard" element={<Dashboard />} />
    <Route path="/deals" element={<Deals />} />
    <Route path="/profile" element={<Profile />} />
    {/* Other protected routes */}
  </Route>
  
  {/* Catch-all route */}
  <Route path="*" element={<NotFound />} />
</Routes>
```

### Usage

To add a new protected route:

1. Add the new route as a child of the `<Route element={<ProtectedRoute />}>` element in `src/App.tsx`.
2. The route will automatically be protected and require authentication.

To add a new public route:

1. Add the new route outside the `<Route element={<ProtectedRoute />}>` element in `src/App.tsx`.
2. The route will be accessible without authentication.

### Redirecting After Login

When a user tries to access a protected route without authentication:

1. They are redirected to the login page (`/auth`)
2. The original URL they were trying to access is stored in the location state
3. After successful login, they are redirected back to the original URL

This provides a seamless user experience while maintaining security.
