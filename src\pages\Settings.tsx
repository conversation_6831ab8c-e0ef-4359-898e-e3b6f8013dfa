import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { toast } from "sonner";
import { User, Building2, BellRing, ShieldCheck, Loader2, Mail, Lock } from "lucide-react";

import MainLayout from "@/layouts/MainLayout";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { useAuth } from "@/contexts/AuthContext";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { supabase } from "@/integrations/supabase/client";
import { useBusinessStore } from "@/store/businessStore";
import { useUserDetails } from "@/components/sidebar/hooks/useUserDetails";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { EmailChangeForm } from "@/components/settings/EmailChangeForm";
import { PasswordChangeForm } from "@/components/settings/PasswordChangeForm";

const userFormSchema = z.object({
  firstName: z.string().min(2, {
    message: "Il nome deve contenere almeno 2 caratteri.",
  }),
  lastName: z.string().min(2, {
    message: "Il cognome deve contenere almeno 2 caratteri.",
  }),
  displayName: z.string().min(2, {
    message: "Il nome visualizzato deve contenere almeno 2 caratteri.",
  }).optional(),
  phoneNumber: z.string().optional(),
  notificationsEnabled: z.boolean().default(true),
  locationEnabled: z.boolean().default(true),
  defaultBusinessId: z.string().optional(),
  businessMode: z.boolean().default(false),
});

const businessFormSchema = z.object({
  name: z.string().min(2, {
    message: "Il nome dell'attività deve contenere almeno 2 caratteri.",
  }),
  description: z.string().optional(),
  address: z.string().min(5, {
    message: "L'indirizzo deve contenere almeno 5 caratteri.",
  }).optional(),
  city: z.string().optional(),
  zipCode: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email({
    message: "Inserisci un indirizzo email valido.",
  }).optional(),
  website: z.string().url({
    message: "Inserisci un URL valido.",
  }).optional(),
  taxId: z.string().optional(),
  registrationNumber: z.string().optional(),
});

type UserFormValues = z.infer<typeof userFormSchema>;
type BusinessFormValues = z.infer<typeof businessFormSchema>;

const Settings = () => {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isBusinessSubmitting, setIsBusinessSubmitting] = useState(false);
  const { businesses, selectedBusiness, fetchBusinesses, setDefaultBusiness } = useBusinessStore();
  const { userDetails, isLoading: userLoading } = useUserDetails();

  const defaultValues: Partial<UserFormValues> = {
    firstName: "",
    lastName: "",
    displayName: "",
    phoneNumber: "",
    notificationsEnabled: true,
    locationEnabled: true,
    defaultBusinessId: selectedBusiness?.id || "",
    businessMode: false,
  };

  const defaultBusinessValues: Partial<BusinessFormValues> = {
    name: "",
    description: "",
    address: "",
    city: "",
    zipCode: "",
    state: "",
    country: "",
    phone: "",
    email: "",
    website: "",
    taxId: "",
    registrationNumber: "",
  };

  const form = useForm<UserFormValues>({
    resolver: zodResolver(userFormSchema),
    defaultValues,
  });

  const businessForm = useForm<BusinessFormValues>({
    resolver: zodResolver(businessFormSchema),
    defaultValues: defaultBusinessValues,
  });

  useEffect(() => {
    const fetchUserDetails = async () => {
      if (!user) return;

      try {
        const { data, error } = await supabase
          .from('users_with_details')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) throw error;

        if (data) {
          form.reset({
            firstName: data.first_name || "",
            lastName: data.last_name || "",
            displayName: data.display_name || "",
            phoneNumber: data.phone_number || "",
            notificationsEnabled: true,
            locationEnabled: true,
            defaultBusinessId: data.default_business_id || selectedBusiness?.id || "",
            businessMode: data.business_mode || false,
          });
        }
      } catch (error) {
        console.error('Errore nel caricamento dei dettagli utente:', error);
        toast.error("Si è verificato un errore durante il caricamento dei dati");
      }
    };

    fetchUserDetails();
  }, [user, form, selectedBusiness]);

  useEffect(() => {
    const loadBusinessDetails = async () => {
      if (!selectedBusiness) return;

      try {
        const { data, error } = await supabase
          .from('businesses')
          .select('*')
          .eq('id', selectedBusiness.id)
          .single();

        if (error) throw error;

        if (data) {
          businessForm.reset({
            name: data.name || "",
            description: data.description || "",
            address: data.address || "",
            city: data.city || "",
            zipCode: data.zip_code || "",
            state: data.state || "",
            country: data.country || "",
            phone: data.phone || "",
            email: data.email || "",
            website: data.website || "",
            taxId: data.tax_id || "",
            registrationNumber: data.registration_number || "",
          });
        }
      } catch (error) {
        console.error('Errore nel caricamento dei dettagli attività:', error);
        toast.error("Si è verificato un errore durante il caricamento dei dati");
      }
    };

    loadBusinessDetails();
  }, [selectedBusiness, businessForm]);

  const getUserInitials = () => {
    if (!userDetails) return 'U';

    if (userDetails.first_name && userDetails.last_name) {
      return `${userDetails.first_name.charAt(0)}${userDetails.last_name.charAt(0)}`;
    } else if (userDetails.display_name) {
      return userDetails.display_name.charAt(0);
    } else if (userDetails.email) {
      return userDetails.email.charAt(0).toUpperCase();
    }
    return 'U';
  };

  const onSubmit = async (data: UserFormValues) => {
    setIsSubmitting(true);

    try {
      const isDefaultBusinessChanged = data.defaultBusinessId !== userDetails?.default_business_id;

      const { error } = await supabase
        .from('user_details')
        .upsert({
          id: user?.id,
          first_name: data.firstName,
          last_name: data.lastName,
          display_name: data.displayName,
          phone_number: data.phoneNumber,
          location_enabled: data.locationEnabled,
          business_mode: data.businessMode,
          default_business_id: data.defaultBusinessId,
        });

      if (error) throw error;

      if (isDefaultBusinessChanged && data.defaultBusinessId && user) {
        const defaultBusiness = businesses.find(b => b.id === data.defaultBusinessId);
        if (defaultBusiness) {
          await setDefaultBusiness(user.id, defaultBusiness);
        }
      }

      toast.success("Le tue impostazioni sono state salvate con successo");
    } catch (error) {
      console.error('Errore nel salvataggio delle impostazioni:', error);
      toast.error("Si è verificato un errore durante il salvataggio delle impostazioni");
    } finally {
      setIsSubmitting(false);
    }
  };

  const onBusinessSubmit = async (data: BusinessFormValues) => {
    if (!selectedBusiness) {
      toast.error("Seleziona un'attività prima di salvare le modifiche");
      return;
    }

    setIsBusinessSubmitting(true);

    try {
      const { error } = await supabase
        .from('businesses')
        .update({
          name: data.name,
          description: data.description,
          address: data.address,
          city: data.city,
          zip_code: data.zipCode,
          state: data.state,
          country: data.country,
          phone: data.phone,
          email: data.email,
          website: data.website,
          tax_id: data.taxId,
          registration_number: data.registrationNumber,
        })
        .eq('id', selectedBusiness.id);

      if (error) throw error;

      if (user) {
        await fetchBusinesses(user.id);
      }

      toast.success("Le informazioni dell'attività sono state aggiornate con successo");
    } catch (error) {
      console.error('Errore nell\'aggiornamento dell\'attività:', error);
      toast.error("Si è verificato un errore durante l'aggiornamento dell'attività");
    } finally {
      setIsBusinessSubmitting(false);
    }
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-6 max-w-5xl">
        <h1 className="text-2xl font-semibold mb-6">Impostazioni</h1>

        <Tabs defaultValue="account" className="space-y-4">
          <TabsList>
            <TabsTrigger value="account" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Profilo
            </TabsTrigger>
            <TabsTrigger value="business" className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Attività
            </TabsTrigger>
            <TabsTrigger value="security" className="flex items-center gap-2">
              <ShieldCheck className="h-4 w-4" />
              Sicurezza
            </TabsTrigger>
            <TabsTrigger value="notifications" className="flex items-center gap-2">
              <BellRing className="h-4 w-4" />
              Notifiche
            </TabsTrigger>
            <TabsTrigger value="privacy" className="flex items-center gap-2">
              <ShieldCheck className="h-4 w-4" />
              Privacy
            </TabsTrigger>
          </TabsList>

          <TabsContent value="account" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle>Informazioni Personali</CardTitle>
                  <CardDescription>
                    Gestisci le tue informazioni personali e come appari su BookingHub
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {userLoading ? (
                    <div className="flex justify-center p-6">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  ) : (
                    <Form {...form}>
                      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="firstName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Nome</FormLabel>
                                <FormControl>
                                  <Input placeholder="Inserisci il tuo nome" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="lastName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Cognome</FormLabel>
                                <FormControl>
                                  <Input placeholder="Inserisci il tuo cognome" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={form.control}
                          name="displayName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Nome visualizzato</FormLabel>
                              <FormControl>
                                <Input placeholder="Come vuoi essere chiamato" {...field} />
                              </FormControl>
                              <FormDescription>
                                Questo è il nome che gli altri utenti vedranno.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="phoneNumber"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Telefono</FormLabel>
                              <FormControl>
                                <Input
                                  type="tel"
                                  placeholder="Il tuo numero di telefono"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="defaultBusinessId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Attività predefinita</FormLabel>
                              <Select
                                onValueChange={(value) => {
                                  console.log(`Changing default business to: ${value}`);
                                  field.onChange(value);
                                }}
                                defaultValue={field.value}
                                value={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Seleziona un'attività" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {businesses.map((business) => (
                                    <SelectItem key={business.id} value={business.id}>
                                      {business.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                Questa attività sarà selezionata automaticamente quando accedi.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="businessMode"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">Modalità Business</FormLabel>
                                <FormDescription>
                                  Attiva questa opzione per utilizzare l'app come gestore di attività
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <Button type="submit" disabled={isSubmitting}>
                          {isSubmitting ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Aggiornamento...
                            </>
                          ) : (
                            "Salva modifiche"
                          )}
                        </Button>
                      </form>
                    </Form>
                  )}
                </CardContent>
              </Card>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Il tuo avatar</CardTitle>
                  </CardHeader>
                  <CardContent className="flex flex-col items-center">
                    <Avatar className="h-32 w-32 mb-4">
                      <AvatarImage src={userDetails?.avatar_url || ""} />
                      <AvatarFallback className="text-3xl bg-primary/10 text-primary">
                        {getUserInitials()}
                      </AvatarFallback>
                    </Avatar>
                    <Button variant="outline" className="w-full" disabled>
                      Cambia avatar
                    </Button>
                    <p className="text-xs text-muted-foreground mt-2 text-center">
                      La funzionalità di caricamento avatar sarà disponibile a breve.
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Account</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div>
                      <Label className="text-muted-foreground">ID Utente</Label>
                      <p className="text-sm text-ellipsis overflow-hidden">{userDetails?.id || "N/A"}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Data di registrazione</Label>
                      <p className="text-sm">
                        {userDetails?.registered_at
                          ? new Date(userDetails.registered_at).toLocaleDateString('it-IT')
                          : "N/A"}
                      </p>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button
                      variant="destructive"
                      className="w-full"
                      onClick={async () => {
                        try {
                          await supabase.auth.signOut();
                          toast.success("Sei stato disconnesso con successo.");
                        } catch (error) {
                          console.error("Errore durante il logout:", error);
                        }
                      }}
                    >
                      Disconnetti
                    </Button>
                  </CardFooter>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="business" className="space-y-4">
            <div className="bg-white rounded-lg shadow-sm p-6 md:p-8">
              <h2 className="text-xl font-medium mb-6">Dettagli Attività</h2>

              {!selectedBusiness ? (
                <div className="p-6 text-center text-muted-foreground rounded-lg border">
                  <p className="mb-3">Nessuna attività selezionata</p>
                  <p className="text-sm">Seleziona un'attività dal selettore nella barra laterale per modificarne i dettagli</p>
                </div>
              ) : (
                <Form {...businessForm}>
                  <form onSubmit={businessForm.handleSubmit(onBusinessSubmit)} className="space-y-6">
                    <FormField
                      control={businessForm.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Nome attività</FormLabel>
                          <FormControl>
                            <Input placeholder="Nome della tua attività" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={businessForm.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Descrizione</FormLabel>
                          <FormControl>
                            <Textarea placeholder="Descrivi brevemente la tua attività" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={businessForm.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input type="email" placeholder="Email di contatto" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={businessForm.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Telefono</FormLabel>
                            <FormControl>
                              <Input type="tel" placeholder="Numero di telefono" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={businessForm.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Indirizzo</FormLabel>
                          <FormControl>
                            <Input placeholder="Indirizzo completo" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={businessForm.control}
                        name="city"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Città</FormLabel>
                            <FormControl>
                              <Input placeholder="Città" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={businessForm.control}
                        name="zipCode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>CAP</FormLabel>
                            <FormControl>
                              <Input placeholder="Codice postale" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={businessForm.control}
                        name="state"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Provincia</FormLabel>
                            <FormControl>
                              <Input placeholder="Provincia" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={businessForm.control}
                        name="country"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Paese</FormLabel>
                            <FormControl>
                              <Input placeholder="Paese" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={businessForm.control}
                      name="website"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Sito web</FormLabel>
                          <FormControl>
                            <Input placeholder="https://www.esempio.com" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={businessForm.control}
                        name="taxId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Partita IVA</FormLabel>
                            <FormControl>
                              <Input placeholder="Partita IVA" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={businessForm.control}
                        name="registrationNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Numero REA</FormLabel>
                            <FormControl>
                              <Input placeholder="Numero registro imprese" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <Button type="submit" disabled={isBusinessSubmitting}>
                      {isBusinessSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Aggiornamento...
                        </>
                      ) : (
                        "Salva modifiche"
                      )}
                    </Button>
                  </form>
                </Form>
              )}
            </div>
          </TabsContent>

          <TabsContent value="security" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <EmailChangeForm currentEmail={user?.email || ""} />
              <PasswordChangeForm />
            </div>
          </TabsContent>

          <TabsContent value="notifications" className="space-y-4">
            <div className="bg-white rounded-lg shadow-sm p-6 md:p-8">
              <h2 className="text-xl font-medium mb-4">Preferenze Notifiche</h2>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="notificationsEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Notifiche</FormLabel>
                          <FormDescription>
                            Ricevi notifiche su nuove offerte e prenotazioni.
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? "Salvataggio..." : "Salva preferenze"}
                  </Button>
                </form>
              </Form>
            </div>
          </TabsContent>

          <TabsContent value="privacy" className="space-y-4">
            <div className="bg-white rounded-lg shadow-sm p-6 md:p-8">
              <h2 className="text-xl font-medium mb-4">Impostazioni Privacy</h2>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="locationEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Posizione</FormLabel>
                          <FormDescription>
                            Permetti all'app di accedere alla tua posizione per offerte personalizzate.
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? "Salvataggio..." : "Salva preferenze"}
                  </Button>
                </form>
              </Form>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default Settings;
