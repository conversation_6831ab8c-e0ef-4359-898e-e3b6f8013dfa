import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Play, Video } from "lucide-react";

interface TeamMemberVideoProps {
  name: string;
  role: string;
  videoId?: string; // ID del video (YouTube, Vimeo, ecc.) - opzionale perché i video non sono ancora pronti
  videoPlaceholder: string; // URL dell'immagine placeholder
}

const TeamMemberVideo: React.FC<TeamMemberVideoProps> = ({
  name,
  role,
  videoId,
  videoPlaceholder,
}) => {
  // Estrai il nome dal formato "Nome - Ruolo"
  const firstName = name.split(" - ")[0];

  return (
    <Dialog>
      <DialogTrigger asChild>
        <div className="relative">
          <Button variant="outline" size="sm" className="rounded-full px-4 py-1 text-primary-600 border-primary-200 hover:bg-primary-50 hover:text-primary-700 flex items-center gap-2">
            <Video className="h-4 w-4" />
            <span>Video</span>
          </Button>
          {!videoId && (
            <span className="absolute -top-2 -right-2 bg-amber-500 text-white text-xs px-2 py-0.5 rounded-full animate-pulse">
              In arrivo
            </span>
          )}
        </div>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold mb-2">
            {firstName} si presenta
          </DialogTitle>
        </DialogHeader>
        <div className="mt-2">
          {videoId ? (
            // Se il video è disponibile, mostralo
            <div className="aspect-video w-full">
              <iframe
                width="100%"
                height="100%"
                src={`https://www.youtube.com/embed/${videoId}`}
                title={`${name} Video`}
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                className="rounded-lg"
              ></iframe>
            </div>
          ) : (
            // Altrimenti, mostra un placeholder con un messaggio
            <div className="aspect-video w-full relative rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center">
              <img
                src={videoPlaceholder}
                alt={`${name} Placeholder`}
                className="absolute inset-0 w-full h-full object-cover opacity-50"
              />
              <div className="absolute inset-0 bg-black/30"></div>
              <div className="relative z-10 text-center p-6">
                <div className="w-20 h-20 rounded-full bg-primary-600/80 flex items-center justify-center mx-auto mb-4 animate-pulse">
                  <Play className="h-10 w-10 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Video in preparazione</h3>
                <p className="text-white/90">
                  Stiamo preparando un video di presentazione per {firstName}. Torna presto per scoprire di più!
                </p>
                <div className="mt-6">
                  <span className="bg-white/20 text-white px-4 py-2 rounded-full text-sm inline-block">
                    Disponibile a breve
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TeamMemberVideo;
