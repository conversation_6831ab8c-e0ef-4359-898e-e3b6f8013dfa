-- Update the pricing_tiers table to use ai_business_agents IDs instead of enum
-- First, drop the existing allowed_agents column
ALTER TABLE public.pricing_tiers DROP COLUMN allowed_agents;

-- Add new allowed_agents column as UUID array referencing ai_business_agents
ALTER TABLE public.pricing_tiers ADD COLUMN allowed_agents UUID[] DEFAULT '{}';

-- Add comment to clarify the relationship
COMMENT ON COLUMN public.pricing_tiers.allowed_agents IS 'Array of UUIDs referencing ai_business_agents.id';

-- You can optionally add a check constraint to ensure all UUIDs exist in ai_business_agents
-- ALTER TABLE public.pricing_tiers ADD CONSTRAINT pricing_tiers_allowed_agents_fkey 
-- CHECK (allowed_agents <@ (SELECT array_agg(id) FROM ai_business_agents));