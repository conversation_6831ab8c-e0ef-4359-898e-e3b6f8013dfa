# Multi-Tenant Booking Hub - Project Rules

## Code Patterns

### Component Structure
- All React components should use the functional component pattern with hooks
- Components should import dependencies in the following order: React, external libraries, internal components, styles
- Each component should have a clear, single responsibility
- Prefer composition over inheritance for component reuse

### Naming Conventions
- React components: PascalCase (e.g., `ProfileCard.tsx`)
- Hooks: camelCase with 'use' prefix (e.g., `useUserDetails.ts`)
- Helper functions: camelCase (e.g., `formatDateTime.ts`)
- Constants: UPPER_SNAKE_CASE (e.g., `MAX_DEALS_PER_PAGE`)
- Database tables: snake_case (e.g., `user_details`)

### Form Handling
- Always use react-hook-form with zod validation
- Form schemas should be defined at the top of the component file
- Use FormField components from shadcn/ui for all form inputs
- Always show validation feedback to users
- Include loading states during form submission

### Styling Approach
- Use Tailwind CSS utility classes for styling
- Follow mobile-first responsive design principles
- Use shadcn/ui components as the foundation for UI elements
- Keep component-specific styles close to the component

## Project Preferences

### Code Organization
- Group related components in feature-based directories
- Place reusable components in the `components` directory
- Keep hooks in a separate `hooks` directory
- Organize pages in the `pages` directory

### State Management
- Use React Query for server state management
- Use React Context for application-wide state
- Use local state for component-specific concerns
- Avoid prop drilling by using context or custom hooks

### Security Practices
- Always validate user input on both client and server
- Use Supabase RLS policies for data access control
- Never expose sensitive data in client-side code
- Handle authentication state consistently throughout the app

### Performance Considerations
- Use React Query's caching capabilities
- Implement proper memoization for expensive calculations
- Use code splitting for route-based components
- Optimize images and assets for faster loading

## Development Workflow

### Git Workflow
- Use descriptive branch names (feature/, bugfix/, hotfix/)
- Write clear commit messages with context
- Keep pull requests focused on a single concern
- Always run tests before submitting code

### Testing Strategy
- Unit test critical business logic
- Component tests for complex UI components
- Integration tests for key user flows
- Manual testing for complex interactions

### Documentation
- Include JSDoc comments for functions and components
- Update README with new features and dependencies
- Document API endpoints and expected responses
- Keep technical documentation up-to-date

## Project-Specific Guidelines

### Multi-Tenant Implementation
- All database queries must respect tenant isolation
- Use Supabase RLS policies to enforce data separation
- Include tenant_id in all relevant database tables
- Validate tenant access in business logic

### Deal Management
- Time slots should follow the established JSON structure
- All deals must include pricing information and time slots
- Deal status transitions should follow the defined workflow
- Images should be optimized before storage

### Booking System
- Validate all booking requests against available time slots
- Handle booking conflicts with proper error messages
- Always confirm bookings with notifications
- Implement proper status transitions for bookings

### Internationalization
- All user-facing text should use Italian as the primary language
- Plan for English support in future iterations
- Keep text in separate language files
- Use relative date/time formatting for better localization
