export type UserWithDetails = {
  id: string;
  email: string;
  registered_at: string;
  first_name: string | null;
  last_name: string | null;
  display_name: string | null;
  avatar_url: string | null;
  phone_number: string | null;
  business_mode?: boolean;
  default_business_id?: string | null;
  role: 'user' | 'admin' | 'business_owner';
};

export type Document = {
  id: string;
  name: string;
  file_url: string;
  file_type: string | null;
  file_size: number | null;
  business_id: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  content?: string | null;
};

export type DocumentEmbedding = {
  id: string;
  document_id: string;
  text_content: string;
  embedding: number[];
  version: number;
  created_at: string;
};

export type DocumentWithEmbedding = Document & {
  embedding?: DocumentEmbedding;
  text_content?: string | null;
  match_type?: 'text' | 'semantic' | 'fallback';
  similarity?: number;
};

// Definizione del tipo Json che rispecchia quello di Supabase
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];
