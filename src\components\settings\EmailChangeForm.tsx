
import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Loader2, Mail } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuthUpdate } from "@/hooks/useAuthUpdate";

const emailChangeSchema = z.object({
  newEmail: z.string().email({
    message: "Inserisci un indirizzo email valido.",
  }),
  confirmEmail: z.string().email({
    message: "Inserisci un indirizzo email valido.",
  }),
}).refine((data) => data.newEmail === data.confirmEmail, {
  message: "Gli indirizzi email non corrispondono",
  path: ["confirmEmail"],
});

type EmailChangeValues = z.infer<typeof emailChangeSchema>;

interface EmailChangeFormProps {
  currentEmail: string;
}

export const EmailChangeForm = ({ currentEmail }: EmailChangeFormProps) => {
  const { isUpdating, updateEmail } = useAuthUpdate();

  const form = useForm<EmailChangeValues>({
    resolver: zodResolver(emailChangeSchema),
    defaultValues: {
      newEmail: "",
      confirmEmail: "",
    },
  });

  const onSubmit = async (data: EmailChangeValues) => {
    const success = await updateEmail(data.newEmail);
    if (success) {
      form.reset();
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          Cambia Email
        </CardTitle>
        <CardDescription>
          Aggiorna il tuo indirizzo email. Riceverai un'email di conferma al nuovo indirizzo.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-4 p-3 bg-muted rounded-lg">
          <p className="text-sm text-muted-foreground">Email attuale:</p>
          <p className="font-medium">{currentEmail}</p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="newEmail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nuova Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Inserisci la nuova email"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmEmail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Conferma Nuova Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Conferma la nuova email"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" disabled={isUpdating} className="w-full">
              {isUpdating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Aggiornamento...
                </>
              ) : (
                "Aggiorna Email"
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
