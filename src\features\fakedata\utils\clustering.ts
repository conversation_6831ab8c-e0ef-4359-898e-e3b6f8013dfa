import { faker } from '@faker-js/faker';
import { CityCoordinates } from '../types';

// Business clustering utilities for realistic geographic distribution

export class BusinessClusteringUtils {
  
  // Generate clustered business locations (businesses tend to cluster in commercial areas)
  static generateClusteredLocations(
    city: CityCoordinates,
    count: number,
    clusteringFactor: number = 0.7 // 0 = completely random, 1 = highly clustered
  ): Array<{ lat: number; lng: number }> {
    const locations: Array<{ lat: number; lng: number }> = [];
    
    // Generate cluster centers (commercial areas)
    const numClusters = Math.max(1, Math.floor(count * 0.3)); // 30% of businesses define cluster centers
    const clusterCenters = this.generateClusterCenters(city, numClusters);
    
    for (let i = 0; i < count; i++) {
      let lat: number, lng: number;
      
      if (Math.random() < clusteringFactor && clusterCenters.length > 0) {
        // Place near a cluster center
        const cluster = faker.helpers.arrayElement(clusterCenters);
        const radius = 0.01; // ~1km radius
        
        lat = cluster.lat + (Math.random() - 0.5) * radius;
        lng = cluster.lng + (Math.random() - 0.5) * radius;
        
        // Ensure we stay within city bounds
        lat = Math.max(city.latitude.min, Math.min(city.latitude.max, lat));
        lng = Math.max(city.longitude.min, Math.min(city.longitude.max, lng));
      } else {
        // Random location within city bounds
        lat = faker.datatype.float({
          min: city.latitude.min,
          max: city.latitude.max,
          precision: 0.00001
        });
        
        lng = faker.datatype.float({
          min: city.longitude.min,
          max: city.longitude.max,
          precision: 0.00001
        });
      }
      
      locations.push({ lat, lng });
    }
    
    return locations;
  }
  
  private static generateClusterCenters(
    city: CityCoordinates,
    count: number
  ): Array<{ lat: number; lng: number }> {
    const centers: Array<{ lat: number; lng: number }> = [];
    
    // Known commercial areas for major cities (approximations)
    const knownCenters: Record<string, Array<{ lat: number; lng: number }>> = {
      'Milano': [
        { lat: 45.4654, lng: 9.1859 }, // Duomo area
        { lat: 45.4773, lng: 9.1967 }, // Porta Garibaldi
        { lat: 45.4561, lng: 9.1897 }, // Navigli
        { lat: 45.4641, lng: 9.1886 }, // Brera
      ],
      'Roma': [
        { lat: 41.9028, lng: 12.4964 }, // Centro Storico
        { lat: 41.9055, lng: 12.4823 }, // Trastevere
        { lat: 41.9077, lng: 12.4969 }, // Campo de' Fiori
        { lat: 41.8986, lng: 12.4768 }, // Testaccio
      ],
      'Napoli': [
        { lat: 40.8518, lng: 14.2681 }, // Centro Storico
        { lat: 40.8359, lng: 14.2488 }, // Chiaia
        { lat: 40.8414, lng: 14.2464 }, // Via Toledo
      ]
    };
    
    const cityKnownCenters = knownCenters[city.name] || [];
    
    // Use known centers if available, otherwise generate random ones
    if (cityKnownCenters.length > 0) {
      // Select from known centers and add some random ones
      const knownCount = Math.min(count, cityKnownCenters.length);
      centers.push(...faker.helpers.shuffle(cityKnownCenters).slice(0, knownCount));
      
      // Generate additional random centers if needed
      for (let i = knownCount; i < count; i++) {
        centers.push({
          lat: faker.datatype.float({
            min: city.latitude.min,
            max: city.latitude.max,
            precision: 0.0001
          }),
          lng: faker.datatype.float({
            min: city.longitude.min,
            max: city.longitude.max,
            precision: 0.0001
          })
        });
      }
    } else {
      // Generate completely random centers
      for (let i = 0; i < count; i++) {
        centers.push({
          lat: faker.datatype.float({
            min: city.latitude.min,
            max: city.latitude.max,
            precision: 0.0001
          }),
          lng: faker.datatype.float({
            min: city.longitude.min,
            max: city.longitude.max,
            precision: 0.0001
          })
        });
      }
    }
    
    return centers;
  }
  
  // Calculate business density for different areas
  static calculateBusinessDensity(
    businesses: Array<{ lat: number; lng: number; category: string }>,
    gridSize: number = 0.01 // ~1km grid
  ): Record<string, { count: number; categories: Record<string, number> }> {
    const density: Record<string, { count: number; categories: Record<string, number> }> = {};
    
    businesses.forEach(business => {
      // Round coordinates to grid
      const gridLat = Math.round(business.lat / gridSize) * gridSize;
      const gridLng = Math.round(business.lng / gridSize) * gridSize;
      const gridKey = `${gridLat},${gridLng}`;
      
      if (!density[gridKey]) {
        density[gridKey] = { count: 0, categories: {} };
      }
      
      density[gridKey].count++;
      density[gridKey].categories[business.category] = 
        (density[gridKey].categories[business.category] || 0) + 1;
    });
    
    return density;
  }
  
  // Suggest optimal locations for new businesses based on existing density
  static suggestOptimalLocations(
    city: CityCoordinates,
    existingBusinesses: Array<{ lat: number; lng: number; category: string }>,
    targetCategory: string,
    count: number = 1
  ): Array<{ lat: number; lng: number; score: number; reason: string }> {
    const suggestions: Array<{ lat: number; lng: number; score: number; reason: string }> = [];
    const density = this.calculateBusinessDensity(existingBusinesses);
    
    // Generate candidate locations
    const candidates = this.generateClusteredLocations(city, count * 10, 0.3);
    
    candidates.forEach(candidate => {
      const gridLat = Math.round(candidate.lat / 0.01) * 0.01;
      const gridLng = Math.round(candidate.lng / 0.01) * 0.01;
      const gridKey = `${gridLat},${gridLng}`;
      
      const gridData = density[gridKey] || { count: 0, categories: {} };
      
      let score = 50; // Base score
      let reason = 'Standard location';
      
      // Scoring factors
      const totalBusinesses = gridData.count;
      const sameCategory = gridData.categories[targetCategory] || 0;
      
      // Avoid oversaturated areas of same category
      if (sameCategory > 3) {
        score -= 30;
        reason = 'High competition area';
      } else if (sameCategory === 0 && totalBusinesses > 0) {
        score += 20;
        reason = 'Underserved category in commercial area';
      }
      
      // Moderate business activity is good
      if (totalBusinesses >= 2 && totalBusinesses <= 8) {
        score += 15;
        reason += ' - Good commercial activity';
      } else if (totalBusinesses === 0) {
        score -= 10;
        reason = 'No existing commercial activity';
      } else if (totalBusinesses > 15) {
        score -= 10;
        reason += ' - Oversaturated area';
      }
      
      // Complementary businesses bonus
      const complementaryCategories = this.getComplementaryCategories(targetCategory);
      const complementaryCount = complementaryCategories.reduce(
        (sum, cat) => sum + (gridData.categories[cat] || 0), 0
      );
      
      if (complementaryCount > 0) {
        score += complementaryCount * 5;
        reason += ' - Near complementary businesses';
      }
      
      suggestions.push({
        lat: candidate.lat,
        lng: candidate.lng,
        score,
        reason
      });
    });
    
    // Return top suggestions
    return suggestions
      .sort((a, b) => b.score - a.score)
      .slice(0, count);
  }
  
  private static getComplementaryCategories(category: string): string[] {
    const complementary: Record<string, string[]> = {
      'bar': ['ristorante', 'abbigliamento', 'bellezza'],
      'ristorante': ['bar', 'alimentari', 'bellezza'],
      'alimentari': ['ristorante', 'bar', 'abbigliamento'],
      'bellezza': ['abbigliamento', 'bar', 'spa'],
      'abbigliamento': ['bellezza', 'bar', 'ristorante'],
      'palestra': ['spa', 'bellezza', 'alimentari'],
      'spa': ['palestra', 'bellezza', 'bar']
    };
    
    return complementary[category] || [];
  }
  
  // Analyze existing business clustering patterns
  static analyzeClusteringPatterns(
    businesses: Array<{ lat: number; lng: number; category: string }>
  ): {
    clusters: Array<{ center: { lat: number; lng: number }; businesses: number; diversity: number }>;
    isolatedBusinesses: number;
    averageDistanceToNearest: number;
  } {
    // Simplified clustering analysis
    const density = this.calculateBusinessDensity(businesses);
    
    const clusters = Object.entries(density)
      .filter(([key, data]) => data.count >= 3) // At least 3 businesses to be a cluster
      .map(([key, data]) => {
        const [lat, lng] = key.split(',').map(Number);
        const diversity = Object.keys(data.categories).length / data.count;
        
        return {
          center: { lat, lng },
          businesses: data.count,
          diversity
        };
      });
    
    const isolatedBusinesses = Object.values(density)
      .filter(data => data.count === 1).length;
    
    // Calculate average distance to nearest neighbor (simplified)
    let totalDistance = 0;
    let comparisons = 0;
    
    for (let i = 0; i < Math.min(businesses.length, 100); i++) {
      let minDistance = Infinity;
      
      for (let j = 0; j < businesses.length; j++) {
        if (i === j) continue;
        
        const distance = this.calculateDistance(
          businesses[i].lat, businesses[i].lng,
          businesses[j].lat, businesses[j].lng
        );
        
        minDistance = Math.min(minDistance, distance);
      }
      
      if (minDistance !== Infinity) {
        totalDistance += minDistance;
        comparisons++;
      }
    }
    
    const averageDistanceToNearest = comparisons > 0 ? totalDistance / comparisons : 0;
    
    return {
      clusters,
      isolatedBusinesses,
      averageDistanceToNearest
    };
  }
  
  private static calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371000; // Earth's radius in meters
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }
}