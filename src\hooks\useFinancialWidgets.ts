
import { useEffect } from 'react';
import { useWidgetStore } from '@/store/widgetStore';
import { useBusinessStore } from '@/store/businessStore';
import { useFinancialMetrics, defaultFinancialFilters } from '@/queries/useFinancialMetrics';
import { WidgetType } from '@/types/widget';
import { supabase } from '@/integrations/supabase/client';

/**
 * Hook per aggiornare automaticamente i widget finanziari con i dati reali
 */
export const useFinancialWidgets = () => {
  const { selectedBusiness } = useBusinessStore();
  const { dashboardConfig, updateWidgetData, setWidgetLoading } = useWidgetStore();

  // Ottieni i dati finanziari (assicurati che useFinancialMetrics utilizzi booking_date)
  const { data: financialMetrics, isLoading } = useFinancialMetrics(
    selectedBusiness?.id,
    defaultFinancialFilters
  );

  // Funzione per verificare i dati della vista materializzata
  const checkMaterializedView = async (businessId: string | undefined) => {
    if (!businessId) return;
    
    try {
      console.log('Verificando i dati nella vista materializzata mv_business_availability per business:', businessId);
      
      const { data, error } = await supabase
        .from('mv_business_availability')
        .select('*')
        .eq('business_id', businessId)
        .single();
      
      if (error) {
        console.error('Errore nel recupero dei dati dalla vista materializzata:', error);
      } else {
        console.log('Dati recuperati dalla vista materializzata:', data);
        
        // Proviamo anche a verificare i dati delle prenotazioni per questo business
        const { data: deals, error: dealsError } = await supabase
          .from('deals')
          .select('id')
          .eq('business_id', businessId);
          
        if (dealsError) {
          console.error('Errore nel recupero delle offerte:', dealsError);
        } else if (deals && deals.length > 0) {
          console.log('Offerte trovate:', deals.length, 'IDs:', deals.map(d => d.id));
          
          // Recupera le prenotazioni per queste offerte
          const { data: bookings, error: bookingsError } = await supabase
            .from('bookings')
            .select('id, deal_id, booking_date, created_at, discounted_price, original_price')
            .in('deal_id', deals.map(d => d.id))
            .order('booking_date', { ascending: false })
            .limit(10);
            
          if (bookingsError) {
            console.error('Errore nel recupero delle prenotazioni:', bookingsError);
          } else {
            console.log('Ultime 10 prenotazioni:', bookings);
          }
        } else {
          console.log('Nessuna offerta trovata per il business ID:', businessId);
        }
      }
    } catch (e) {
      console.error('Errore nella verifica della vista materializzata:', e);
    }
  };

  // Aggiorna i widget finanziari quando cambiano i dati
  useEffect(() => {
    if (!selectedBusiness) {
      console.log('Nessun business selezionato per i widget finanziari');
      return;
    }

    console.log('Business ID selezionato:', selectedBusiness.id);
    console.log('Business Nome selezionato:', selectedBusiness.name);
    
    // Verifica i dati nella vista materializzata
    checkMaterializedView(selectedBusiness.id);

    // Memorizza gli ID dei widget finanziari
    const totalRevenueWidgetId = dashboardConfig.widgets.find(w => w.type === WidgetType.TOTAL_REVENUE)?.id;
    const estimatedProfitWidgetId = dashboardConfig.widgets.find(w => w.type === WidgetType.ESTIMATED_PROFIT)?.id;
    const avgBookingPriceWidgetId = dashboardConfig.widgets.find(w => w.type === WidgetType.AVG_BOOKING_PRICE)?.id;
    const revenuePerClientWidgetId = dashboardConfig.widgets.find(w => w.type === WidgetType.REVENUE_PER_CLIENT)?.id;

    // Imposta lo stato di caricamento per tutti i widget finanziari
    if (totalRevenueWidgetId) setWidgetLoading(totalRevenueWidgetId, isLoading);
    if (estimatedProfitWidgetId) setWidgetLoading(estimatedProfitWidgetId, isLoading);
    if (avgBookingPriceWidgetId) setWidgetLoading(avgBookingPriceWidgetId, isLoading);
    if (revenuePerClientWidgetId) setWidgetLoading(revenuePerClientWidgetId, isLoading);

    console.log('Financial metrics update:', financialMetrics);

    // Se i dati sono stati caricati, aggiorna i widget
    if (!isLoading && financialMetrics) {
      console.log('Aggiornamento dei widget finanziari con dati:', financialMetrics);
      
      // Aggiorna il widget dei ricavi totali
      if (totalRevenueWidgetId) {
        console.log('Aggiornamento widget ricavi totali:', {
          amount: financialMetrics.totalRevenue,
          percentChange: financialMetrics.totalRevenueChange
        });
        
        updateWidgetData(totalRevenueWidgetId, {
          amount: financialMetrics.totalRevenue,
          period: '30 giorni',
          percentChange: financialMetrics.totalRevenueChange
        });
      }

      // Aggiorna il widget del profitto stimato
      if (estimatedProfitWidgetId) {
        console.log('Aggiornamento widget profitto stimato:', {
          amount: financialMetrics.estimatedProfit,
          margin: financialMetrics.profitMargin,
          percentChange: financialMetrics.estimatedProfitChange
        });
        
        updateWidgetData(estimatedProfitWidgetId, {
          amount: financialMetrics.estimatedProfit,
          margin: financialMetrics.profitMargin,
          period: '30 giorni',
          percentChange: financialMetrics.estimatedProfitChange
        });
      }

      // Aggiorna il widget del prezzo medio di prenotazione
      if (avgBookingPriceWidgetId) {
        console.log('Aggiornamento widget prezzo medio:', {
          avgBookingPrice: financialMetrics.avgBookingPrice,
          avgBookingPriceChange: financialMetrics.avgBookingPriceChange
        });
        
        updateWidgetData(avgBookingPriceWidgetId, {
          amount: financialMetrics.avgBookingPrice,
          period: '30 giorni',
          percentChange: financialMetrics.avgBookingPriceChange
        });
      }

      // Aggiorna il widget del ricavo per cliente
      if (revenuePerClientWidgetId) {
        console.log('Aggiornamento widget ricavo per cliente:', {
          revenuePerClient: financialMetrics.revenuePerClient,
          revenuePerClientChange: financialMetrics.revenuePerClientChange
        });
        
        updateWidgetData(revenuePerClientWidgetId, {
          amount: financialMetrics.revenuePerClient,
          period: '30 giorni',
          percentChange: financialMetrics.revenuePerClientChange
        });
      }
    } else {
      console.log('Dati finanziari non ancora disponibili o in caricamento');
    }
  }, [
    selectedBusiness,
    financialMetrics,
    isLoading,
    dashboardConfig.widgets,
    updateWidgetData,
    setWidgetLoading
  ]);

  return { financialMetrics, isLoading };
};
