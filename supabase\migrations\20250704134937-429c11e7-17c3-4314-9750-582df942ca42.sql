-- Add RLS policies for pricing_tiers table to allow admin operations
CREATE POLICY "Only admins can create pricing tiers" 
ON public.pricing_tiers 
FOR INSERT 
WITH CHECK (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Only admins can update pricing tiers" 
ON public.pricing_tiers 
FOR UPDATE 
USING (get_user_role(auth.uid()) = 'admin')
WITH CHECK (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Only admins can delete pricing tiers" 
ON public.pricing_tiers 
FOR DELETE 
USING (get_user_role(auth.uid()) = 'admin');