-- Rename the business_ai_agents table to ai_business_agents
ALTER TABLE public.business_ai_agents RENAME TO ai_business_agents;

-- Update constraint names to match the new table name
ALTER INDEX IF EXISTS business_ai_agents_pkey RENAME TO ai_business_agents_pkey;
ALTER INDEX IF EXISTS business_ai_agents_business_id_agent_type_key RENAME TO ai_business_agents_business_id_agent_type_key;

-- Update RLS policies to match the new table name
DROP POLICY IF EXISTS "Business owners can manage their AI agents" ON public.ai_business_agents;
CREATE POLICY "Business owners can manage their AI agents" 
ON public.ai_business_agents 
FOR ALL 
USING (business_id IN (
  SELECT id FROM businesses WHERE owner_id = auth.uid()
));