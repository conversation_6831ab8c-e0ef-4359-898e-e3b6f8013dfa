import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { <PERSON>lide<PERSON> } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  InfoIcon,
  TrendingDown,
  TrendingUp,
  ChartLine,
  BarChart4,
  Clock,
  ArrowRightLeft,
  Calendar,
  PartyPopper,
  Film,
  DollarSign,
  RotateCcw,
  Users,
  Zap,
  BarChart2,
} from "lucide-react";
import MainLayout from "@/layouts/MainLayout";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";

const PricingPage = () => {
  // State for Dynamic Pricing Controls
  const [automatedPricing, setAutomatedPricing] = useState(true);
  const [priceSensitivity, setPriceSensitivity] = useState(50);
  const [minPrice, setMinPrice] = useState("7.99");
  const [maxPrice, setMaxPrice] = useState("16.99");

  // State for Price Fluctuation Rules
  const [maxPriceChange, setMaxPriceChange] = useState(30);
  const [selectedCategory, setSelectedCategory] = useState("Tutti i Film");
  const [premiumDays, setPremiumDays] = useState(["V", "S", "D"]);

  // State for Pricing Factors
  const [demandScore, setDemandScore] = useState(7);
  const [competition, setCompetition] = useState(7);
  const [timeOfDay, setTimeOfDay] = useState(15);
  const [dayOfWeek, setDayOfWeek] = useState(10);
  const [holidaysEvents, setHolidaysEvents] = useState(10);
  const [movieAge, setMovieAge] = useState(5);

  // State for Price Simulator
  const [selectedMovie, setSelectedMovie] = useState("Dune: Part Two");
  const [selectedTimeSlot, setSelectedTimeSlot] = useState("Evening (5PM - 8PM)");
  const [selectedDay, setSelectedDay] = useState("Friday");
  const [expectedDemand, setExpectedDemand] = useState(50);

  // Tab state
  const [activeTab, setActiveTab] = useState("optimization");

  // Dialog state
  const [showAiInfoDialog, setShowAiInfoDialog] = useState(false);

  // Dati mockati per il grafico
  const priceData = [
    { price: "7.99 €", attendance: 80, revenue: 639.2 },
    { price: "9.99 €", attendance: 70, revenue: 699.3 },
    { price: "11.99 €", attendance: 60, revenue: 719.4 },
    { price: "13.99 €", attendance: 50, revenue: 699.5 },
    { price: "15.99 €", attendance: 40, revenue: 639.6 },
  ];

  // Helper function to toggle premium days
  const togglePremiumDay = (day: string) => {
    if (premiumDays.includes(day)) {
      setPremiumDays(premiumDays.filter((d) => d !== day));
    } else {
      setPremiumDays([...premiumDays, day]);
    }
  };

  return (
    <MainLayout>
      <div className="container mx-auto p-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-3xl font-bold">Prezzi in Tempo Reale</h1>
            <p className="text-muted-foreground mt-1">
              Ottimizza i prezzi dei biglietti con raccomandazioni basate su AI
              in base alla domanda e ai dati storici.
            </p>
          </div>
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => setShowAiInfoDialog(true)}
          >
            <InfoIcon size={16} />
            Info sui Prezzi AI
          </Button>
        </div>

        {/* AI Pricing Info Dialog */}
        <Dialog open={showAiInfoDialog} onOpenChange={setShowAiInfoDialog}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle className="text-2xl font-bold">
                Come Funziona il Nostro Sistema di Prezzi Basato su AI
              </DialogTitle>
              <DialogDescription className="text-base">
                Comprensione dell'intelligenza dietro l'ottimizzazione dei
                prezzi
              </DialogDescription>
            </DialogHeader>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 py-4">
              {/* Left column */}
              <div>
                <h2 className="text-xl font-semibold mb-4">L'Algoritmo</h2>
                <p className="mb-4">
                  Il nostro algoritmo di prezzi combina machine learning con
                  principi economici per analizzare dati storici e in tempo
                  reale. Identifica i punti di prezzo ottimali che massimizzano
                  i tuoi obiettivi scelti - ricavi, affluenza o un approccio
                  equilibrato.
                </p>

                <h3 className="text-lg font-semibold mb-2 mt-6">
                  Caratteristiche Principali
                </h3>
                <ul className="space-y-4">
                  <li className="flex gap-3">
                    <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                      <InfoIcon size={14} />
                    </div>
                    <div>
                      <h4 className="font-medium">Analisi Multi-fattoriale</h4>
                      <p className="text-sm text-muted-foreground">
                        Elabora più di 15 variabili inclusi domanda,
                        competizione e fattori esterni
                      </p>
                    </div>
                  </li>

                  <li className="flex gap-3">
                    <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                      <TrendingUp size={14} />
                    </div>
                    <div>
                      <h4 className="font-medium">Ponderazione Dinamica</h4>
                      <p className="text-sm text-muted-foreground">
                        Regola automaticamente l'importanza dei fattori in base
                        all'affidabilità dei dati
                      </p>
                    </div>
                  </li>

                  <li className="flex gap-3">
                    <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                      <BarChart4 size={14} />
                    </div>
                    <div>
                      <h4 className="font-medium">
                        Modellazione dell'Elasticità dei Prezzi
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        Prevede come l'affluenza cambia con diverse variazioni
                        di prezzo
                      </p>
                    </div>
                  </li>

                  <li className="flex gap-3">
                    <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                      <Clock size={14} />
                    </div>
                    <div>
                      <h4 className="font-medium">Intelligenza Temporale</h4>
                      <p className="text-sm text-muted-foreground">
                        Riconosce modelli basati sul tempo per ottimizzazione
                        giornaliera, settimanale e stagionale
                      </p>
                    </div>
                  </li>
                </ul>
              </div>

              {/* Right column */}
              <div>
                <h2 className="text-xl font-semibold mb-4">
                  Comprendere l'Intelligenza
                </h2>

                <div className="bg-slate-50 p-4 rounded-md mb-6">
                  <h3 className="text-lg font-medium mb-2">
                    Punteggi di Confidenza
                  </h3>
                  <div className="mb-2 flex justify-between items-center">
                    <div className="w-full bg-gray-200 rounded-full h-4">
                      <div
                        className="bg-blue-600 h-4 rounded-full"
                        style={{ width: "85%" }}
                      ></div>
                    </div>
                    <span className="ml-2 font-medium">85%</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    I punteggi di confidenza riflettono l'affidabilità
                    statistica delle previsioni basate sulla qualità, quantità e
                    precisione storica dei dati. Punteggi più alti indicano
                    maggiore certezza.
                  </p>
                </div>

                <div className="bg-slate-50 p-4 rounded-md mb-6">
                  <h3 className="text-lg font-medium mb-2">Stime di Impatto</h3>
                  <div className="flex items-center gap-2 mb-2 text-green-600 font-medium">
                    <span className="text-xl">+12.5% Ricavi</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Le stime di impatto mostrano le variazioni previste nei
                    ricavi o nell'affluenza basate su modelli di elasticità dei
                    prezzi e dati sulle performance storiche. Sono calcolate
                    utilizzando l'analisi di regressione di cambiamenti di
                    prezzo simili.
                  </p>
                </div>

                <div className="bg-slate-50 p-4 rounded-md">
                  <h3 className="text-lg font-medium mb-2">
                    Dove Trovare gli Insight AI
                  </h3>
                  <ul className="space-y-3">
                    <li className="flex gap-3 items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium text-xs">
                        1
                      </div>
                      <div>
                        <p className="font-medium">
                          Scheda Metriche di Prezzo: Widget di Ottimizzazione AI
                          e pannello Insight Azionabili
                        </p>
                      </div>
                    </li>
                    <li className="flex gap-3 items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium text-xs">
                        2
                      </div>
                      <div>
                        <p className="font-medium">
                          Scheda Raccomandazioni AI: Suggerimenti dettagliati
                          sui prezzi con parametri regolabili
                        </p>
                      </div>
                    </li>
                    <li className="flex gap-3 items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium text-xs">
                        3
                      </div>
                      <div>
                        <p className="font-medium">
                          Cerca le icone ℹ️ e ⚙️ per ulteriori informazioni su
                          raccomandazioni specifiche
                        </p>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        <Tabs
          defaultValue="optimization"
          className="mb-6"
          onValueChange={setActiveTab}
        >
                     <TabsList className="grid grid-cols-5 lg:grid-cols-6 w-full">
                          <TabsTrigger value="optimization">
               Strumenti di Ottimizzazione
             </TabsTrigger>
             <TabsTrigger value="simulator">Simulatore di Prezzi</TabsTrigger>
             <TabsTrigger value="metrics">Metriche di Performance</TabsTrigger>
             <TabsTrigger value="recommendations">
               Raccomandazioni AI
             </TabsTrigger>
             <TabsTrigger value="testing" className="hidden lg:block">
               Test A/B
             </TabsTrigger>
           </TabsList>

          <TabsContent value="optimization" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {/* Dynamic Pricing Controls */}
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <ArrowRightLeft className="h-5 w-5 text-blue-500" />
                    <CardTitle className="text-xl">
                      Controlli di Prezzo Dinamico
                    </CardTitle>
                  </div>
                  <CardDescription>
                    Configura come i prezzi rispondono alla domanda in tempo
                    reale
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">Prezzo Automatizzato</h3>
                        <InfoIcon size={16} className="text-muted-foreground" />
                      </div>
                      <Switch
                        checked={automatedPricing}
                        onCheckedChange={setAutomatedPricing}
                      />
                    </div>

                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-sm font-medium">
                          Sensibilità di Prezzo
                        </span>
                        <span className="text-sm font-medium">
                          {priceSensitivity}%
                        </span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className="text-xs text-muted-foreground">
                          Conservativo
                        </span>
                        <Slider
                          value={[priceSensitivity]}
                          onValueChange={(value) =>
                            setPriceSensitivity(value[0])
                          }
                          max={100}
                          step={1}
                        />
                        <span className="text-xs text-muted-foreground">
                          Aggressivo
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium mb-2 block">
                          Prezzo Minimo (€)
                        </label>
                        <Input
                          type="number"
                          value={minPrice}
                          onChange={(e) => setMinPrice(e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium mb-2 block">
                          Prezzo Massimo (€)
                        </label>
                        <Input
                          type="number"
                          value={maxPrice}
                          onChange={(e) => setMaxPrice(e.target.value)}
                        />
                      </div>
                    </div>

                    <Button className="w-full">Salva Impostazioni</Button>
                  </div>
                </CardContent>
              </Card>

              {/* Price Fluctuation Rules */}
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <ChartLine className="h-5 w-5 text-blue-500" />
                    <CardTitle className="text-xl">
                      Regole di Fluttuazione Prezzi
                    </CardTitle>
                  </div>
                  <CardDescription>
                    Imposta limiti su quanto possono cambiare i prezzi
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <label className="text-sm font-medium mb-2 block">
                        Applica a
                      </label>
                      <Select
                        value={selectedCategory}
                        onValueChange={setSelectedCategory}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Seleziona categoria" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Tutti i Film">
                            Tutti i Film
                          </SelectItem>
                          <SelectItem value="In Evidenza">
                            In Evidenza
                          </SelectItem>
                          <SelectItem value="Nuove Uscite">
                            Nuove Uscite
                          </SelectItem>
                          <SelectItem value="Classici">Classici</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-sm font-medium">
                          Variazione Massima di Prezzo (%)
                        </span>
                        <span className="text-sm font-medium">
                          {maxPriceChange}%
                        </span>
                      </div>
                      <Slider
                        value={[maxPriceChange]}
                        onValueChange={(value) => setMaxPriceChange(value[0])}
                        max={100}
                        step={1}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="border rounded-md p-3">
                        <div className="flex items-center gap-2 mb-1">
                          <TrendingDown className="h-4 w-4 text-red-500" />
                          <span className="text-sm font-medium">
                            Diminuzione Max
                          </span>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          -20% per 24h
                        </div>
                      </div>
                      <div className="border rounded-md p-3">
                        <div className="flex items-center gap-2 mb-1">
                          <TrendingUp className="h-4 w-4 text-green-500" />
                          <span className="text-sm font-medium">
                            Aumento Max
                          </span>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          +30% per 24h
                        </div>
                      </div>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-3 block">
                        Giorni Premium
                      </label>
                      <div className="flex flex-row gap-2">
                        {["L", "M", "M", "G", "V", "S", "D"].map((day) => (
                          <Button
                            key={day}
                            variant={
                              premiumDays.includes(day) ? "default" : "outline"
                            }
                            onClick={() => togglePremiumDay(day)}
                            className="flex-1"
                          >
                            {day}
                          </Button>
                        ))}
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        I giorni premium possono avere prezzi massimi più alti
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Price Optimization Tools Section */}
            <div className="p-6 bg-white rounded-lg shadow">
              <h1 className="text-xl font-semibold mb-2">
                Strumenti di Ottimizzazione Prezzi
              </h1>
              <p className="text-sm text-gray-600 mb-4">
                Strumenti avanzati per analizzare e ottimizzare la tua strategia
                di prezzo
              </p>

                             <Tabs defaultValue="elasticity" className="w-full">
                 <TabsList className="grid w-full grid-cols-3">
                   <TabsTrigger value="elasticity" className="flex items-center gap-2">
                     <BarChart2 className="w-4 h-4" />
                     Elasticità di Prezzo
                   </TabsTrigger>
                   <TabsTrigger value="factors" className="flex items-center gap-2">
                     <ChartLine className="w-4 h-4" />
                     Fattori di Prezzo
                   </TabsTrigger>
                   <TabsTrigger value="time-based" className="flex items-center gap-2">
                     <Clock className="w-4 h-4" />
                     Prezzi Basati sul Tempo
                   </TabsTrigger>
                 </TabsList>

              <TabsContent value="elasticity" className="space-y-6">
                <div className="flex gap-6">
                  {/* Left Panel */}
                  <div className="flex-grow">
                    <div className="mb-4">
                      <h2 className="text-lg font-medium mb-1">
                        Analisi dell'Elasticità di Prezzo
                      </h2>
                      <p className="text-sm text-gray-600">
                        Mostra come le variazioni di prezzo influenzano i ricavi
                        e l'affluenza
                      </p>
                      <p className="text-sm text-gray-600 mt-1">
                        Il grafico mostra la curva di ottimizzazione dei ricavi
                        con il punto di prezzo ottimale attuale. Elasticità &lt;
                        -1 significa che l'affluenza diminuisce più velocemente
                        di quanto aumentino i ricavi.
                      </p>
                    </div>

                    {/* Time Period Selector */}
                    <div className="flex gap-2 mb-4">
                      <button className="px-3 py-1 rounded text-sm">
                        Giorno
                      </button>
                      <button className="px-3 py-1 rounded text-sm bg-purple-600 text-white">
                        Settimana
                      </button>
                      <button className="px-3 py-1 rounded text-sm">
                        Mese
                      </button>
                      <button className="px-3 py-1 rounded text-sm">
                        Trimestre
                      </button>
                      <button className="px-3 py-1 rounded text-sm">
                        Anno
                      </button>
                    </div>

                    {/* Chart Component */}
                    <div className="h-[400px] w-full border rounded-lg mb-4 p-4">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart
                          data={priceData}
                          margin={{
                            top: 20,
                            right: 30,
                            left: 20,
                            bottom: 20,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis
                            dataKey="price"
                            label={{
                              value: "Prezzo",
                              position: "bottom",
                            }}
                          />
                          <YAxis
                            yAxisId="left"
                            label={{
                              value: "Affluenza",
                              angle: -90,
                              position: "insideLeft",
                            }}
                          />
                          <YAxis
                            yAxisId="right"
                            orientation="right"
                            label={{
                              value: "Ricavi (€)",
                              angle: 90,
                              position: "insideRight",
                            }}
                          />
                          <Tooltip />
                          <Legend />
                          <Line
                            yAxisId="left"
                            type="monotone"
                            dataKey="attendance"
                            name="Affluenza"
                            stroke="#10B981"
                            strokeWidth={2}
                            dot={{ r: 6 }}
                            activeDot={{ r: 8 }}
                          />
                          <Line
                            yAxisId="right"
                            type="monotone"
                            dataKey="revenue"
                            name="Ricavi"
                            stroke="#3B82F6"
                            strokeWidth={2}
                            dot={{ r: 6 }}
                            activeDot={{ r: 8 }}
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </div>

                    <div className="flex justify-between items-center">
                      <div className="text-sm">
                        Elasticità Attuale:{" "}
                        <span className="font-medium text-indigo-600">
                          -0.6
                        </span>
                      </div>
                      <Button className="flex items-center gap-2">
                        <BarChart4 className="h-4 w-4" />
                        Esegui Ottimizzazione Prezzi AI
                      </Button>
                    </div>
                  </div>

                  {/* Elasticity Multiplier Card - Takes 1/3 of the space */}
                  <div className="md:col-span-1">
                    <Card className="h-full">
                      <CardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="text-lg flex items-center gap-2">
                              <BarChart4 className="h-5 w-5 text-blue-500" />
                              Moltiplicatore di Elasticità
                            </CardTitle>
                            <CardDescription>
                              Regola quanto la domanda è sensibile alle
                              variazioni di prezzo
                            </CardDescription>
                          </div>
                          <InfoIcon className="h-5 w-5 text-muted-foreground" />
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                              <TrendingUp className="h-4 w-4 text-blue-500" />
                              <span className="text-sm font-medium">
                                Alta Sensibilità
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium">
                                Bassa Sensibilità
                              </span>
                              <TrendingDown className="h-4 w-4 text-blue-500" />
                            </div>
                          </div>

                          <Slider defaultValue={[50]} max={100} step={1} />

                          <div className="grid grid-cols-1 gap-4 mt-4">
                            <div className="border rounded-md p-3">
                              <div className="flex items-center gap-2 mb-1">
                                <BarChart4 className="h-4 w-4 text-orange-500" />
                                <span className="text-sm font-medium">
                                  Sensibilità di prezzo moderata
                                </span>
                              </div>
                              <div className="text-sm text-muted-foreground">
                                Elasticità: -1.10
                              </div>
                            </div>
                            <div className="border rounded-md p-3">
                              <div className="flex items-center gap-2 mb-1">
                                <BarChart4 className="h-4 w-4 text-purple-500" />
                                <span className="text-sm font-medium">
                                  Impatto sull'Affluenza
                                </span>
                              </div>
                              <div className="text-sm text-muted-foreground">
                                Fortemente influenzato dal prezzo
                              </div>
                            </div>
                          </div>

                          <p className="text-xs text-muted-foreground mt-2">
                            Regola questo moltiplicatore per riflettere come il
                            tuo pubblico risponde alle variazioni di prezzo.
                            Film e pubblici diversi possono avere profili di
                            elasticità differenti.
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="factors" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Pricing Factors Panel */}
                  <div className="lg:col-span-2">
                    <Card>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="text-xl">
                              Fattori di Prezzo
                            </CardTitle>
                            <CardDescription>
                              Regola l'impatto di ogni fattore sui calcoli del
                              prezzo
                            </CardDescription>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium bg-blue-100 text-blue-800 px-2 py-1 rounded">
                              AI Powered
                            </span>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-6">
                          {/* Demand Score */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4 text-blue-500" />
                              <span className="text-sm font-medium">
                                Punteggio Domanda
                              </span>
                              <InfoIcon className="h-4 w-4 text-muted-foreground" />
                            </div>
                            <span className="text-sm font-medium">
                              {demandScore}%
                            </span>
                          </div>
                          <Slider
                            value={[demandScore]}
                            onValueChange={(value) => setDemandScore(value[0])}
                            max={100}
                            step={1}
                            className="w-full"
                          />

                          {/* Competition */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Zap className="h-4 w-4 text-orange-500" />
                              <span className="text-sm font-medium">
                                Concorrenza
                              </span>
                              <InfoIcon className="h-4 w-4 text-muted-foreground" />
                            </div>
                            <span className="text-sm font-medium">
                              {competition}%
                            </span>
                          </div>
                          <Slider
                            value={[competition]}
                            onValueChange={(value) => setCompetition(value[0])}
                            max={100}
                            step={1}
                            className="w-full"
                          />

                          {/* Time of Day */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4 text-amber-500" />
                              <span className="text-sm font-medium">
                                Orario del Giorno
                              </span>
                              <InfoIcon className="h-4 w-4 text-muted-foreground" />
                            </div>
                            <span className="text-sm font-medium">
                              {timeOfDay}%
                            </span>
                          </div>
                          <Slider
                            value={[timeOfDay]}
                            onValueChange={(value) => setTimeOfDay(value[0])}
                            max={100}
                            step={1}
                            className="w-full"
                          />

                          {/* Day of Week */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-purple-500" />
                              <span className="text-sm font-medium">
                                Giorno della Settimana
                              </span>
                              <InfoIcon className="h-4 w-4 text-muted-foreground" />
                            </div>
                            <span className="text-sm font-medium">
                              {dayOfWeek}%
                            </span>
                          </div>
                          <Slider
                            value={[dayOfWeek]}
                            onValueChange={(value) => setDayOfWeek(value[0])}
                            max={100}
                            step={1}
                            className="w-full"
                          />

                          {/* Holidays & Events */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <PartyPopper className="h-4 w-4 text-pink-500" />
                              <span className="text-sm font-medium">
                                Festività e Eventi
                              </span>
                              <InfoIcon className="h-4 w-4 text-muted-foreground" />
                            </div>
                            <span className="text-sm font-medium">
                              {holidaysEvents}%
                            </span>
                          </div>
                          <Slider
                            value={[holidaysEvents]}
                            onValueChange={(value) =>
                              setHolidaysEvents(value[0])
                            }
                            max={100}
                            step={1}
                            className="w-full"
                          />

                          {/* Movie Age */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Film className="h-4 w-4 text-green-500" />
                              <span className="text-sm font-medium">
                                Età del Film
                              </span>
                              <InfoIcon className="h-4 w-4 text-muted-foreground" />
                            </div>
                            <span className="text-sm font-medium">
                              {movieAge}%
                            </span>
                          </div>
                          <Slider
                            value={[movieAge]}
                            onValueChange={(value) => setMovieAge(value[0])}
                            max={100}
                            step={1}
                            className="w-full"
                          />

                          {/* Action Buttons */}
                          <div className="flex gap-2 pt-4">
                            <Button
                              variant="outline"
                              className="flex items-center gap-2"
                            >
                              <RotateCcw className="h-4 w-4" />
                              Ripristina Default
                            </Button>
                            <Button className="flex items-center gap-2">
                              Applica Modifiche ai Fattori
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* All Movies Details Panel */}
                  <div className="lg:col-span-1">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg flex items-center gap-2">
                          <Film className="h-5 w-5 text-blue-500" />
                          Dettagli Tutti i Film
                        </CardTitle>
                        <CardDescription>
                          Metriche di prezzo attuali e statistiche
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-6">
                          {/* Current vs Recommended Price */}
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium">
                                Prezzo Attuale
                              </span>
                              <span className="text-sm font-medium">
                                Raccomandato
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-2xl font-bold">$12.99</span>
                              <span className="text-2xl font-bold text-blue-600">
                                $13.49
                              </span>
                            </div>
                          </div>

                          {/* Elasticity */}
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                              <span className="text-sm font-medium">
                                Elasticità
                              </span>
                            </div>
                            <div className="text-right">
                              <div className="font-medium">-0.8</div>
                              <div className="text-xs text-muted-foreground">
                                Bassa sensibilità prezzo
                              </div>
                            </div>
                          </div>

                          {/* Attendance */}
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                              <span className="text-sm font-medium">
                                Affluenza
                              </span>
                            </div>
                            <div className="text-right">
                              <div className="font-medium">72%</div>
                              <div className="text-xs text-red-500">
                                ↓ -2.5%
                              </div>
                            </div>
                          </div>

                          {/* Revenue */}
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-4 w-4 text-green-500" />
                              <span className="text-sm font-medium">
                                Ricavi
                              </span>
                            </div>
                            <div className="text-right">
                              <div className="font-medium">$4,250</div>
                              <div className="text-xs text-green-500">
                                ↑ +8.2%
                              </div>
                            </div>
                          </div>

                          {/* Last Update */}
                          <div>
                            <div className="text-sm font-medium mb-1">
                              Ultimo Aggiornamento
                            </div>
                            <div className="text-xs text-muted-foreground">
                              3 giorni fa
                            </div>
                            <div className="text-xs text-muted-foreground">
                              Basato su 1,428 transazioni
                            </div>
                          </div>

                          {/* Optimization Status */}
                          <div className="p-3 bg-green-50 rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <span className="text-sm font-medium text-green-800">
                                Ottimizzazione Completata
                              </span>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              className="w-full mt-2"
                            >
                              <RotateCcw className="h-4 w-4 mr-2" />
                              Esegui Nuovamente
                            </Button>
                          </div>

                          {/* Movie Examples */}
                          <div className="space-y-3">
                            <div className="flex justify-between items-center">
                              <div>
                                <div className="text-sm font-medium">
                                  Galactic Odyssey
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  Da $8.13 a $9.22
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="text-sm font-medium text-green-600">
                                  +13.5%
                                </div>
                                <div className="text-xs text-green-600">
                                  +$156.03
                                </div>
                              </div>
                            </div>

                            <div className="flex justify-between items-center">
                              <div>
                                <div className="text-sm font-medium">
                                  The Silent Echo
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  Da $8.68 a $8.09
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="text-sm font-medium text-red-600">
                                  -6.7%
                                </div>
                                <div className="text-xs text-green-600">
                                  +$59.54
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                                   </div>
                 </TabsContent>

                 <TabsContent value="factors" className="space-y-6">
                   <div className="p-4 text-center text-muted-foreground">
                     <p>I fattori di prezzo interni verranno implementati qui.</p>
                   </div>
                 </TabsContent>

                 <TabsContent value="time-based" className="space-y-6">
                   <div className="p-4 text-center text-muted-foreground">
                     <p>I prezzi basati sul tempo verranno implementati qui.</p>
                   </div>
                 </TabsContent>
               </Tabs>
             </div>
           </TabsContent>

           <TabsContent value="simulator" className="space-y-6">
             <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
               {/* Price Simulator Panel */}
               <div>
                 <Card>
                   <CardHeader>
                     <div className="flex items-center gap-2">
                       <DollarSign className="h-5 w-5 text-blue-500" />
                       <CardTitle className="text-xl">Simulatore di Prezzi</CardTitle>
                     </div>
                     <CardDescription>
                       Simula i prezzi basandoti su vari fattori
                     </CardDescription>
                   </CardHeader>
                   <CardContent>
                     <div className="space-y-6">
                       {/* Select Movie */}
                       <div>
                         <label className="text-sm font-medium mb-2 block">
                           Seleziona Film
                         </label>
                         <Select value={selectedMovie} onValueChange={setSelectedMovie}>
                           <SelectTrigger>
                             <SelectValue placeholder="Seleziona un film" />
                           </SelectTrigger>
                           <SelectContent>
                             <SelectItem value="Dune: Part Two">Dune: Part Two</SelectItem>
                             <SelectItem value="Galactic Odyssey">Galactic Odyssey</SelectItem>
                             <SelectItem value="The Silent Echo">The Silent Echo</SelectItem>
                             <SelectItem value="Quantum Dreams">Quantum Dreams</SelectItem>
                             <SelectItem value="Ocean's Edge">Ocean's Edge</SelectItem>
                           </SelectContent>
                         </Select>
                       </div>

                       {/* Time of Day */}
                       <div>
                         <label className="text-sm font-medium mb-2 block">
                           Orario del Giorno
                         </label>
                         <Select value={selectedTimeSlot} onValueChange={setSelectedTimeSlot}>
                           <SelectTrigger>
                             <SelectValue placeholder="Seleziona orario" />
                           </SelectTrigger>
                           <SelectContent>
                             <SelectItem value="Morning (9AM - 12PM)">
                               Mattina (9AM - 12PM)
                             </SelectItem>
                             <SelectItem value="Afternoon (12PM - 5PM)">
                               Pomeriggio (12PM - 5PM)
                             </SelectItem>
                             <SelectItem value="Evening (5PM - 8PM)">
                               Sera (5PM - 8PM)
                             </SelectItem>
                             <SelectItem value="Night (8PM - 11PM)">
                               Notte (8PM - 11PM)
                             </SelectItem>
                           </SelectContent>
                         </Select>
                       </div>

                       {/* Day of Week */}
                       <div>
                         <label className="text-sm font-medium mb-2 block">
                           Giorno della Settimana
                         </label>
                         <Select value={selectedDay} onValueChange={setSelectedDay}>
                           <SelectTrigger>
                             <SelectValue placeholder="Seleziona giorno" />
                           </SelectTrigger>
                           <SelectContent>
                             <SelectItem value="Monday">Lunedì</SelectItem>
                             <SelectItem value="Tuesday">Martedì</SelectItem>
                             <SelectItem value="Wednesday">Mercoledì</SelectItem>
                             <SelectItem value="Thursday">Giovedì</SelectItem>
                             <SelectItem value="Friday">Venerdì</SelectItem>
                             <SelectItem value="Saturday">Sabato</SelectItem>
                             <SelectItem value="Sunday">Domenica</SelectItem>
                           </SelectContent>
                         </Select>
                       </div>

                       {/* Expected Demand */}
                       <div>
                         <div className="flex justify-between mb-2">
                           <span className="text-sm font-medium">Domanda Prevista</span>
                           <span className="text-sm font-medium">{expectedDemand}%</span>
                         </div>
                         <div className="flex items-center gap-4">
                           <span className="text-xs text-muted-foreground">Bassa</span>
                           <Slider
                             value={[expectedDemand]}
                             onValueChange={(value) => setExpectedDemand(value[0])}
                             max={100}
                             step={1}
                             className="flex-1"
                           />
                           <span className="text-xs text-muted-foreground">Alta</span>
                         </div>
                         <div className="flex justify-between text-xs text-muted-foreground mt-1">
                           <span>Bassa</span>
                           <span>Media</span>
                           <span>Alta</span>
                         </div>
                       </div>
                     </div>
                   </CardContent>
                 </Card>
               </div>

               {/* Simulation Results Panel */}
               <div>
                 <Card>
                   <CardHeader>
                     <div className="flex items-center gap-2">
                       <TrendingUp className="h-5 w-5 text-blue-500" />
                       <CardTitle className="text-xl">Risultati Simulazione</CardTitle>
                     </div>
                     <CardDescription>
                       Prezzi e ricavi previsti basati sui tuoi input
                     </CardDescription>
                   </CardHeader>
                   <CardContent>
                     <div className="grid grid-cols-1 gap-4">
                       {/* Optimal Price */}
                       <div className="border rounded-lg p-4">
                         <div className="flex items-center gap-2 mb-2">
                           <DollarSign className="h-4 w-4 text-green-500" />
                           <span className="text-sm font-medium">Prezzo Ottimale</span>
                         </div>
                         <div className="text-2xl font-bold">€16.23</div>
                         <div className="text-sm text-green-600">+62.5% vs. prezzo base</div>
                       </div>

                       {/* Projected Attendance */}
                       <div className="border rounded-lg p-4">
                         <div className="flex items-center gap-2 mb-2">
                           <Users className="h-4 w-4 text-blue-500" />
                           <span className="text-sm font-medium">Affluenza Prevista</span>
                         </div>
                         <div className="text-2xl font-bold">50%</div>
                         <div className="text-sm text-muted-foreground">
                           Della capacità del teatro
                         </div>
                       </div>

                       {/* Estimated Revenue */}
                       <div className="border rounded-lg p-4">
                         <div className="flex items-center gap-2 mb-2">
                           <BarChart4 className="h-4 w-4 text-purple-500" />
                           <span className="text-sm font-medium">Ricavi Stimati</span>
                         </div>
                         <div className="text-2xl font-bold">€1,217.25</div>
                         <div className="text-sm text-muted-foreground">Per spettacolo</div>
                       </div>
                     </div>
                   </CardContent>
                 </Card>
               </div>
             </div>

             {/* Price Comparison Table */}
             <Card>
               <CardHeader>
                 <CardTitle>Tabella di Confronto Prezzi</CardTitle>
               </CardHeader>
               <CardContent>
                 <div className="overflow-x-auto">
                   <table className="w-full">
                     <thead>
                       <tr className="border-b">
                         <th className="text-left p-3 font-medium">Strategia di Prezzo</th>
                         <th className="text-left p-3 font-medium">Prezzo Biglietto</th>
                         <th className="text-left p-3 font-medium">Affluenza Stimata</th>
                         <th className="text-left p-3 font-medium">Ricavi Previsti</th>
                       </tr>
                     </thead>
                     <tbody>
                       <tr className="border-b bg-green-50">
                         <td className="p-3 font-medium">Dinamico (Ottimale)</td>
                         <td className="p-3">€16.23</td>
                         <td className="p-3">50%</td>
                         <td className="p-3 font-medium text-green-600">€1,217.25</td>
                       </tr>
                       <tr className="border-b">
                         <td className="p-3">Prezzo Base Fisso</td>
                         <td className="p-3">€9.99</td>
                         <td className="p-3">60%</td>
                         <td className="p-3">€299.70</td>
                       </tr>
                       <tr className="border-b">
                         <td className="p-3">Prezzo Economy</td>
                         <td className="p-3">€7.99</td>
                         <td className="p-3">70%</td>
                         <td className="p-3">€279.65</td>
                       </tr>
                       <tr className="border-b">
                         <td className="p-3">Prezzo Premium</td>
                         <td className="p-3">€14.99</td>
                         <td className="p-3">35%</td>
                         <td className="p-3">€262.32</td>
                       </tr>
                     </tbody>
                   </table>
                 </div>
               </CardContent>
             </Card>
           </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default PricingPage;
