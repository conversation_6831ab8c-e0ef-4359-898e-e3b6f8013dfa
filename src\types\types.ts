
export interface UserDetails {
  id: string;
  email: string | null;
  first_name: string | null;
  last_name: string | null;
  avatar_url: string | null;
  display_name: string | null;
  phone_number: string | null;
  registered_at: string | null;
  business_mode?: boolean;
  default_business_id?: string | null;
}

export interface Business {
  id: string;
  name: string;
  description: string | null;
  deal_count: number;
  booking_count: number;
  pending_booking_count: number;
  created_at?: string;
}

export interface LayoutElement {
  id: string;
  type: string;
  name: string;
  capacity?: number;
  width?: number;
  height?: number;
  parentId?: string | null;
}

export interface LayoutSuggestion {
  title: string;
  description: string;
  elements: Array<{
    type: string;
    name: string;
    count: number;
    capacity?: number;
    parentType?: string;
    parentName?: string;
  }>;
}

export interface Layout {
  id: string;
  name: string;
  business_id: string;
  description?: string | null;
  elements: LayoutElement[];
  created_at?: string;
  updated_at?: string;
}

export interface UserSettings {
  notificationsEnabled: boolean;
  locationEnabled: boolean;
  darkMode?: boolean;
  language?: string;
  defaultBusinessId?: string; // Added field for default selected business
}

export interface Category {
  id: string;
  name: string;
  description: string | null;
  icon: string | null;
  created_at: string;
  updated_at: string;
}

export interface BusinessProductCategory {
  id: string;
  name: string;
  description: string | null;
  icon: string | null;
  business_id: string;
  created_at: string;
  updated_at: string;
}

export interface BusinessProduct {
  id: string;
  business_id: string;
  name: string;
  description: string | null;
  price: number | null;
  sku: string | null;
  category: string | null; // Now references business_product_categories.id
  category_text_old: string | null; // Old text category field preserved
  stock_quantity: number | null;
  is_available: boolean | null;
  images: string[] | null;
  created_at: string;
  updated_at: string;
}

export interface SubCategory {
  id: string;
  category_id: string;
  name: string;
  description: string | null;
  icon: string | null;
  created_at: string;
  updated_at: string;
}
