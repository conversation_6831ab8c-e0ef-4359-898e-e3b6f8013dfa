import { faker } from '@faker-js/faker';
import { BusinessCategory, FakeDataResult } from '../types';
import { categoryProducts, successLevelCharacteristics } from '../data/businesses';
import { seasonalPatterns, italianHolidays } from '../data/seasonal';
import { categoryOperatingHours } from '../data/daysNames';
import { supabase } from '@/integrations/supabase/client';
import { mapCategoryNameToType } from './utils/mapCategoryNameToType';

export class DealGenerator {
  private categoryMapping: Record<string, BusinessCategory> = {};

  constructor() {
    this.initializeCategoryMapping();
  }

  private async initializeCategoryMapping() {
    try {
      const { data: categories } = await supabase
        .from('categories')
        .select('id, name');
      
      if (categories) {
        categories.forEach(cat => {
          this.categoryMapping[cat.id] = mapCategoryNameToType(cat.name);
        });
      }
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  }


  private getSeasonalMultipliers(categoryType: BusinessCategory, date: Date) {
    const month = date.getMonth() + 1; // getMonth() returns 0-11
    const patterns = seasonalPatterns[categoryType] || seasonalPatterns.default;
    const pattern = patterns.find(p => p.month === month);
    
    return {
      dealMultiplier: pattern?.dealMultiplier || 1.0,
      priceMultiplier: pattern?.priceMultiplier || 1.0
    };
  }

  private isHoliday(date: Date): { isHoliday: boolean; holidayName?: string; impact?: string } {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    const holiday = italianHolidays.find(h => h.month === month && h.day === day);
    
    if (holiday) {
      return {
        isHoliday: true,
        holidayName: holiday.name,
        impact: holiday.impact
      };
    }
    
    return { isHoliday: false };
  }

  private generateDealTitle(categoryType: BusinessCategory, isSeasonalDeal: boolean): string {
    const products = categoryProducts[categoryType] || categoryProducts.default;
    const product = faker.helpers.arrayElement(products);
    
    const dealTypes = [
      `Offerta ${product}`,
      `Sconto ${product}`,
      `Promozione ${product}`,
      `Pacchetto ${product}`,
      `Offerta Speciale ${product}`
    ];

    if (isSeasonalDeal) {
      const currentMonth = new Date().getMonth();
      const seasonalPrefixes = {
        winter: ['Inverno Caldo', 'Offerta Invernale', 'Speciale Inverno'],
        spring: ['Primavera Fresca', 'Offerta Primaverile', 'Speciale Primavera'], 
        summer: ['Estate Calda', 'Offerta Estiva', 'Speciale Estate'],
        autumn: ['Autunno Dorato', 'Offerta Autunnale', 'Speciale Autunno']
      };

      let season: keyof typeof seasonalPrefixes;
      if (currentMonth >= 11 || currentMonth <= 1) season = 'winter';
      else if (currentMonth >= 2 && currentMonth <= 4) season = 'spring';
      else if (currentMonth >= 5 && currentMonth <= 7) season = 'summer';
      else season = 'autumn';

      const seasonalPrefix = faker.helpers.arrayElement(seasonalPrefixes[season]);
      dealTypes.push(`${seasonalPrefix} - ${product}`);
    }

    return faker.helpers.arrayElement(dealTypes);
  }

  private generateDealDescription(categoryType: BusinessCategory, title: string): string {
    const baseDescriptions = {
      bar: [
        "Goditi un momento di relax con la nostra selezione di caffè e dolci.",
        "L'aperitivo perfetto per iniziare la serata con gusto.",
        "Colazione italiana autentica in un ambiente accogliente."
      ],
      ristorante: [
        "Cucina tradizionale preparata con ingredienti freschi e genuini.",
        "Un'esperienza culinaria indimenticabile nel cuore della città.",
        "Sapori autentici della tradizione italiana in ogni piatto."
      ],
      spa: [
        "Concediti un momento di puro relax e benessere.",
        "Trattamenti rigeneranti per corpo e mente in un ambiente sereno.",
        "L'oasi di tranquillità che stavi cercando."
      ],
      palestra: [
        "Allenamento completo con attrezzature moderne e personal trainer.",
        "Raggiungi i tuoi obiettivi fitness in un ambiente motivante.",
        "Corsi di gruppo e allenamento personalizzato per ogni livello."
      ],
      bellezza: [
        "Trattamenti di bellezza personalizzati con prodotti di qualità.",
        "Cura della persona con tecniche all'avanguardia.",
        "Il tuo momento di bellezza e benessere."
      ],
      abbigliamento: [
        "Stile e qualità si incontrano nelle nostre collezioni.",
        "Abbigliamento di tendenza per ogni occasione.",
        "Moda italiana e internazionale selezionata con cura."
      ],
      alimentari: [
        "Prodotti freschi e genuini selezionati dal territorio.",
        "La qualità della tradizione alimentare italiana.",
        "Sapori autentici e ingredienti di prima scelta."
      ],
      default: [
        "Un'offerta imperdibile per i nostri clienti più affezionati.",
        "Qualità e convenienza si incontrano in questa promozione speciale.",
        "Non perdere questa occasione unica."
      ]
    };

    const descriptions = baseDescriptions[categoryType] || baseDescriptions.default;
    const baseDescription = faker.helpers.arrayElement(descriptions);
    
    // Add offer-specific details
    const offerDetails = [
      "Prenotazione obbligatoria.",
      "Offerta valida fino ad esaurimento posti.",
      "Non cumulabile con altre promozioni.",
      "Valido per nuovi clienti e clienti abituali."
    ];
    
    const detail = faker.helpers.arrayElement(offerDetails);
    return `${baseDescription} ${detail}`;
  }

  private generatePricing(
    categoryType: BusinessCategory,
    businessSuccessLevel: 'low' | 'medium' | 'high',
    seasonalMultipliers: { dealMultiplier: number; priceMultiplier: number }
  ) {
    // Base price ranges by category
    const basePrices = {
      bar: { min: 8, max: 35 },              // Caffè, aperitivi, snack
      ristorante: { min: 25, max: 120 },     // Menu, pizze, piatti
      alimentari: { min: 15, max: 80 },      // Prodotti alimentari, spesa
      bellezza: { min: 20, max: 150 },       // Servizi parrucchiere/estetica
      abbigliamento: { min: 30, max: 250 },  // Vestiti, accessori
      palestra: { min: 25, max: 100 },       // Abbonamenti, corsi
      spa: { min: 40, max: 180 },            // Trattamenti benessere
      cinema: { min: 8, max: 18 },           // Biglietti cinema
      fioraio: { min: 15, max: 85 },         // Bouquet, composizioni
      hotel: { min: 60, max: 350 },          // Camere, servizi hotel
      teatro: { min: 20, max: 120 },         // Biglietti spettacoli
      aperitivo: { min: 12, max: 35 },       // Cocktail, aperitivi
      default: { min: 20, max: 100 }         // Fallback generico
    };

    const priceRange = basePrices[categoryType] || basePrices.default;
    
    // Adjust for business success level
    const successMultiplier = businessSuccessLevel === 'high' ? 1.3 : 
                             businessSuccessLevel === 'medium' ? 1.0 : 0.8;
    
    const originalPrice = faker.datatype.number({
      min: priceRange.min * successMultiplier * seasonalMultipliers.priceMultiplier,
      max: priceRange.max * successMultiplier * seasonalMultipliers.priceMultiplier,
      precision: 0.01
    });

    // Discount based on business success level and seasonality  
    const successCharacteristics = successLevelCharacteristics[businessSuccessLevel];
    const discountRange = successCharacteristics.priceReduction;
    
    const discountPercentage = faker.datatype.number({
      min: discountRange[0] * 100,
      max: discountRange[1] * 100,
      precision: 1
    }) / 100;

    const discountedPrice = originalPrice * (1 - discountPercentage);

    return {
      originalPrice: Math.round(originalPrice * 100) / 100,
      discountedPrice: Math.round(discountedPrice * 100) / 100,
      discountPercentage: Math.round(discountPercentage * 100)
    };
  }

  private generateTimeSlots(categoryType: BusinessCategory): any {
    const hours = categoryOperatingHours[categoryType] || categoryOperatingHours.bar;
    const schedule = [];

    for (let day = 1; day <= 7; day++) {
      if (hours.closedDays.includes(day)) {
        schedule.push({
          day,
          day_name: ['Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato', 'Domenica'][day - 1],
          time_slots: []
        });
        continue;
      }

      const isWeekend = day === 6 || day === 7;
      const dayHours = isWeekend ? hours.weekends : hours.weekdays;
      
      // Generate realistic time slots based on category
      const timeSlots = [];
      
      if (categoryType === 'ristorante') {
        // Lunch and dinner slots
        timeSlots.push(
          { start_time: "12:00", end_time: "14:30", available_seats: faker.datatype.number({ min: 2, max: 8 }) },
          { start_time: "19:00", end_time: "22:00", available_seats: faker.datatype.number({ min: 2, max: 8 }) }
        );
      } else if (categoryType === 'spa' || categoryType === 'bellezza') {
        // Hourly appointments
        const startHour = parseInt(dayHours.open.split(':')[0]);
        const endHour = parseInt(dayHours.close.split(':')[0]);
        
        for (let hour = startHour; hour < endHour; hour++) {
          if (hour >= 12 && hour <= 14 && !isWeekend) continue; // Skip lunch break on weekdays
          
          timeSlots.push({
            start_time: `${hour.toString().padStart(2, '0')}:00`,
            end_time: `${(hour + 1).toString().padStart(2, '0')}:00`,
            available_seats: faker.datatype.number({ min: 1, max: 3 })
          });
        }
      } else {
        // General availability throughout opening hours
        timeSlots.push({
          start_time: dayHours.open,
          end_time: dayHours.close,
          available_seats: faker.datatype.number({ min: 5, max: 20 })
        });
      }

      schedule.push({
        day,
        day_name: ['Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato', 'Domenica'][day - 1],  
        time_slots: timeSlots
      });
    }

    return { schedule, exceptions: [] };
  }

  private generateImages(categoryType: BusinessCategory, count: number = 2): string[] {
    const images = [];
    const categoryKeywords = {
      bar: 'coffee,bar,drink',
      ristorante: 'food,restaurant,dining',
      spa: 'spa,wellness,relaxation',
      palestra: 'gym,fitness,exercise',
      bellezza: 'beauty,salon,cosmetics',
      abbigliamento: 'fashion,clothing,style',
      alimentari: 'food,grocery,market',
      default: 'business,commercial,service'
    };

    const keyword = categoryKeywords[categoryType] || categoryKeywords.default;
    
    for (let i = 0; i < count; i++) {
      images.push(faker.image.business(800, 600, true));
    }

    return images;
  }

  public async generateDeal(
    businessId: string, 
    categoryId: string,
    dateRange: { start: Date; end: Date }
  ): Promise<any> {
    // Get business details for success level
    const { data: business } = await supabase
      .from('businesses')
      .select('*')
      .eq('id', businessId)
      .single();

    if (!business) {
      throw new Error('Business not found');
    }

    const categoryType = this.categoryMapping[categoryId] || 'default';
    const businessSuccessLevel = (business as any)._success_level || 'medium';
    
    // Generate deal dates within the specified range
    const dealStartDate = faker.date.between(dateRange.start, dateRange.end);
    const dealEndDate = faker.date.between(
      dealStartDate,
      new Date(Math.min(dateRange.end.getTime(), dealStartDate.getTime() + (30 * 24 * 60 * 60 * 1000))) // Max 30 days duration
    );

    // Check for seasonal effects
    const seasonalMultipliers = this.getSeasonalMultipliers(categoryType, dealStartDate);
    const holidayInfo = this.isHoliday(dealStartDate);

    // Generate deal content
    const isSeasonalDeal = seasonalMultipliers.dealMultiplier > 1.2;
    const title = this.generateDealTitle(categoryType, isSeasonalDeal);
    const description = this.generateDealDescription(categoryType, title);
    const pricing = this.generatePricing(categoryType, businessSuccessLevel, seasonalMultipliers);
    const timeSlots = this.generateTimeSlots(categoryType);
    const images = this.generateImages(categoryType, faker.datatype.number({ min: 1, max: 4 }));

    // Terms and conditions
    const termsConditions = [
      "Prenotazione obbligatoria",
      "Non rimborsabile", 
      "Valido fino alla data di scadenza",
      "Non cumulabile con altre offerte",
      "Presenta il codice al momento dell'utilizzo"
    ].join(" • ");

    return {
      business_id: businessId,
      category_id: categoryId,
      title,
      description,
      original_price: pricing.originalPrice,
      discounted_price: pricing.discountedPrice,
      discount_percentage: pricing.discountPercentage,
      start_date: dealStartDate.toISOString().split('T')[0],
      end_date: dealEndDate.toISOString().split('T')[0],
      time_slots: timeSlots,
      images,
      terms_conditions: termsConditions,
      status: 'published',
      auto_confirm: faker.datatype.boolean(),
      fake: true,
      // Additional metadata
      _seasonal_multiplier: seasonalMultipliers.dealMultiplier,
      _holiday_info: holidayInfo.isHoliday ? holidayInfo : null
    };
  }

  public async generateMultipleDeals(
    businessIds: string[],
    dateRange: { start: Date; end: Date },
    dealsPerBusiness: number = 1,
    onProgress?: (current: number, total: number, message: string) => void
  ): Promise<FakeDataResult> {
    const result: FakeDataResult = {
      created: 0,
      errors: [],
      warnings: []
    };

    const totalDeals = businessIds.length * dealsPerBusiness;
    let processed = 0;

    for (const businessId of businessIds) {
      try {
        // Get business category
        const { data: business } = await supabase
          .from('businesses')
          .select('category_id')
          .eq('id', businessId)
          .single();

        if (!business) {
          result.errors.push(`Business ${businessId} not found`);
          continue;
        }

        for (let i = 0; i < dealsPerBusiness; i++) {
          try {
            processed++;
            onProgress?.(processed, totalDeals, `Creazione deal per business ${businessId}`);

            const deal = await this.generateDeal(businessId, business.category_id, dateRange);
            
            const { error } = await supabase
              .from('deals')
              .insert(deal);

            if (error) {
              result.errors.push(`Deal for business ${businessId}: ${error.message}`);
            } else {
              result.created++;
            }

            // Small delay to prevent overwhelming the database
            await new Promise(resolve => setTimeout(resolve, 50));

          } catch (error) {
            result.errors.push(`Deal for business ${businessId}: ${error}`);
          }
        }

      } catch (error) {
        result.errors.push(`Business ${businessId}: ${error}`);
      }
    }

    return result;
  }
}