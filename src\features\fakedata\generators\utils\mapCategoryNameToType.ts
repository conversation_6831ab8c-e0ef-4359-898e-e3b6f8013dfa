import { BusinessCategory } from "../../types";

export const mapCategoryNameToType = (categoryName: string): BusinessCategory => {
    const name = categoryName.toLowerCase();
    
    if (name.includes('bar') || name.includes('caffè') || name.includes('pub')) {
      return 'bar';
    }
    if (name.includes('ristorante') || name.includes('pizzeria') || name.includes('trattoria')) {
      return 'ristorante';
    }
    if (name.includes('aliment') || name.includes('super') || name.includes('market')) {
      return 'alimentari';
    }
    if (name.includes('parrucchier') || name.includes('estet') || name.includes('bellezza')) {
      return 'bellezza';
    }
    if (name.includes('abbigliam') || name.includes('moda') || name.includes('boutique')) {
      return 'abbigliamento';
    }
    if (name.includes('palestra') || name.includes('fitness') || name.includes('gym')) {
      return 'palestra';
    }
    if (name.includes('spa') || name.includes('benessere') || name.includes('relax')) {
      return 'spa';
    }
    if (name.includes('cinema') || name.includes('multisala') || name.includes('film')) {
      return 'cinema';
    }
    if (name.includes('fioraio') || name.includes('fiori') || name.includes('floreale') || name.includes('fioreria')) {
      return 'fioraio';
    }
    if (name.includes('hotel') || name.includes('albergo') || name.includes('alloggi') || name.includes('bed') || name.includes('breakfast')) {
      return 'hotel';
    }
    if (name.includes('teatro') || name.includes('spettacolo') || name.includes('teatrale')) {
      return 'teatro';
    }
    if (name.includes('aperitiv') || name.includes('cocktail') || name.includes('spritz') || name.includes('mixology')) {
      return 'aperitivo';
    }
    
    return 'default';
  }