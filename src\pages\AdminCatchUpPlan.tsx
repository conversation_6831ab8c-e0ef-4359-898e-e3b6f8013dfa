import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
  AreaChart,
  Area,
} from "recharts";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  Calendar,
  Target,
  BarChart3,
  PieChart,
  Settings,
  Zap,
  CreditCard,
  Server,
  Brain,
} from "lucide-react";

function AdminCatchUpPlan() {
  // Enhanced parameters with transaction details and granular costs
  const [params, setParams] = useState({
    // Business Parameters

    // Business Partner Parameters
    foundingPartners: 100,
    trialMonths: 6,
    commissionStarter: 8,
    commissionProfessional: 6,
    commissionEnterprise: 4,

    subscriptionStarter: 29,
    subscriptionProfessional: 79,
    subscriptionEnterprise: 199,

    // Transaction Parameters
    avgTransactionsPerBusinessPerMonth: 10,
    avgTransactionValue: 50,
    transactionFeeFixed: 0.5, // Fixed fee per transaction in EUR
    transactionFeePercentage: 2.5, // Percentage fee on transaction value

    // Consumer Parameters
    earlyVipUsers: 300,
    vipTrialMonths: 6,
    plusTierPrice: 9.99,
    conciergeTierPrice: 29.99,

    // Growth Parameters
    monthlyPartnerGrowth: 25,
    monthlyConsumerGrowth: 500,
    businessChurnRate: 3,
    consumerChurnRate: 5,

    // Granular Cost Parameters
    // Personnel Costs
    salariesAndBenefits: 15000,

    // AI Usage-Based Cost Parameters
    // LLM Costs
    llmCostPerTokenInput: 0.0001,      // €0.0001 per input token (realistic for mixed models)
    llmCostPerTokenOutput: 0.0003,     // €0.0003 per output token
    
    // Business LLM usage patterns (monthly per business)
    businessLLMTokensInput: 50000,     // Input tokens per business per month
    businessLLMTokensOutput: 25000,    // Output tokens per business per month
    
    // Consumer LLM usage patterns (monthly per consumer) 
    consumerLLMTokensInput: 2000,      // Input tokens per consumer per month
    consumerLLMTokensOutput: 1000,     // Output tokens per consumer per month
    
    // AI Voice Costs
    aiVoiceCostPerMinute: 0.15,        // €0.15 per minute of voice processing
    
    // Business Voice usage patterns (monthly per business)
    businessVoiceMinutes: 120,         // Voice minutes per business per month
    
    // Consumer Voice usage patterns (monthly per consumer)
    consumerVoiceMinutes: 5,           // Voice minutes per consumer per month
    
    // AI Voice adoption rates
    businessVoiceAdoptionRate: 0.6,    // 60% of businesses use voice features
    consumerVoiceAdoptionRate: 0.3,    // 30% of consumers use voice features

    // Technology Costs (remove old AI costs)
    cloudInfrastructureAWS: 12000,
    cloudInfrastructureOther: 5000,
    softwareLicenses: 8000,

    // Operations Costs
    officeRent: 6000,
    marketing: 15000,
    legal: 3000,
    accounting: 2000,
    insurance: 1500,
    other: 2500,

    // Acquisition Costs
    businessCAC: 5000,
    consumerCAC: 25,
  });

  const [results, setResults] = useState({
    monthlyData: [],
    kpis: {
      year1Revenue: 0,
      year2Revenue: 0,
      year3Revenue: 0,
      finalARR: 0,
      finalMRR: 0,
      breakEvenMonth: "Not reached",
      maxBurnRate: 0,
      totalInvestmentNeeded: 0,
      finalBusinessPartners: 0,
      finalConsumers: 0,
      finalTransactions: 0,
      ltv: 0,
      cac: 0,
      ltvCacRatio: 0,
    },
    transactionData: [],
    costBreakdown: {
      personnel: 0,
      aiProviders: 0,
      infrastructure: 0,
      operations: 0,
      total: 0,
    },
  });

  const [isLoading, setIsLoading] = useState(false);

  // Calculate financial projections with detailed transaction modeling
  const calculateProjections = () => {
    setIsLoading(true);

    setTimeout(() => {
      const monthlyData = [];
      const transactionData = [];
      const months = 36;

      let businessPartners = 0;
      let consumers = 0;
      let foundingPartnersActive = 0;
      let standardPartnersActive = 0;
      let cumulativeRevenue = 0;
      let cumulativeCosts = 0;

      // Calculate monthly fixed costs
      const monthlyFixedCosts =
        params.salariesAndBenefits +
        params.cloudInfrastructureAWS +
        params.cloudInfrastructureOther +
        params.softwareLicenses +
        params.officeRent +
        params.marketing +
        params.legal +
        params.accounting +
        params.insurance +
        params.other;

      for (let month = 1; month <= months; month++) {
        // Business Partner Growth
        if (month <= 6) {
          const newFounding = Math.min(
            params.foundingPartners - foundingPartnersActive,
            month === 1
              ? 10
              : month === 2
              ? 10
              : month === 3
              ? 15
              : month === 4
              ? 20
              : month === 5
              ? 25
              : 20
          );
          foundingPartnersActive += newFounding;
        } else {
          standardPartnersActive += params.monthlyPartnerGrowth;
          // Apply churn
          foundingPartnersActive *= 1 - params.businessChurnRate / 100;
          standardPartnersActive *= 1 - params.businessChurnRate / 100;
        }

        businessPartners = foundingPartnersActive + standardPartnersActive;

        // Consumer Growth
        if (month <= 6) {
          consumers +=
            month === 1
              ? 100
              : month === 2
              ? 150
              : month === 3
              ? 150
              : month === 4
              ? 200
              : month === 5
              ? 250
              : 350;
        } else {
          consumers += params.monthlyConsumerGrowth;
          consumers *= 1 - params.consumerChurnRate / 100;
        }

        // Transaction Calculations
        const totalTransactions =
          businessPartners * params.avgTransactionsPerBusinessPerMonth;
        const totalTransactionValue =
          totalTransactions * params.avgTransactionValue;

        // Transaction Revenue (only from active paying partners)
        let transactionRevenue = 0;
        let activePayingPartners = 0;

        if (month > params.trialMonths) {
          // Founding partners start paying after trial
          activePayingPartners += foundingPartnersActive;
          const foundingTransactions =
            foundingPartnersActive * params.avgTransactionsPerBusinessPerMonth;
          const foundingTransactionValue =
            foundingTransactions * params.avgTransactionValue;
          transactionRevenue +=
            foundingTransactions * params.transactionFeeFixed +
            foundingTransactionValue * (params.transactionFeePercentage / 100);
        }

        if (month > 6) {
          // Standard partners (no trial)
          activePayingPartners += standardPartnersActive;
          const standardTransactions =
            standardPartnersActive * params.avgTransactionsPerBusinessPerMonth;
          const standardTransactionValue =
            standardTransactions * params.avgTransactionValue;
          transactionRevenue +=
            standardTransactions * params.transactionFeeFixed +
            standardTransactionValue * (params.transactionFeePercentage / 100);
        }

        // Subscription Revenue
        let subscriptionRevenue = 0;
        if (month > params.trialMonths) {
          subscriptionRevenue +=
            foundingPartnersActive * params.subscriptionStarter;
        }
        if (month > 6) {
          subscriptionRevenue +=
            standardPartnersActive * params.subscriptionStarter;
        }

        const businessRevenue = transactionRevenue + subscriptionRevenue;

        // Consumer Revenue
        let consumerRevenue = 0;
        const earlyVipActive = Math.min(consumers, params.earlyVipUsers);
        const standardConsumers = Math.max(0, consumers - params.earlyVipUsers);

        if (month > params.vipTrialMonths) {
          // Early VIP start paying
          consumerRevenue += earlyVipActive * 0.8 * params.conciergeTierPrice; // 80% conversion
        }

        // Standard consumers
        const plusUsers = standardConsumers * 0.2;
        const conciergeUsers = standardConsumers * 0.05;
        consumerRevenue +=
          plusUsers * params.plusTierPrice +
          conciergeUsers * params.conciergeTierPrice;

        const totalRevenue = businessRevenue + consumerRevenue;

        // Calculate AI costs based on usage
        const businessLLMCosts = businessPartners * (
          (params.businessLLMTokensInput * params.llmCostPerTokenInput) +
          (params.businessLLMTokensOutput * params.llmCostPerTokenOutput)
        );
        
        const consumerLLMCosts = consumers * (
          (params.consumerLLMTokensInput * params.llmCostPerTokenInput) +
          (params.consumerLLMTokensOutput * params.llmCostPerTokenOutput)
        );
        
        const businessVoiceCosts = businessPartners * 
          params.businessVoiceMinutes * 
          params.aiVoiceCostPerMinute * 
          params.businessVoiceAdoptionRate;
        
        const consumerVoiceCosts = consumers * 
          params.consumerVoiceMinutes * 
          params.aiVoiceCostPerMinute * 
          params.consumerVoiceAdoptionRate;
        
        const totalAICosts = businessLLMCosts + consumerLLMCosts + businessVoiceCosts + consumerVoiceCosts;
        
        // Variable costs (scale with users) - including AI costs
        const variableCosts = businessPartners * 50 + consumers * 2 + totalAICosts;
        const totalCosts = monthlyFixedCosts + variableCosts;
        const netIncome = totalRevenue - totalCosts;

        cumulativeRevenue += totalRevenue;
        cumulativeCosts += totalCosts;

        // Calculate key metrics
        const mrr = totalRevenue;
        const arr = mrr * 12;
        const burnRate = totalCosts - totalRevenue;
        const runway = burnRate > 0 ? Math.max(0, 36 - month) : Infinity;

        monthlyData.push({
          month,
          monthName: new Date(2025, 6 + month - 1).toLocaleDateString("en-US", {
            month: "short",
            year: "numeric",
          }),
          businessPartners: Math.round(businessPartners),
          consumers: Math.round(consumers),
          activePayingPartners: Math.round(activePayingPartners),
          totalTransactions: Math.round(totalTransactions),
          totalTransactionValue: Math.round(totalTransactionValue),
          transactionRevenue: Math.round(transactionRevenue),
          subscriptionRevenue: Math.round(subscriptionRevenue),
          businessRevenue: Math.round(businessRevenue),
          consumerRevenue: Math.round(consumerRevenue),
          totalRevenue: Math.round(totalRevenue),
          fixedCosts: Math.round(monthlyFixedCosts),
          variableCosts: Math.round(variableCosts),
          totalCosts: Math.round(totalCosts),
          netIncome: Math.round(netIncome),
          mrr: Math.round(mrr),
          arr: Math.round(arr),
          burnRate: Math.round(burnRate),
          runway: runway === Infinity ? "Infinite" : Math.round(runway),
          cumulativeRevenue: Math.round(cumulativeRevenue),
          cumulativeCosts: Math.round(cumulativeCosts),
          // AI Cost Details
          businessLLMCosts: Math.round(businessLLMCosts),
          consumerLLMCosts: Math.round(consumerLLMCosts),
          businessVoiceCosts: Math.round(businessVoiceCosts),
          consumerVoiceCosts: Math.round(consumerVoiceCosts),
          totalAICosts: Math.round(totalAICosts),
        });

        // Transaction data for detailed analysis
        transactionData.push({
          month,
          monthName: new Date(2025, 6 + month - 1).toLocaleDateString("en-US", {
            month: "short",
          }),
          totalTransactions: Math.round(totalTransactions),
          activePayingPartners: Math.round(activePayingPartners),
          avgTransactionsPerPartner: params.avgTransactionsPerBusinessPerMonth,
          avgTransactionValue: params.avgTransactionValue,
          fixedFeeRevenue: Math.round(
            activePayingPartners *
              params.avgTransactionsPerBusinessPerMonth *
              params.transactionFeeFixed
          ),
          percentageFeeRevenue: Math.round(
            activePayingPartners *
              params.avgTransactionsPerBusinessPerMonth *
              params.avgTransactionValue *
              (params.transactionFeePercentage / 100)
          ),
          totalTransactionRevenue: Math.round(transactionRevenue),
        });
      }

      // Calculate cost breakdown (using final month for AI costs)
      const finalMonthData = monthlyData[monthlyData.length - 1];
      const costBreakdown = {
        personnel: params.salariesAndBenefits,
        aiProviders: finalMonthData ? finalMonthData.totalAICosts : 0,
        infrastructure:
          params.cloudInfrastructureAWS +
          params.cloudInfrastructureOther +
          params.softwareLicenses,
        operations:
          params.officeRent +
          params.marketing +
          params.legal +
          params.accounting +
          params.insurance +
          params.other,
        total: monthlyFixedCosts + (finalMonthData ? finalMonthData.totalAICosts : 0),
      };

      // Calculate KPIs
      const finalMonth = monthlyData[monthlyData.length - 1];
      const year1Revenue = monthlyData
        .slice(0, 12)
        .reduce((sum, m) => sum + m.totalRevenue, 0);
      const year2Revenue = monthlyData
        .slice(12, 24)
        .reduce((sum, m) => sum + m.totalRevenue, 0);
      const year3Revenue = monthlyData
        .slice(24, 36)
        .reduce((sum, m) => sum + m.totalRevenue, 0);

      const breakEvenMonth = monthlyData.findIndex((m) => m.netIncome > 0) + 1;
      const maxBurnRate = Math.max(...monthlyData.map((m) => m.burnRate));

      const kpis = {
        year1Revenue,
        year2Revenue,
        year3Revenue,
        finalARR: finalMonth.arr,
        finalMRR: finalMonth.mrr,
        breakEvenMonth: breakEvenMonth
          ? breakEvenMonth.toString()
          : "Not reached",
        maxBurnRate,
        totalInvestmentNeeded: Math.max(
          ...monthlyData.map((m) => m.cumulativeCosts - m.cumulativeRevenue)
        ),
        finalBusinessPartners: finalMonth.businessPartners,
        finalConsumers: finalMonth.consumers,
        finalTransactions: finalMonth.totalTransactions,
        ltv:
          ((params.avgTransactionsPerBusinessPerMonth *
            params.avgTransactionValue *
            (params.transactionFeePercentage / 100) +
            params.subscriptionStarter) *
            12) /
          (params.businessChurnRate / 100),
        cac: params.businessCAC,
        ltvCacRatio:
          ((params.avgTransactionsPerBusinessPerMonth *
            params.avgTransactionValue *
            (params.transactionFeePercentage / 100) +
            params.subscriptionStarter) *
            12) /
          (params.businessChurnRate / 100) /
          params.businessCAC,
      };

      setResults({ monthlyData, kpis, transactionData, costBreakdown });
      setIsLoading(false);
    }, 300);
  };

  useEffect(() => {
    calculateProjections();
  }, [params]);

  const handleParamChange = (key, value) => {
    setParams((prev) => ({
      ...prev,
      [key]: parseFloat(value) || 0,
    }));
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat("it-IT", {
      style: "currency",
      currency: "EUR",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatNumber = (value) => {
    return new Intl.NumberFormat("it-IT").format(value);
  };

  const resetToDefaults = () => {
    setParams({
      foundingPartners: 100,
      trialMonths: 6,
      commissionStarter: 8,
      commissionProfessional: 6,
      commissionEnterprise: 4,
      subscriptionStarter: 29,
      subscriptionProfessional: 79,
      subscriptionEnterprise: 199,
      avgTransactionsPerBusinessPerMonth: 200,
      avgTransactionValue: 50,
      transactionFeeFixed: 0.5,
      transactionFeePercentage: 2.5,
      earlyVipUsers: 300,
      vipTrialMonths: 6,
      plusTierPrice: 9.99,
      conciergeTierPrice: 29.99,
      monthlyPartnerGrowth: 25,
      monthlyConsumerGrowth: 500,
      businessChurnRate: 3,
      consumerChurnRate: 5,
      salariesAndBenefits: 85000,
      cloudInfrastructureAWS: 12000,
      cloudInfrastructureOther: 5000,
      softwareLicenses: 8000,
      officeRent: 6000,
      marketing: 15000,
      legal: 3000,
      accounting: 2000,
      insurance: 1500,
      other: 2500,
      businessCAC: 5000,
      consumerCAC: 25,
      // AI Usage-Based Cost Parameters
      llmCostPerTokenInput: 0.0001,
      llmCostPerTokenOutput: 0.0003,
      businessLLMTokensInput: 50000,
      businessLLMTokensOutput: 25000,
      consumerLLMTokensInput: 2000,
      consumerLLMTokensOutput: 1000,
      aiVoiceCostPerMinute: 0.15,
      businessVoiceMinutes: 120,
      consumerVoiceMinutes: 5,
      businessVoiceAdoptionRate: 0.6,
      consumerVoiceAdoptionRate: 0.3,
    });
  };

  return (
    <div className="financial-simulator">
      <div className="max-w-7xl mx-auto space-y-6 p-4">
        {/* Header */}
        <div className="text-center space-y-4 py-8">
          <h1 className="text-5xl font-bold header-gradient">
            CatchUp Financial Simulator
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Interactive investor dashboard with detailed transaction modeling
            and granular cost control
          </p>
          <div className="flex justify-center gap-4">
            <Badge variant="outline" className="px-4 py-2">
              <CreditCard className="w-4 h-4 mr-2" />
              Transaction Modeling
            </Badge>
            <Badge variant="outline" className="px-4 py-2">
              <Server className="w-4 h-4 mr-2" />
              Granular Costs
            </Badge>
            <Badge variant="outline" className="px-4 py-2">
              <Brain className="w-4 h-4 mr-2" />
              AI Cost Tracking
            </Badge>
          </div>
        </div>

        {/* Enhanced Key Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="metric-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                Monthly Transactions
              </CardTitle>
              <CreditCard className="h-5 w-5 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="kpi-value text-blue-600">
                {isLoading ? (
                  <div className="loading-shimmer h-10 w-32 rounded"></div>
                ) : (
                  formatNumber(results.kpis.finalTransactions || 0)
                )}
              </div>
              <p className="kpi-label">Final month transaction volume</p>
            </CardContent>
          </Card>

          <Card className="metric-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                Break-even Month
              </CardTitle>
              <Calendar className="h-5 w-5 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="kpi-value text-green-600">
                {isLoading ? (
                  <div className="loading-shimmer h-10 w-16 rounded"></div>
                ) : (
                  results.kpis.breakEvenMonth
                )}
              </div>
              <p className="kpi-label">Months to profitability</p>
            </CardContent>
          </Card>

          <Card className="metric-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                LTV/CAC Ratio
              </CardTitle>
              <Target className="h-5 w-5 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="kpi-value text-purple-600">
                {isLoading ? (
                  <div className="loading-shimmer h-10 w-20 rounded"></div>
                ) : (
                  `${(results.kpis.ltvCacRatio || 0).toFixed(1)}x`
                )}
              </div>
              <p className="kpi-label">
                <Badge
                  variant={
                    results.kpis.ltvCacRatio > 3 ? "default" : "destructive"
                  }
                  className="mt-1"
                >
                  {results.kpis.ltvCacRatio > 3
                    ? "Healthy"
                    : "Needs improvement"}
                </Badge>
              </p>
            </CardContent>
          </Card>

          <Card className="metric-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                Total Investment
              </CardTitle>
              <TrendingUp className="h-5 w-5 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="kpi-value text-orange-600">
                {isLoading ? (
                  <div className="loading-shimmer h-10 w-32 rounded"></div>
                ) : (
                  formatCurrency(results.kpis.totalInvestmentNeeded || 0)
                )}
              </div>
              <p className="kpi-label">Maximum funding needed</p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="sales" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5 h-12">
            <TabsTrigger value="sales" className="flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              Sales
            </TabsTrigger>
            <TabsTrigger value="costs" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Costs
            </TabsTrigger>
            <TabsTrigger value="charts" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              Charts
            </TabsTrigger>
            <TabsTrigger value="tables" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              Tables
            </TabsTrigger>
            <TabsTrigger value="comparison" className="flex items-center gap-2">
              <PieChart className="w-4 h-4" />
              Analysis
            </TabsTrigger>
          </TabsList>

          {/* Sales Tab */}
          <TabsContent value="sales" className="space-y-6">
            <h2 className="text-2xl font-bold">Sales</h2>

            {/* Transaction Model Section */}
            <div className="space-y-4">
              <h3 className="text-xl font-semibold">Transaction Model</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card className="sales-section">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base">
                      <CreditCard className="w-4 h-4" />
                      Transactions/Month
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <Label htmlFor="avgTransactionsPerBusinessPerMonth">
                      Per Business
                    </Label>
                    <Input
                      id="avgTransactionsPerBusinessPerMonth"
                      type="number"
                      value={params.avgTransactionsPerBusinessPerMonth}
                      onChange={(e) =>
                        handleParamChange(
                          "avgTransactionsPerBusinessPerMonth",
                          e.target.value
                        )
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </CardContent>
                </Card>

                <Card className="sales-section">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base">
                      <DollarSign className="w-4 h-4" />
                      Transaction Value
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <Label htmlFor="avgTransactionValue">Average (€)</Label>
                    <Input
                      id="avgTransactionValue"
                      type="number"
                      value={params.avgTransactionValue}
                      onChange={(e) =>
                        handleParamChange("avgTransactionValue", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </CardContent>
                </Card>

                <Card className="sales-section">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base">
                      <Zap className="w-4 h-4" />
                      Fixed Fee
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <Label htmlFor="transactionFeeFixed">
                      Per Transaction (€)
                    </Label>
                    <Input
                      id="transactionFeeFixed"
                      type="number"
                      step="0.01"
                      value={params.transactionFeeFixed}
                      onChange={(e) =>
                        handleParamChange("transactionFeeFixed", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </CardContent>
                </Card>

                <Card className="sales-section">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base">
                      <TrendingUp className="w-4 h-4" />
                      Percentage Fee
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <Label htmlFor="transactionFeePercentage">Rate (%)</Label>
                    <Input
                      id="transactionFeePercentage"
                      type="number"
                      step="0.1"
                      value={params.transactionFeePercentage}
                      onChange={(e) =>
                        handleParamChange(
                          "transactionFeePercentage",
                          e.target.value
                        )
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </CardContent>
                </Card>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Business Partner Parameters */}
              <Card className="sales-section">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    Business Partners
                  </CardTitle>
                  <CardDescription>
                    Configure business partner acquisition and pricing
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="foundingPartners">
                      Founding Partners Target
                    </Label>
                    <Input
                      id="foundingPartners"
                      type="number"
                      value={params.foundingPartners}
                      onChange={(e) =>
                        handleParamChange("foundingPartners", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="trialMonths">Trial Period (months)</Label>
                    <Input
                      id="trialMonths"
                      type="number"
                      value={params.trialMonths}
                      onChange={(e) =>
                        handleParamChange("trialMonths", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="subscriptionStarter">
                      Starter Subscription (€/month)
                    </Label>
                    <Input
                      id="subscriptionStarter"
                      type="number"
                      value={params.subscriptionStarter}
                      onChange={(e) =>
                        handleParamChange("subscriptionStarter", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="subscriptionProfessional">
                      Professional Subscription (€/month)
                    </Label>
                    <Input
                      id="subscriptionProfessional"
                      type="number"
                      value={params.subscriptionProfessional}
                      onChange={(e) =>
                        handleParamChange(
                          "subscriptionProfessional",
                          e.target.value
                        )
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="subscriptionEnterprise">
                      Enterprise Subscription (€/month)
                    </Label>
                    <Input
                      id="subscriptionEnterprise"
                      type="number"
                      value={params.subscriptionEnterprise}
                      onChange={(e) =>
                        handleParamChange(
                          "subscriptionEnterprise",
                          e.target.value
                        )
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Growth Parameters */}
              <Card className="sales-section">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Growth & Churn
                  </CardTitle>
                  <CardDescription>
                    Configure growth rates and customer retention
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="monthlyPartnerGrowth">
                      Monthly Partner Growth
                    </Label>
                    <Input
                      id="monthlyPartnerGrowth"
                      type="number"
                      value={params.monthlyPartnerGrowth}
                      onChange={(e) =>
                        handleParamChange(
                          "monthlyPartnerGrowth",
                          e.target.value
                        )
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="monthlyConsumerGrowth">
                      Monthly Consumer Growth
                    </Label>
                    <Input
                      id="monthlyConsumerGrowth"
                      type="number"
                      value={params.monthlyConsumerGrowth}
                      onChange={(e) =>
                        handleParamChange(
                          "monthlyConsumerGrowth",
                          e.target.value
                        )
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="businessChurnRate">
                      Business Churn Rate (%)
                    </Label>
                    <Input
                      id="businessChurnRate"
                      type="number"
                      step="0.1"
                      value={params.businessChurnRate}
                      onChange={(e) =>
                        handleParamChange("businessChurnRate", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="consumerChurnRate">
                      Consumer Churn Rate (%)
                    </Label>
                    <Input
                      id="consumerChurnRate"
                      type="number"
                      step="0.1"
                      value={params.consumerChurnRate}
                      onChange={(e) =>
                        handleParamChange("consumerChurnRate", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="businessCAC">
                      Business Customer Acquisition Cost (€)
                    </Label>
                    <Input
                      id="businessCAC"
                      type="number"
                      value={params.businessCAC}
                      onChange={(e) =>
                        handleParamChange("businessCAC", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Consumer Parameters */}
              <Card className="sales-section">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    Consumer Plans
                  </CardTitle>
                  <CardDescription>
                    Configure consumer subscription tiers and pricing
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="earlyVipUsers">
                      Early VIP Users Target
                    </Label>
                    <Input
                      id="earlyVipUsers"
                      type="number"
                      value={params.earlyVipUsers}
                      onChange={(e) =>
                        handleParamChange("earlyVipUsers", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="vipTrialMonths">
                      VIP Trial Period (months)
                    </Label>
                    <Input
                      id="vipTrialMonths"
                      type="number"
                      value={params.vipTrialMonths}
                      onChange={(e) =>
                        handleParamChange("vipTrialMonths", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="plusTierPrice">
                      Plus Tier Price (€/month)
                    </Label>
                    <Input
                      id="plusTierPrice"
                      type="number"
                      step="0.01"
                      value={params.plusTierPrice}
                      onChange={(e) =>
                        handleParamChange("plusTierPrice", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="conciergeTierPrice">
                      Concierge Tier Price (€/month)
                    </Label>
                    <Input
                      id="conciergeTierPrice"
                      type="number"
                      step="0.01"
                      value={params.conciergeTierPrice}
                      onChange={(e) =>
                        handleParamChange("conciergeTierPrice", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="consumerCAC">
                      Consumer Acquisition Cost (€)
                    </Label>
                    <Input
                      id="consumerCAC"
                      type="number"
                      value={params.consumerCAC}
                      onChange={(e) =>
                        handleParamChange("consumerCAC", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sales Charts Section */}
            <div className="space-y-6 mt-8">
              <h3 className="text-xl font-semibold">
                Sales Performance Charts
              </h3>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Revenue Growth Chart */}
                <Card className="chart-container">
                  <CardHeader>
                    <CardTitle>Revenue Growth Projection</CardTitle>
                    <CardDescription>
                      Total revenue breakdown by source over time
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <AreaChart data={results.monthlyData.slice(0, 12)}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis dataKey="monthName" tick={{ fontSize: 12 }} />
                        <YAxis
                          tickFormatter={(value) => formatCurrency(value)}
                          tick={{ fontSize: 12 }}
                        />
                        <Tooltip
                          formatter={(value) => formatCurrency(value)}
                          labelStyle={{ color: "#374151" }}
                          contentStyle={{
                            backgroundColor: "white",
                            border: "1px solid #e5e7eb",
                            borderRadius: "8px",
                          }}
                        />
                        <Legend />
                        <Area
                          type="monotone"
                          dataKey="businessRevenue"
                          stackId="1"
                          stroke="#3b82f6"
                          fill="#3b82f6"
                          fillOpacity={0.8}
                          name="Business Revenue"
                        />
                        <Area
                          type="monotone"
                          dataKey="consumerRevenue"
                          stackId="1"
                          stroke="#10b981"
                          fill="#10b981"
                          fillOpacity={0.8}
                          name="Consumer Revenue"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* User Growth Chart */}
                <Card className="chart-container">
                  <CardHeader>
                    <CardTitle>User Base Growth</CardTitle>
                    <CardDescription>
                      Business partners and consumer growth over time
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={results.monthlyData.slice(0, 12)}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis dataKey="monthName" tick={{ fontSize: 12 }} />
                        <YAxis yAxisId="left" tick={{ fontSize: 12 }} />
                        <YAxis
                          yAxisId="right"
                          orientation="right"
                          tick={{ fontSize: 12 }}
                        />
                        <Tooltip
                          labelStyle={{ color: "#374151" }}
                          contentStyle={{
                            backgroundColor: "white",
                            border: "1px solid #e5e7eb",
                            borderRadius: "8px",
                          }}
                        />
                        <Legend />
                        <Line
                          yAxisId="left"
                          type="monotone"
                          dataKey="businessPartners"
                          stroke="#8b5cf6"
                          strokeWidth={3}
                          name="Business Partners"
                        />
                        <Line
                          yAxisId="right"
                          type="monotone"
                          dataKey="consumers"
                          stroke="#f59e0b"
                          strokeWidth={3}
                          name="Consumers"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Subscription Revenue Chart */}
                <Card className="chart-container">
                  <CardHeader>
                    <CardTitle>Subscription vs Transaction Revenue</CardTitle>
                    <CardDescription>
                      Revenue composition showing recurring vs transactional
                      income
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={results.monthlyData.slice(0, 12)}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis dataKey="monthName" tick={{ fontSize: 12 }} />
                        <YAxis
                          tickFormatter={(value) => formatCurrency(value)}
                          tick={{ fontSize: 12 }}
                        />
                        <Tooltip
                          formatter={(value) => formatCurrency(value)}
                          labelStyle={{ color: "#374151" }}
                          contentStyle={{
                            backgroundColor: "white",
                            border: "1px solid #e5e7eb",
                            borderRadius: "8px",
                          }}
                        />
                        <Legend />
                        <Bar
                          dataKey="subscriptionRevenue"
                          fill="#3b82f6"
                          name="Subscription Revenue"
                        />
                        <Bar
                          dataKey="transactionRevenue"
                          fill="#10b981"
                          name="Transaction Revenue"
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* MRR Growth Chart */}
                <Card className="chart-container">
                  <CardHeader>
                    <CardTitle>Monthly Recurring Revenue (MRR)</CardTitle>
                    <CardDescription>
                      Predictable revenue growth trajectory
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={results.monthlyData.slice(0, 12)}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis dataKey="monthName" tick={{ fontSize: 12 }} />
                        <YAxis
                          tickFormatter={(value) => formatCurrency(value)}
                          tick={{ fontSize: 12 }}
                        />
                        <Tooltip
                          formatter={(value) => formatCurrency(value)}
                          labelStyle={{ color: "#374151" }}
                          contentStyle={{
                            backgroundColor: "white",
                            border: "1px solid #e5e7eb",
                            borderRadius: "8px",
                          }}
                        />
                        <Legend />
                        <Line
                          type="monotone"
                          dataKey="mrr"
                          stroke="#8b5cf6"
                          strokeWidth={4}
                          name="Monthly Recurring Revenue"
                        />
                        <Line
                          type="monotone"
                          dataKey="arr"
                          stroke="#f59e0b"
                          strokeWidth={2}
                          strokeDasharray="5 5"
                          name="Annual Recurring Revenue"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Cost Tab */}
          <TabsContent value="costs" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Costs</h2>
              <Button onClick={resetToDefaults} variant="outline">
                Reset to Defaults
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Usage-Based AI Costs */}
              <Card className="parameter-section">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="w-5 h-5" />
                    Usage-Based AI Costs
                  </CardTitle>
                  <CardDescription>
                    LLM and AI Voice costs based on actual user behavior patterns
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="llmCostPerTokenInput">
                      LLM Input Cost (€/Token)
                    </Label>
                    <Input
                      id="llmCostPerTokenInput"
                      type="number"
                      step="0.00001"
                      value={params.llmCostPerTokenInput}
                      onChange={(e) =>
                        handleParamChange("llmCostPerTokenInput", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="llmCostPerTokenOutput">
                      LLM Output Cost (€/Token)
                    </Label>
                    <Input
                      id="llmCostPerTokenOutput"
                      type="number"
                      step="0.00001"
                      value={params.llmCostPerTokenOutput}
                      onChange={(e) =>
                        handleParamChange("llmCostPerTokenOutput", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="businessLLMTokensInput">
                      Business LLM Input Tokens (Monthly)
                    </Label>
                    <Input
                      id="businessLLMTokensInput"
                      type="number"
                      value={params.businessLLMTokensInput}
                      onChange={(e) =>
                        handleParamChange("businessLLMTokensInput", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="businessLLMTokensOutput">
                      Business LLM Output Tokens (Monthly)
                    </Label>
                    <Input
                      id="businessLLMTokensOutput"
                      type="number"
                      value={params.businessLLMTokensOutput}
                      onChange={(e) =>
                        handleParamChange("businessLLMTokensOutput", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="consumerLLMTokensInput">
                      Consumer LLM Input Tokens (Monthly)
                    </Label>
                    <Input
                      id="consumerLLMTokensInput"
                      type="number"
                      value={params.consumerLLMTokensInput}
                      onChange={(e) =>
                        handleParamChange("consumerLLMTokensInput", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="consumerLLMTokensOutput">
                      Consumer LLM Output Tokens (Monthly)
                    </Label>
                    <Input
                      id="consumerLLMTokensOutput"
                      type="number"
                      value={params.consumerLLMTokensOutput}
                      onChange={(e) =>
                        handleParamChange("consumerLLMTokensOutput", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="aiVoiceCostPerMinute">
                      AI Voice Cost (€/Minute)
                    </Label>
                    <Input
                      id="aiVoiceCostPerMinute"
                      type="number"
                      step="0.01"
                      value={params.aiVoiceCostPerMinute}
                      onChange={(e) =>
                        handleParamChange("aiVoiceCostPerMinute", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="businessVoiceMinutes">
                      Business Voice Minutes (Monthly)
                    </Label>
                    <Input
                      id="businessVoiceMinutes"
                      type="number"
                      value={params.businessVoiceMinutes}
                      onChange={(e) =>
                        handleParamChange("businessVoiceMinutes", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="consumerVoiceMinutes">
                      Consumer Voice Minutes (Monthly)
                    </Label>
                    <Input
                      id="consumerVoiceMinutes"
                      type="number"
                      value={params.consumerVoiceMinutes}
                      onChange={(e) =>
                        handleParamChange("consumerVoiceMinutes", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="businessVoiceAdoptionRate">
                      Business Voice Adoption Rate (%)
                    </Label>
                    <Input
                      id="businessVoiceAdoptionRate"
                      type="number"
                      step="0.01"
                      value={params.businessVoiceAdoptionRate}
                      onChange={(e) =>
                        handleParamChange("businessVoiceAdoptionRate", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="consumerVoiceAdoptionRate">
                      Consumer Voice Adoption Rate (%)
                    </Label>
                    <Input
                      id="consumerVoiceAdoptionRate"
                      type="number"
                      step="0.01"
                      value={params.consumerVoiceAdoptionRate}
                      onChange={(e) =>
                        handleParamChange("consumerVoiceAdoptionRate", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Infrastructure Costs */}
              <Card className="parameter-section">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Server className="w-5 h-5" />
                    Infrastructure Costs
                  </CardTitle>
                  <CardDescription>
                    Cloud infrastructure and software licenses
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="cloudInfrastructureAWS">
                      AWS Infrastructure (€/month)
                    </Label>
                    <Input
                      id="cloudInfrastructureAWS"
                      type="number"
                      value={params.cloudInfrastructureAWS}
                      onChange={(e) =>
                        handleParamChange(
                          "cloudInfrastructureAWS",
                          e.target.value
                        )
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cloudInfrastructureOther">
                      Other Cloud Services (€/month)
                    </Label>
                    <Input
                      id="cloudInfrastructureOther"
                      type="number"
                      value={params.cloudInfrastructureOther}
                      onChange={(e) =>
                        handleParamChange(
                          "cloudInfrastructureOther",
                          e.target.value
                        )
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="softwareLicenses">
                      Software Licenses (€/month)
                    </Label>
                    <Input
                      id="softwareLicenses"
                      type="number"
                      value={params.softwareLicenses}
                      onChange={(e) =>
                        handleParamChange("softwareLicenses", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Operations Costs */}
              <Card className="parameter-section">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Operations Costs
                  </CardTitle>
                  <CardDescription>
                    Monthly operational expenses
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="salariesAndBenefits">
                      Salaries & Benefits (€/month)
                    </Label>
                    <Input
                      id="salariesAndBenefits"
                      type="number"
                      value={params.salariesAndBenefits}
                      onChange={(e) =>
                        handleParamChange("salariesAndBenefits", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="marketing">Marketing (€/month)</Label>
                    <Input
                      id="marketing"
                      type="number"
                      value={params.marketing}
                      onChange={(e) =>
                        handleParamChange("marketing", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="officeRent">Office Rent (€/month)</Label>
                    <Input
                      id="officeRent"
                      type="number"
                      value={params.officeRent}
                      onChange={(e) =>
                        handleParamChange("officeRent", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="legal">Legal & Compliance (€/month)</Label>
                    <Input
                      id="legal"
                      type="number"
                      value={params.legal}
                      onChange={(e) =>
                        handleParamChange("legal", e.target.value)
                      }
                      className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Cost Breakdown Summary */}
            <Card className="parameter-section">
              <CardHeader>
                <CardTitle>Monthly Cost Breakdown</CardTitle>
                <CardDescription>
                  Real-time calculation of total monthly costs
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {formatCurrency(results.costBreakdown.personnel || 0)}
                    </div>
                    <div className="text-sm text-gray-600">Personnel</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {formatCurrency(results.costBreakdown.aiProviders || 0)}
                    </div>
                    <div className="text-sm text-gray-600">AI Providers</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {formatCurrency(
                        results.costBreakdown.infrastructure || 0
                      )}
                    </div>
                    <div className="text-sm text-gray-600">Infrastructure</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">
                      {formatCurrency(results.costBreakdown.operations || 0)}
                    </div>
                    <div className="text-sm text-gray-600">Operations</div>
                  </div>
                </div>
                <div className="mt-4 text-center p-4 bg-gray-100 rounded-lg">
                  <div className="text-3xl font-bold text-gray-800">
                    {formatCurrency(results.costBreakdown.total || 0)}
                  </div>
                  <div className="text-sm text-gray-600">
                    Total Monthly Fixed Costs
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Enhanced Charts Tab */}
          <TabsContent value="charts" className="space-y-6">
            <h2 className="text-2xl font-bold">
              Financial Projections & Transaction Analysis
            </h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Transaction Revenue Chart */}
              <Card className="chart-container">
                <CardHeader>
                  <CardTitle>Transaction Revenue Breakdown</CardTitle>
                  <CardDescription>
                    Fixed fees vs percentage fees over time
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={results.transactionData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis dataKey="monthName" tick={{ fontSize: 12 }} />
                      <YAxis
                        tickFormatter={(value) => formatCurrency(value)}
                        tick={{ fontSize: 12 }}
                      />
                      <Tooltip
                        formatter={(value) => formatCurrency(value)}
                        labelStyle={{ color: "#374151" }}
                        contentStyle={{
                          backgroundColor: "white",
                          border: "1px solid #e5e7eb",
                          borderRadius: "8px",
                        }}
                      />
                      <Legend />
                      <Area
                        type="monotone"
                        dataKey="fixedFeeRevenue"
                        stackId="1"
                        stroke="#3b82f6"
                        fill="#3b82f6"
                        fillOpacity={0.8}
                        name="Fixed Fee Revenue"
                      />
                      <Area
                        type="monotone"
                        dataKey="percentageFeeRevenue"
                        stackId="1"
                        stroke="#10b981"
                        fill="#10b981"
                        fillOpacity={0.8}
                        name="Percentage Fee Revenue"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Transaction Volume Chart */}
              <Card className="chart-container">
                <CardHeader>
                  <CardTitle>Transaction Volume</CardTitle>
                  <CardDescription>
                    Monthly transaction count and active partners
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={results.transactionData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis dataKey="monthName" tick={{ fontSize: 12 }} />
                      <YAxis yAxisId="left" tick={{ fontSize: 12 }} />
                      <YAxis
                        yAxisId="right"
                        orientation="right"
                        tick={{ fontSize: 12 }}
                      />
                      <Tooltip
                        labelStyle={{ color: "#374151" }}
                        contentStyle={{
                          backgroundColor: "white",
                          border: "1px solid #e5e7eb",
                          borderRadius: "8px",
                        }}
                      />
                      <Legend />
                      <Line
                        yAxisId="left"
                        type="monotone"
                        dataKey="totalTransactions"
                        stroke="#8b5cf6"
                        strokeWidth={3}
                        name="Total Transactions"
                      />
                      <Line
                        yAxisId="right"
                        type="monotone"
                        dataKey="activePayingPartners"
                        stroke="#f59e0b"
                        strokeWidth={3}
                        name="Active Paying Partners"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* AI Cost Breakdown Chart */}
              <Card className="chart-container">
                <CardHeader>
                  <CardTitle>AI Cost Breakdown</CardTitle>
                  <CardDescription>
                    LLM vs Voice costs by user type over time
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={results.monthlyData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis dataKey="monthName" tick={{ fontSize: 12 }} />
                      <YAxis
                        tickFormatter={(value) => formatCurrency(value)}
                        tick={{ fontSize: 12 }}
                      />
                      <Tooltip
                        formatter={(value) => formatCurrency(value)}
                        labelStyle={{ color: "#374151" }}
                        contentStyle={{
                          backgroundColor: "white",
                          border: "1px solid #e5e7eb",
                          borderRadius: "8px",
                        }}
                      />
                      <Legend />
                      <Area
                        type="monotone"
                        dataKey="businessLLMCosts"
                        stackId="1"
                        stroke="#3b82f6"
                        fill="#3b82f6"
                        fillOpacity={0.8}
                        name="Business LLM"
                      />
                      <Area
                        type="monotone"
                        dataKey="consumerLLMCosts"
                        stackId="1"
                        stroke="#10b981"
                        fill="#10b981"
                        fillOpacity={0.8}
                        name="Consumer LLM"
                      />
                      <Area
                        type="monotone"
                        dataKey="businessVoiceCosts"
                        stackId="1"
                        stroke="#8b5cf6"
                        fill="#8b5cf6"
                        fillOpacity={0.8}
                        name="Business Voice"
                      />
                      <Area
                        type="monotone"
                        dataKey="consumerVoiceCosts"
                        stackId="1"
                        stroke="#f59e0b"
                        fill="#f59e0b"
                        fillOpacity={0.8}
                        name="Consumer Voice"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Cost Breakdown Chart */}
              <Card className="chart-container">
                <CardHeader>
                  <CardTitle>Cost Structure Analysis</CardTitle>
                  <CardDescription>
                    Fixed vs variable costs over time
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={results.monthlyData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis dataKey="monthName" tick={{ fontSize: 12 }} />
                      <YAxis
                        tickFormatter={(value) => formatCurrency(value)}
                        tick={{ fontSize: 12 }}
                      />
                      <Tooltip
                        formatter={(value) => formatCurrency(value)}
                        labelStyle={{ color: "#374151" }}
                        contentStyle={{
                          backgroundColor: "white",
                          border: "1px solid #e5e7eb",
                          borderRadius: "8px",
                        }}
                      />
                      <Legend />
                      <Area
                        type="monotone"
                        dataKey="fixedCosts"
                        stackId="1"
                        stroke="#ef4444"
                        fill="#ef4444"
                        fillOpacity={0.8}
                        name="Fixed Costs"
                      />
                      <Area
                        type="monotone"
                        dataKey="variableCosts"
                        stackId="1"
                        stroke="#f97316"
                        fill="#f97316"
                        fillOpacity={0.8}
                        name="Variable Costs"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Revenue vs Costs */}
              <Card className="chart-container">
                <CardHeader>
                  <CardTitle>Profitability Analysis</CardTitle>
                  <CardDescription>
                    Revenue vs costs and net income
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={results.monthlyData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis dataKey="monthName" tick={{ fontSize: 12 }} />
                      <YAxis
                        tickFormatter={(value) => formatCurrency(value)}
                        tick={{ fontSize: 12 }}
                      />
                      <Tooltip
                        formatter={(value) => formatCurrency(value)}
                        labelStyle={{ color: "#374151" }}
                        contentStyle={{
                          backgroundColor: "white",
                          border: "1px solid #e5e7eb",
                          borderRadius: "8px",
                        }}
                      />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="totalRevenue"
                        stroke="#3b82f6"
                        strokeWidth={3}
                        name="Total Revenue"
                      />
                      <Line
                        type="monotone"
                        dataKey="totalCosts"
                        stroke="#ef4444"
                        strokeWidth={3}
                        name="Total Costs"
                      />
                      <Line
                        type="monotone"
                        dataKey="netIncome"
                        stroke="#10b981"
                        strokeWidth={3}
                        name="Net Income"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Enhanced Tables Tab with Transposed Table */}
          <TabsContent value="tables" className="space-y-6">
            <h2 className="text-2xl font-bold">Detailed Financial Data</h2>

            {/* Transposed Financial Table */}
            <Card className="table-container">
              <CardHeader>
                <CardTitle>Financial Projections (Transposed View)</CardTitle>
                <CardDescription>
                  Months as columns for easier comparison
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto table-scroll">
                  <table className="w-full text-xs">
                    <thead className="bg-gray-50">
                      <tr className="border-b">
                        <th className="text-left p-2 font-semibold sticky left-0 bg-gray-50 min-w-[150px]">
                          Metric
                        </th>
                        {results.monthlyData.slice(0, 12).map((month) => (
                          <th
                            key={month.month}
                            className="text-center p-2 font-semibold min-w-[80px]"
                          >
                            {month.monthName}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b hover:bg-gray-50">
                        <td className="p-2 font-medium sticky left-0 bg-white">
                          Business Partners
                        </td>
                        {results.monthlyData.slice(0, 12).map((month) => (
                          <td key={month.month} className="text-center p-2">
                            {formatNumber(month.businessPartners)}
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50">
                        <td className="p-2 font-medium sticky left-0 bg-white">
                          Active Paying Partners
                        </td>
                        {results.monthlyData.slice(0, 12).map((month) => (
                          <td key={month.month} className="text-center p-2">
                            {formatNumber(month.activePayingPartners)}
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50">
                        <td className="p-2 font-medium sticky left-0 bg-white">
                          Total Transactions
                        </td>
                        {results.monthlyData.slice(0, 12).map((month) => (
                          <td key={month.month} className="text-center p-2">
                            {formatNumber(month.totalTransactions)}
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50">
                        <td className="p-2 font-medium sticky left-0 bg-white">
                          Transaction Revenue
                        </td>
                        {results.monthlyData.slice(0, 12).map((month) => (
                          <td key={month.month} className="text-center p-2">
                            {formatCurrency(month.transactionRevenue)}
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50">
                        <td className="p-2 font-medium sticky left-0 bg-white">
                          Subscription Revenue
                        </td>
                        {results.monthlyData.slice(0, 12).map((month) => (
                          <td key={month.month} className="text-center p-2">
                            {formatCurrency(month.subscriptionRevenue)}
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50">
                        <td className="p-2 font-medium sticky left-0 bg-white">
                          Consumer Revenue
                        </td>
                        {results.monthlyData.slice(0, 12).map((month) => (
                          <td key={month.month} className="text-center p-2">
                            {formatCurrency(month.consumerRevenue)}
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50 bg-blue-50">
                        <td className="p-2 font-bold sticky left-0 bg-blue-50">
                          Total Revenue
                        </td>
                        {results.monthlyData.slice(0, 12).map((month) => (
                          <td
                            key={month.month}
                            className="text-center p-2 font-semibold"
                          >
                            {formatCurrency(month.totalRevenue)}
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50">
                        <td className="p-2 font-medium sticky left-0 bg-white">
                          Fixed Costs
                        </td>
                        {results.monthlyData.slice(0, 12).map((month) => (
                          <td key={month.month} className="text-center p-2">
                            {formatCurrency(month.fixedCosts)}
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50">
                        <td className="p-2 font-medium sticky left-0 bg-white">
                          Variable Costs
                        </td>
                        {results.monthlyData.slice(0, 12).map((month) => (
                          <td key={month.month} className="text-center p-2">
                            {formatCurrency(month.variableCosts)}
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50 bg-red-50">
                        <td className="p-2 font-bold sticky left-0 bg-red-50">
                          Total Costs
                        </td>
                        {results.monthlyData.slice(0, 12).map((month) => (
                          <td
                            key={month.month}
                            className="text-center p-2 font-semibold"
                          >
                            {formatCurrency(month.totalCosts)}
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50 bg-green-50">
                        <td className="p-2 font-bold sticky left-0 bg-green-50">
                          Net Income
                        </td>
                        {results.monthlyData.slice(0, 12).map((month) => (
                          <td
                            key={month.month}
                            className={`text-center p-2 font-semibold ${
                              month.netIncome > 0
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {formatCurrency(month.netIncome)}
                          </td>
                        ))}
                      </tr>
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            {/* Transaction Analysis Table - Transposed */}
            <Card className="table-container">
              <CardHeader>
                <CardTitle>Transaction Analysis (Transposed View)</CardTitle>
                <CardDescription>
                  Months as columns for detailed transaction metrics comparison
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto table-scroll">
                  <table className="w-full text-xs">
                    <thead className="bg-gray-50">
                      <tr className="border-b">
                        <th className="text-left p-2 font-semibold sticky left-0 bg-gray-50 min-w-[180px]">
                          Transaction Metric
                        </th>
                        {results.transactionData.slice(0, 12).map((month) => (
                          <th
                            key={month.month}
                            className="text-center p-2 font-semibold min-w-[80px]"
                          >
                            {month.monthName}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b hover:bg-gray-50">
                        <td className="p-2 font-medium sticky left-0 bg-white">
                          Active Paying Partners
                        </td>
                        {results.transactionData.slice(0, 12).map((month) => (
                          <td key={month.month} className="text-center p-2">
                            {formatNumber(month.activePayingPartners)}
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50">
                        <td className="p-2 font-medium sticky left-0 bg-white">
                          Total Transactions
                        </td>
                        {results.transactionData.slice(0, 12).map((month) => (
                          <td key={month.month} className="text-center p-2">
                            {formatNumber(month.totalTransactions)}
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50">
                        <td className="p-2 font-medium sticky left-0 bg-white">
                          Avg Transactions/Partner
                        </td>
                        {results.transactionData.slice(0, 12).map((month) => (
                          <td key={month.month} className="text-center p-2">
                            {formatNumber(month.avgTransactionsPerPartner)}
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50">
                        <td className="p-2 font-medium sticky left-0 bg-white">
                          Avg Transaction Value
                        </td>
                        {results.transactionData.slice(0, 12).map((month) => (
                          <td key={month.month} className="text-center p-2">
                            {formatCurrency(month.avgTransactionValue)}
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50">
                        <td className="p-2 font-medium sticky left-0 bg-white">
                          Fixed Fee Revenue
                        </td>
                        {results.transactionData.slice(0, 12).map((month) => (
                          <td key={month.month} className="text-center p-2">
                            {formatCurrency(month.fixedFeeRevenue)}
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50">
                        <td className="p-2 font-medium sticky left-0 bg-white">
                          Percentage Fee Revenue
                        </td>
                        {results.transactionData.slice(0, 12).map((month) => (
                          <td key={month.month} className="text-center p-2">
                            {formatCurrency(month.percentageFeeRevenue)}
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50 bg-green-50">
                        <td className="p-2 font-bold sticky left-0 bg-green-50">
                          Total Transaction Revenue
                        </td>
                        {results.transactionData.slice(0, 12).map((month) => (
                          <td
                            key={month.month}
                            className="text-center p-2 font-semibold text-green-600"
                          >
                            {formatCurrency(month.totalTransactionRevenue)}
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50 bg-blue-50">
                        <td className="p-2 font-bold sticky left-0 bg-blue-50">
                          Revenue per Transaction
                        </td>
                        {results.transactionData.slice(0, 12).map((month) => (
                          <td
                            key={month.month}
                            className="text-center p-2 font-semibold text-blue-600"
                          >
                            {month.totalTransactions > 0 
                              ? formatCurrency(month.totalTransactionRevenue / month.totalTransactions)
                              : formatCurrency(0)
                            }
                          </td>
                        ))}
                      </tr>
                      <tr className="border-b hover:bg-gray-50 bg-purple-50">
                        <td className="p-2 font-bold sticky left-0 bg-purple-50">
                          Revenue per Partner
                        </td>
                        {results.transactionData.slice(0, 12).map((month) => (
                          <td
                            key={month.month}
                            className="text-center p-2 font-semibold text-purple-600"
                          >
                            {month.activePayingPartners > 0 
                              ? formatCurrency(month.totalTransactionRevenue / month.activePayingPartners)
                              : formatCurrency(0)
                            }
                          </td>
                        ))}
                      </tr>
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Enhanced Analysis Tab */}
          <TabsContent value="comparison" className="space-y-6">
            <h2 className="text-2xl font-bold">
              Scenario Analysis & Cost Optimization
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="parameter-section">
                <CardHeader>
                  <CardTitle>Transaction Model Impact</CardTitle>
                  <CardDescription>
                    Test different transaction fee structures
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Button
                      onClick={() => {
                        setParams((prev) => ({
                          ...prev,
                          transactionFeeFixed: 1.0,
                          transactionFeePercentage: 2.0,
                        }));
                      }}
                      variant="outline"
                      className="w-full scenario-button"
                    >
                      <CreditCard className="w-4 h-4 mr-2" />
                      Higher Fixed Fee Model
                    </Button>
                    <Button
                      onClick={() => {
                        setParams((prev) => ({
                          ...prev,
                          transactionFeeFixed: 0.25,
                          transactionFeePercentage: 3.5,
                        }));
                      }}
                      variant="outline"
                      className="w-full scenario-button"
                    >
                      <CreditCard className="w-4 h-4 mr-2" />
                      Higher Percentage Fee Model
                    </Button>
                    <Button
                      onClick={() => {
                        setParams((prev) => ({
                          ...prev,
                          avgTransactionsPerBusinessPerMonth:
                            prev.avgTransactionsPerBusinessPerMonth * 1.5,
                        }));
                      }}
                      variant="outline"
                      className="w-full scenario-button"
                    >
                      <TrendingUp className="w-4 h-4 mr-2" />
                      High Transaction Volume (+50%)
                    </Button>
                    <Button
                      onClick={() => {
                        setParams((prev) => ({
                          ...prev,
                          // Optimize for profitability
                          avgTransactionsPerBusinessPerMonth: 60,
                          avgTransactionValue: 75,
                          transactionFeeFixed: 0.75,
                          transactionFeePercentage: 2.0,
                          subscriptionStarter: 39,
                          trialMonths: 3,
                          // Reduce high fixed costs
                          salariesAndBenefits: 25000,
                          marketing: 8000,
                          cloudInfrastructureAWS: 6000,
                          officeRent: 3000,
                          // Optimize growth
                          monthlyPartnerGrowth: 30,
                          businessChurnRate: 2,
                          // Better transaction fees
                        }));
                      }}
                      variant="default"
                      className="w-full scenario-button bg-green-600 hover:bg-green-700"
                    >
                      <Target className="w-4 h-4 mr-2" />
                      🎯 Profitability Optimization
                    </Button>
                    <Button
                      onClick={() => {
                        setParams((prev) => ({
                          ...prev,
                          // Aggressive profitability scenario
                          avgTransactionsPerBusinessPerMonth: 80,
                          avgTransactionValue: 85,
                          transactionFeeFixed: 1.0,
                          transactionFeePercentage: 1.8,
                          subscriptionStarter: 49,
                          subscriptionProfessional: 99,
                          trialMonths: 2,
                          // Lean cost structure
                          salariesAndBenefits: 20000,
                          marketing: 5000,
                          cloudInfrastructureAWS: 4000,
                          officeRent: 2000,
                          legal: 1500,
                          // Aggressive growth
                          monthlyPartnerGrowth: 40,
                          businessChurnRate: 1.5,
                          // Higher conversion to Pro
                          foundingPartners: 150,
                        }));
                      }}
                      variant="default"
                      className="w-full scenario-button bg-blue-600 hover:bg-blue-700"
                    >
                      <Zap className="w-4 h-4 mr-2" />
                      ⚡ Fast Break-even (Month 8)
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card className="parameter-section">
                <CardHeader>
                  <CardTitle>Cost Optimization</CardTitle>
                  <CardDescription>
                    Test different cost scenarios
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Button
                      onClick={() => {
                        setParams((prev) => ({
                          ...prev,
                          llmCostPerTokenInput: prev.llmCostPerTokenInput * 0.7,
                          llmCostPerTokenOutput: prev.llmCostPerTokenOutput * 0.7,
                          aiVoiceCostPerMinute: prev.aiVoiceCostPerMinute * 0.7,
                        }));
                      }}
                      variant="outline"
                      className="w-full scenario-button"
                    >
                      <Brain className="w-4 h-4 mr-2" />
                      Optimized AI Costs (-30%)
                    </Button>
                    <Button
                      onClick={() => {
                        setParams((prev) => ({
                          ...prev,
                          cloudInfrastructureAWS:
                            prev.cloudInfrastructureAWS * 0.8,
                          softwareLicenses: prev.softwareLicenses * 0.8,
                        }));
                      }}
                      variant="outline"
                      className="w-full scenario-button"
                    >
                      <Server className="w-4 h-4 mr-2" />
                      Infrastructure Savings (-20%)
                    </Button>
                    <Button
                      onClick={resetToDefaults}
                      variant="outline"
                      className="w-full scenario-button"
                    >
                      <Settings className="w-4 h-4 mr-2" />
                      Reset to Base Case
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Key Metrics Summary */}
            <Card className="parameter-section">
              <CardHeader>
                <CardTitle>Key Performance Indicators</CardTitle>
                <CardDescription>Current scenario summary</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {formatCurrency(results.kpis.year1Revenue || 0)}
                    </div>
                    <div className="text-sm text-gray-600">Year 1 Revenue</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {results.kpis.breakEvenMonth}
                    </div>
                    <div className="text-sm text-gray-600">
                      Break-even Month
                    </div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {(results.kpis.ltvCacRatio || 0).toFixed(1)}x
                    </div>
                    <div className="text-sm text-gray-600">LTV/CAC Ratio</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">
                      {formatCurrency(results.kpis.totalInvestmentNeeded || 0)}
                    </div>
                    <div className="text-sm text-gray-600">Max Investment</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

export default AdminCatchUpPlan;
