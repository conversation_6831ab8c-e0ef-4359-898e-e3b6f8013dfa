import React, { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";

interface TeamMember {
  name: string;
  role: string;
  description: string;
  avatar: string;
  videoPlaceholder: string;
  videoId?: string;
}

interface TeamHexGridProps {
  members: TeamMember[];
}

const TeamHexGrid: React.FC<TeamHexGridProps> = ({ members }) => {
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null);

  // Extract first name from the format "Name - Role"
  const getFirstName = (fullName: string) => fullName.split(" - ")[0];

  // Extract role from the format "Name - Role" or use the role prop
  const getRole = (member: TeamMember) => {
    const parts = member.name.split(" - ");
    return parts.length > 1 ? parts[1] : member.role;
  };

  return (
    <>
      <div className="hex-grid px-2 sm:px-0">
        {/* First row - 2 members */}
        <div className="hex-row">
          <div className="flex justify-center" style={{ width: '100%', maxWidth: '450px' }}>
            {members.slice(0, 2).map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className={`hex-item ${index === 0 ? 'featured' : ''}`}
                onClick={() => setSelectedMember(member)}
              >
                <div
                  className="hex-content"
                  style={{ backgroundImage: `url(${member.avatar})` }}
                >
                  <div className="hex-overlay">
                    <div className="hex-text">
                      <div className="hex-name">{getFirstName(member.name)}</div>
                      <div className="hex-role">{getRole(member)}</div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Second row - 3 members */}
        <div className="hex-row second-row">
          <div className="flex justify-center flex-wrap sm:flex-nowrap" style={{ width: '100%', maxWidth: '650px' }}>
            {members.slice(2, 5).map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
                className="hex-item"
                onClick={() => setSelectedMember(member)}
              >
                <div
                  className="hex-content"
                  style={{ backgroundImage: `url(${member.avatar})` }}
                >
                  <div className="hex-overlay">
                    <div className="hex-text">
                      <div className="hex-name">{getFirstName(member.name)}</div>
                      <div className="hex-role">{getRole(member)}</div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Mobile layout for small screens */}
        <div className="md:hidden mt-4 mb-2 text-center text-xs sm:text-sm text-gray-500">
          <p>Tocca un membro del team per maggiori informazioni</p>
        </div>
      </div>

      {/* Member Details Dialog */}
      {selectedMember && (
        <Dialog open={!!selectedMember} onOpenChange={() => setSelectedMember(null)}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle className="text-xl font-semibold mb-2">
                {getFirstName(selectedMember.name)}
              </DialogTitle>
            </DialogHeader>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="md:w-1/3">
                <img
                  src={selectedMember.avatar}
                  alt={getFirstName(selectedMember.name)}
                  className="rounded-lg w-full aspect-square object-cover"
                />
              </div>
              <div className="md:w-2/3">
                <h3 className="text-lg font-bold text-primary-600 mb-1">{getRole(selectedMember)}</h3>
                <p className="text-gray-700 mb-4">{selectedMember.description}</p>
                <div className="bg-primary-50 p-3 rounded-lg">
                  <h4 className="font-medium text-primary-700 mb-2">Competenze:</h4>
                  <div className="flex flex-wrap gap-2">
                    <span className="bg-white text-primary-700 px-2 py-1 rounded-full text-xs border border-primary-200">Assistenza 24/7</span>
                    <span className="bg-white text-primary-700 px-2 py-1 rounded-full text-xs border border-primary-200">Personalizzabile</span>
                    <span className="bg-white text-primary-700 px-2 py-1 rounded-full text-xs border border-primary-200">Integrazione</span>
                  </div>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}


    </>
  );
};

export default TeamHexGrid;
