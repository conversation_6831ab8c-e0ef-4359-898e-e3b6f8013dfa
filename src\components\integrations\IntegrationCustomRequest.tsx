
import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Zap } from "lucide-react";

export const IntegrationCustomRequest = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Integrazioni Personalizzate
        </CardTitle>
        <CardDescription>
          Hai bisogno di un'integrazione specifica? Contattaci per sviluppare soluzioni su misura
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <Textarea
              placeholder="Descrivi l'integrazione di cui hai bisogno..."
              className="min-h-[100px]"
            />
          </div>
          <div className="flex flex-col justify-between">
            <Button>
              Richiedi Integrazione
            </Button>
            <p className="text-xs text-gray-500 mt-2">
              Ti risponderemo entro 24 ore
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
