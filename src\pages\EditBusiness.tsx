import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import MainLayout from "@/layouts/MainLayout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, Building2, Save, Users, Plus, X, Image } from "lucide-react";

interface BusinessData {
  id: string;
  name: string;
  description: string | null;
  email: string | null;
  phone: string | null;
  website: string | null;
  address: string | null;
  city: string | null;
  state: string | null;
  country: string | null;
  zip_code: string | null;
  owner_id: string | null;
  category_id: string | null;
  photos: string[] | null;
}

interface User {
  id: string;
  email: string;
  raw_user_meta_data: {
    first_name?: string;
    last_name?: string;
    full_name?: string;
  };
}

interface Category {
  id: string;
  name: string;
}

const EditBusiness = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [business, setBusiness] = useState<BusinessData | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedOwnerId, setSelectedOwnerId] = useState<string>("");
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("none");
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [photoUrls, setPhotoUrls] = useState<string[]>([]);

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    email: "",
    phone: "",
    website: "",
    address: "",
    city: "",
    state: "",
    country: "",
    zip_code: "",
  });

  useEffect(() => {
    if (id) {
      fetchBusiness();
      fetchUsers();
      fetchCategories();
    }
  }, [id]);

  const fetchBusiness = async () => {
    try {
      const { data, error } = await supabase
        .from("businesses")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;

      setBusiness(data);
      setSelectedOwnerId(data.owner_id || "");
      setSelectedCategoryId(data.category_id || "none");
      setPhotoUrls(data.photos || []);
      setFormData({
        name: data.name || "",
        description: data.description || "",
        email: data.email || "",
        phone: data.phone || "",
        website: data.website || "",
        address: data.address || "",
        city: data.city || "",
        state: data.state || "",
        country: data.country || "",
        zip_code: data.zip_code || "",
      });
    } catch (error) {
      console.error("Error fetching business:", error);
      toast({
        title: "Errore",
        description: "Impossibile caricare i dati dell'attività",
        variant: "destructive",
      });
      navigate(-1);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from("categories")
        .select("id, name")
        .order("name");

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const fetchUsers = async () => {
    try {
      const { data, error } = await supabase
        .from("users_with_details")
        .select("id, email, first_name, last_name");

      if (error) throw error;

      const formattedUsers: User[] = (data || []).map((user) => ({
        id: user.id,
        email: user.email || "",
        raw_user_meta_data: {
          first_name: user.first_name,
          last_name: user.last_name,
        },
      }));

      setUsers(formattedUsers);
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const getUserDisplayName = (user: User) => {
    const firstName = user.raw_user_meta_data?.first_name;
    const lastName = user.raw_user_meta_data?.last_name;
    const fullName = user.raw_user_meta_data?.full_name;

    if (firstName && lastName) {
      return `${firstName} ${lastName}`;
    } else if (fullName) {
      return fullName;
    } else {
      return user.email;
    }
  };

  const addPhotoUrl = () => {
    setPhotoUrls([...photoUrls, ""]);
  };

  const updatePhotoUrl = (index: number, url: string) => {
    const newUrls = [...photoUrls];
    newUrls[index] = url;
    setPhotoUrls(newUrls);
  };

  const removePhotoUrl = (index: number) => {
    setPhotoUrls(photoUrls.filter((_, i) => i !== index));
  };

  const handleSave = async () => {
    if (!business) return;

    setSaving(true);
    try {
      const { error } = await supabase
        .from("businesses")
        .update({
          name: formData.name,
          description: formData.description || null,
          email: formData.email || null,
          phone: formData.phone || null,
          website: formData.website || null,
          address: formData.address || null,
          city: formData.city || null,
          state: formData.state || null,
          country: formData.country || null,
          zip_code: formData.zip_code || null,
          category_id:
            selectedCategoryId === "none" ? null : selectedCategoryId,
          owner_id: selectedOwnerId || null,
          photos: photoUrls.filter(url => url.trim() !== ""),
          updated_at: new Date().toISOString(),
        })
        .eq("id", business.id);

      if (error) throw error;

      toast({
        title: "Successo",
        description: "Attività aggiornata con successo",
      });

      navigate(-1);
    } catch (error) {
      console.error("Error updating business:", error);
      toast({
        title: "Errore",
        description: "Impossibile aggiornare l'attività",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-6 max-w-4xl">
          <div className="animate-pulse">
            <div className="h-8 w-64 bg-gray-300 rounded mb-2"></div>
            <div className="h-4 w-96 bg-gray-300 rounded mb-8"></div>
            <div className="h-96 bg-gray-300 rounded"></div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!business) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-6 max-w-4xl">
          <div className="text-center py-8">
            <Building2 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-gray-500">Attività non trovata</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-6 max-w-4xl">
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-4">
            <Button variant="outline" size="sm" onClick={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Indietro
            </Button>
          </div>

          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            <Building2 className="inline-block h-8 w-8 mr-3" />
            Modifica Attività
          </h1>
          <p className="text-gray-600">
            Aggiorna le informazioni dell'attività: {business.name}
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Dettagli Attività</CardTitle>
            <CardDescription>
              Modifica le informazioni generali dell'attività
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Owner Selection Section */}
            <div className="space-y-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-amber-600" />
                <h3 className="text-lg font-medium text-amber-800">
                  Proprietario Attività
                </h3>
              </div>
              <div className="space-y-2">
                <Label htmlFor="owner">Proprietario *</Label>
                <Select
                  value={selectedOwnerId}
                  onValueChange={setSelectedOwnerId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Seleziona il proprietario dell'attività" />
                  </SelectTrigger>
                  <SelectContent>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">
                            {getUserDisplayName(user)}
                          </span>
                          <span className="text-sm text-gray-500">
                            {user.email}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">Nome Attività *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Nome dell'attività"
                  required
                />
              </div>
              {/* Category Selection */}
              <div className="space-y-2">
                <Label htmlFor="category">Categoria</Label>
                <Select
                  value={selectedCategoryId}
                  onValueChange={setSelectedCategoryId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Seleziona una categoria" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Nessuna categoria</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Telefono</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  placeholder="+39 ************"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="website">Sito Web</Label>
                <Input
                  id="website"
                  value={formData.website}
                  onChange={(e) => handleInputChange("website", e.target.value)}
                  placeholder="https://www.esempio.com"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Descrizione</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  handleInputChange("description", e.target.value)
                }
                placeholder="Descrivi la tua attività..."
                rows={3}
              />
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Indirizzo</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="address">Indirizzo</Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) =>
                      handleInputChange("address", e.target.value)
                    }
                    placeholder="Via Roma, 123"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="city">Città</Label>
                  <Input
                    id="city"
                    value={formData.city}
                    onChange={(e) => handleInputChange("city", e.target.value)}
                    placeholder="Milano"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="zip_code">CAP</Label>
                  <Input
                    id="zip_code"
                    value={formData.zip_code}
                    onChange={(e) =>
                      handleInputChange("zip_code", e.target.value)
                    }
                    placeholder="20100"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="state">Provincia/Regione</Label>
                  <Input
                    id="state"
                    value={formData.state}
                    onChange={(e) => handleInputChange("state", e.target.value)}
                    placeholder="Lombardia"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="country">Paese</Label>
                  <Input
                    id="country"
                    value={formData.country}
                    onChange={(e) =>
                      handleInputChange("country", e.target.value)
                    }
                    placeholder="Italia"
                  />
                </div>
              </div>
            </div>

            {/* Photos Section */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Image className="h-5 w-5" />
                <h3 className="text-lg font-medium">Foto Attività</h3>
              </div>
              <div className="space-y-3">
                {photoUrls.map((url, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={url}
                      onChange={(e) => updatePhotoUrl(index, e.target.value)}
                      placeholder="https://esempio.com/foto.jpg"
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => removePhotoUrl(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  onClick={addPhotoUrl}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Aggiungi Foto URL
                </Button>
              </div>
            </div>

            <div className="flex justify-end gap-4 pt-6">
              <Button
                variant="outline"
                onClick={() => navigate(-1)}
                disabled={saving}
              >
                Annulla
              </Button>
              <Button
                onClick={handleSave}
                disabled={saving || !formData.name.trim() || !selectedOwnerId}
              >
                <Save className="h-4 w-4 mr-2" />
                {saving ? "Salvataggio..." : "Salva Modifiche"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default EditBusiness;
