
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { ChevronsUpDown } from "lucide-react";
import { Business } from "../../../types/types";

interface BusinessHeaderProps {
  isLoading: boolean;
  selectedBusiness: Business | null;
  getBusinessInitials: (name: string) => string;
}

export const BusinessHeader = ({ 
  isLoading, 
  selectedBusiness, 
  getBusinessInitials 
}: BusinessHeaderProps) => {
  return (
    <div className="w-full flex items-center justify-between p-2 cursor-pointer hover:bg-muted/50 rounded-md transition-colors">
      <div className="flex items-center">
        {isLoading ? (
          <div className="h-8 w-8 mr-2 rounded-full bg-gray-200 animate-pulse"></div>
        ) : (
          <Avatar className="h-8 w-8 mr-2 bg-blue-100 border border-blue-200">
            <AvatarFallback className="bg-blue-100 text-blue-600">
              {selectedBusiness ? getBusinessInitials(selectedBusiness.name) : 'B'}
            </AvatarFallback>
          </Avatar>
        )}
        <div className="flex flex-col items-start">
          {isLoading ? (
            <>
              <span className="inline-block w-20 h-4 bg-gray-200 animate-pulse rounded"></span>
              <span className="inline-block w-16 h-3 bg-gray-200 animate-pulse rounded mt-1"></span>
            </>
          ) : (
            <>
              <span className="text-sm font-medium">{selectedBusiness?.name || "Seleziona Attività"}</span>
              <span className="text-xs text-muted-foreground">
                {selectedBusiness?.deal_count || 0} offerte
              </span>
            </>
          )}
        </div>
      </div>
      <ChevronsUpDown className="h-4 w-4 opacity-50" />
    </div>
  );
};
