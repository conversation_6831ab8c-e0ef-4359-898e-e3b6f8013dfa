import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import MainLayout from '@/layouts/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Star, Search, Trash2, MessageSquare } from 'lucide-react';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';

interface Review {
  id: string;
  rating: number;
  comment: string | null;
  created_at: string;
  updated_at: string;
  sentiment: 'very_negative' | 'negative' | 'neutral' | 'positive' | 'very_positive' | 'mixed' | null;
  user_id: string;
  business_id: string;
  booking_id: string;
  user?: {
    email: string;
    first_name?: string;
    last_name?: string;
  };
  business?: {
    name: string;
  };
  booking?: {
    booking_date: string;
    booking_time: string;
  };
}

const AdminReviews = () => {
  const navigate = useNavigate();
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [ratingFilter, setRatingFilter] = useState<string>('all');
  const [sentimentFilter, setSentimentFilter] = useState<string>('all');

  useEffect(() => {
    loadReviews();
  }, []);

  const loadReviews = async () => {
    try {
      setLoading(true);
      
      // Get all reviews with related data
      const { data: reviewsData, error } = await supabase
        .from('reviews')
        .select(`
          *,
          booking:bookings!booking_id(booking_date, booking_time),
          business:businesses!business_id(name)
       
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading reviews:', error);
        toast.error('Errore nel caricamento delle recensioni');
        return;
      }

      // Get user details separately (since auth.users is not accessible via API)
      const userEmails = await Promise.all(
        (reviewsData || []).map(async (review) => {
          
          const { data: userData } = await supabase
          .from('user_details')
          .select('*')
          .eq('id', review.user_id)
          .maybeSingle();
         
          
          
          
          return {
            userId: review.user_id,
            email: userData.email || 'Email non disponibile',
            first_name: userData.first_name,
            last_name: userData.last_name
          };
        })
      );

      // Combine reviews with user data
      const reviewsWithUsers = (reviewsData || []).map(review => ({
        ...review,
        user: userEmails.find(u => u.userId === review.user_id)
      }));

      setReviews(reviewsWithUsers);
    } catch (error) {
      console.error('Error loading reviews:', error);
      toast.error('Errore nel caricamento delle recensioni');
    } finally {
      setLoading(false);
    }
  };

  const deleteReview = async (reviewId: string) => {
    if (!confirm('Sei sicuro di voler eliminare questa recensione?')) {
      return;
    }

    try {
      // Use a transaction to ensure all operations succeed or fail together
      const { data, error } = await supabase.rpc('delete_review_with_business_update' as any, {
        review_id: reviewId
      });

      if (error) {
        console.error('RPC call error:', error);
        toast.error('Errore nell\'eliminazione della recensione: ' + error.message);
        return;
      }

      // Check if the function returned an error in the data
      if (data && data.error) {
        console.error('Transaction error:', data.error);
        toast.error('Errore nell\'eliminazione della recensione: ' + data.error);
        return;
      }

      toast.success('Recensione eliminata con successo');
      loadReviews();
    } catch (error) {
      console.error('Error deleting review:', error);
      toast.error('Errore nell\'eliminazione della recensione');
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  const getSentimentBadge = (sentiment: string | null) => {
    switch (sentiment) {
      case 'very_positive':
      case 'positive':
        return <Badge variant="default" className="bg-green-100 text-green-800">Positivo</Badge>;
      case 'very_negative':
      case 'negative':
        return <Badge variant="destructive">Negativo</Badge>;
      case 'mixed':
        return <Badge variant="outline" className="bg-orange-100 text-orange-800">Misto</Badge>;
      case 'neutral':
      default:
        return <Badge variant="secondary">Neutrale</Badge>;
    }
  };

  const filteredReviews = reviews.filter(review => {
    const matchesSearch = 
      review.business?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.user?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.comment?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRating = ratingFilter === 'all' || review.rating.toString() === ratingFilter;
    const matchesSentiment = sentimentFilter === 'all' || review.sentiment === sentimentFilter;

    return matchesSearch && matchesRating && matchesSentiment;
  });

  if (loading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
              <p className="mt-4 text-gray-600">Caricamento recensioni...</p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Gestione Recensioni</h1>
          <Badge variant="outline" className="text-lg px-3 py-1">
            {filteredReviews.length} recensioni
          </Badge>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Cerca per business, utente o commento..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={ratingFilter} onValueChange={setRatingFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filtra per rating" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tutti i rating</SelectItem>
                  <SelectItem value="5">5 stelle</SelectItem>
                  <SelectItem value="4">4 stelle</SelectItem>
                  <SelectItem value="3">3 stelle</SelectItem>
                  <SelectItem value="2">2 stelle</SelectItem>
                  <SelectItem value="1">1 stella</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sentimentFilter} onValueChange={setSentimentFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filtra per sentiment" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tutti i sentiment</SelectItem>
                  <SelectItem value="very_positive">Molto Positivo</SelectItem>
                  <SelectItem value="positive">Positivo</SelectItem>
                  <SelectItem value="neutral">Neutrale</SelectItem>
                  <SelectItem value="negative">Negativo</SelectItem>
                  <SelectItem value="very_negative">Molto Negativo</SelectItem>
                  <SelectItem value="mixed">Misto</SelectItem>
                </SelectContent>
              </Select>

              <Button 
                variant="outline" 
                onClick={loadReviews}
                className="w-full"
              >
                Aggiorna
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Reviews List */}
        <div className="space-y-4">
          {filteredReviews.map((review) => (
            <Card key={review.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold">{review.business?.name || 'Business non trovato'}</h3>
                      {getSentimentBadge(review.sentiment)}
                    </div>
                    <div className="flex items-center gap-2">
                      {renderStars(review.rating)}
                      <span className="text-sm text-gray-600">
                        ({review.rating}/5)
                      </span>
                    </div>
                  </div>
                  <div className="text-right space-y-1">
                    <p className="text-sm text-gray-600">
                      {format(new Date(review.created_at), 'dd MMM yyyy HH:mm', { locale: it })}
                    </p>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deleteReview(review.id)}
                      className="text-red-600 hover:text-red-800 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="space-y-3">
                  {review.comment && (
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="flex items-start gap-2">
                        <MessageSquare className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                        <p className="text-sm">{review.comment}</p>
                      </div>
                    </div>
                  )}
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                    <div>
                      <span className="font-medium">Utente:</span>{' '}
                      {review.user?.first_name && review.user?.last_name 
                        ? `${review.user.first_name} ${review.user.last_name}`
                        : review.user?.email
                      }
                    </div>
                    <div>
                      <span className="font-medium">Email:</span> {review.user?.email}
                    </div>
                    <div>
                      <span className="font-medium">Prenotazione:</span>{' '}
                      {review.booking?.booking_date && review.booking?.booking_time
                        ? `${format(new Date(review.booking.booking_date), 'dd/MM/yyyy', { locale: it })} ${review.booking.booking_time}`
                        : 'Non disponibile'
                      }
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredReviews.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Nessuna recensione trovata
              </h3>
              <p className="text-gray-600">
                {searchTerm || ratingFilter !== 'all' || sentimentFilter !== 'all'
                  ? 'Prova a modificare i filtri di ricerca.'
                  : 'Non ci sono ancora recensioni nel sistema.'
                }
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  );
};

export default AdminReviews;