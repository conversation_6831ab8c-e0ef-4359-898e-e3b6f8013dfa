
import { useState, useEffect, useCallback } from "react";
import MainLayout from "@/layouts/MainLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Store, BarChart4, Utensils, Bed, DoorClosed, Sofa, Armchair, FileJson, Theater, Tickets, Loader2 } from "lucide-react";
import LayoutEditor from "@/components/layout/LayoutEditor";
import LayoutPreview from "@/components/layout/LayoutPreview";
import { toast } from "sonner";
import { useLayoutStore } from "@/store/layoutStore";
import { useBusinessStore } from "@/store/businessStore";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { LayoutElement } from "@/types/types";

const businessCategoryOptions = [
  { value: "ristorante", label: "Ristorante", icon: Utensils },
  { value: "salone", label: "Salone di bellezza", icon: Armchair },
  { value: "palestra", label: "Palestra", icon: BarChart4 },
  { value: "hotel", label: "Hotel", icon: Bed },
  { value: "spa", label: "Spa", icon: Sofa },
  { value: "spettacoli", label: "Spettacoli", icon: Tickets },
];

const LayoutPage = () => {
  // Utilizziamo lo Zustand store invece dello state locale
  const { 
    businessCategory, 
    layoutElements, 
    activeRoomId,
    isLoading,
    setBusinessCategory,
    resetLayout,
    updateLayout,
    loadLayout,
    saveLayout
  } = useLayoutStore();

  const { selectedBusiness } = useBusinessStore();

  const [currentTab, setCurrentTab] = useState("configurazione");
  const [pendingCategory, setPendingCategory] = useState<string | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showJsonDialog, setShowJsonDialog] = useState(false);

  // Load layout when selectedBusiness changes
  useEffect(() => {
    if (selectedBusiness?.id) {
      loadLayout(selectedBusiness.id);
    }
  }, [selectedBusiness?.id, loadLayout]);
  
  const handleSaveLayout = useCallback(async () => {
    if (!selectedBusiness?.id) {
      toast.error("Nessuna attività selezionata");
      return;
    }
    await saveLayout(selectedBusiness.id);
  }, [selectedBusiness?.id, saveLayout]);

  const handleCategoryClick = useCallback((categoryValue: string) => {
    if (businessCategory && businessCategory !== categoryValue) {
      // Se è già selezionata una categoria diversa, mostra la dialog di conferma
      setPendingCategory(categoryValue);
      setShowConfirmDialog(true);
    } else {
      // Se non c'è categoria selezionata o è la stessa, applica direttamente
      setBusinessCategory(categoryValue);
    }
  }, [businessCategory, setBusinessCategory]);

  const handleConfirmCategoryChange = useCallback(() => {
    if (pendingCategory) {
      setBusinessCategory(pendingCategory);
      setPendingCategory(null);
      resetLayout(); // Reset del layout
      toast.success("Categoria cambiata e layout resettato");
    }
    setShowConfirmDialog(false);
  }, [pendingCategory, setBusinessCategory, resetLayout]);

  const handleCancelCategoryChange = useCallback(() => {
    setPendingCategory(null);
    setShowConfirmDialog(false);
  }, []);

  // Memorizziamo questa funzione per evitare ricreazioni ad ogni render
  const handleLayoutUpdate = useCallback((updatedElements: LayoutElement[], updatedActiveRoomId: string | null) => {
    // Utilizziamo un controllo per evitare aggiornamenti non necessari
    const shouldUpdate = 
      JSON.stringify(layoutElements) !== JSON.stringify(updatedElements) ||
      activeRoomId !== updatedActiveRoomId;
    
    if (shouldUpdate) {
      updateLayout(updatedElements, updatedActiveRoomId);
    }
  }, [layoutElements, activeRoomId, updateLayout]);

  // Genera il JSON del layout
  const generateLayoutJson = useCallback(() => {
    const layoutData = {
      businessCategory,
      elements: layoutElements,
      activeRoomId,
      timestamp: new Date().toISOString(),
    };
    
    return JSON.stringify(layoutData, null, 2);
  }, [businessCategory, layoutElements, activeRoomId]);

  // Funzione per aprire la modale JSON
  const handleShowJsonDebug = useCallback(() => {
    setShowJsonDialog(true);
  }, []);

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Layout</h1>
            <p className="text-gray-600">
              Configura lo spazio della tua attività
              {selectedBusiness && (
                <span className="text-primary font-medium"> - {selectedBusiness.name}</span>
              )}
            </p>
          </div>
          <div className="flex gap-2">
            <Button 
              onClick={handleSaveLayout} 
              disabled={isLoading || !selectedBusiness}
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Salvataggio...
                </>
              ) : (
                "Salva Layout"
              )}
            </Button>
            <Button variant="outline" size="icon" onClick={handleShowJsonDebug} title="Visualizza JSON">
              <FileJson className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Tipo di Attività</CardTitle>
            <CardDescription>Seleziona il tipo di attività per personalizzare il layout</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {businessCategoryOptions.map((category) => (
                <div
                  key={category.value}
                  className={`flex flex-col items-center justify-center p-4 border rounded-md cursor-pointer transition-all hover:border-primary hover:bg-primary/5 ${
                    businessCategory === category.value ? "border-primary bg-primary/10" : "border-gray-200"
                  }`}
                  onClick={() => handleCategoryClick(category.value)}
                >
                  <category.icon className="h-8 w-8 mb-2" />
                  <span>{category.label}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        
        <Tabs value={currentTab} onValueChange={setCurrentTab} className="w-full">
          <TabsList className="grid grid-cols-2 w-full max-w-md mb-6">
            <TabsTrigger value="configurazione">Configurazione</TabsTrigger>
            <TabsTrigger value="anteprima">Anteprima</TabsTrigger>
          </TabsList>
          
          <TabsContent value="configurazione" className="space-y-6">
            {!selectedBusiness ? (
              <Card>
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center justify-center p-8 text-center">
                    <Store className="h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium">Nessuna attività selezionata</h3>
                    <p className="text-sm text-gray-500 mt-2">
                      Seleziona un'attività dalla sidebar per configurare il layout
                    </p>
                  </div>
                </CardContent>
              </Card>
            ) : isLoading ? (
              <Card>
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center justify-center p-8 text-center">
                    <Loader2 className="h-12 w-12 text-gray-400 mb-4 animate-spin" />
                    <h3 className="text-lg font-medium">Caricamento layout...</h3>
                  </div>
                </CardContent>
              </Card>
            ) : businessCategory ? (
              <LayoutEditor 
                businessCategory={businessCategory} 
                key={businessCategory} 
                onLayoutUpdate={handleLayoutUpdate}
                initialElements={layoutElements}
                initialActiveRoomId={activeRoomId}
              />
            ) : (
              <Card>
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center justify-center p-8 text-center">
                    <Store className="h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium">Seleziona un tipo di attività</h3>
                    <p className="text-sm text-gray-500 mt-2">
                      Seleziona un tipo di attività per iniziare a configurare il tuo layout
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
          
          <TabsContent value="anteprima" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Anteprima del Layout</CardTitle>
                <CardDescription>
                  Visualizza l'anteprima del layout della tua attività. Trascina gli elementi per posizionarli.
                </CardDescription>
              </CardHeader>
              <CardContent className="min-h-[400px]">
                {businessCategory ? (
                  <LayoutPreview 
                    elements={layoutElements} 
                    activeRoomId={activeRoomId}
                    onLayoutUpdate={handleLayoutUpdate}
                  />
                ) : (
                  <div className="flex flex-col items-center justify-center p-8 text-center">
                    <p className="text-sm text-gray-500">
                      Configura prima il layout nella scheda "Configurazione"
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Dialog di conferma per il cambio categoria */}
        <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Cambiare tipo di attività?</AlertDialogTitle>
              <AlertDialogDescription>
                Cambiando il tipo di attività, il layout corrente verrà resettato. 
                Tutti gli elementi configurati andranno persi. Sei sicuro di voler procedere?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={handleCancelCategoryChange}>Annulla</AlertDialogCancel>
              <AlertDialogAction onClick={handleConfirmCategoryChange}>Conferma</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Modale di debug per visualizzare il JSON */}
        <Dialog open={showJsonDialog} onOpenChange={setShowJsonDialog}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-auto">
            <DialogHeader>
              <DialogTitle>JSON Layout Debug</DialogTitle>
              <DialogDescription>
                Rappresentazione JSON del layout attuale
              </DialogDescription>
            </DialogHeader>
            <pre className="bg-gray-100 p-4 rounded-md text-xs overflow-auto max-h-[70vh]">
              {generateLayoutJson()}
            </pre>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  );
};

export default LayoutPage;
