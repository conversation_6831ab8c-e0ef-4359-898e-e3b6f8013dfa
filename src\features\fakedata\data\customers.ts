import { UserPersona } from '../types';

// Italian customer demographics and behavior patterns
export const italianCustomerData = {
  firstNames: {
    male: [
      "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
      "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", 
      "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>",
      "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"
    ],
    female: [
      "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
      "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>",
      "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Federic<PERSON>",
      "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"
    ]
  },
  lastNames: [
    "<PERSON>", "<PERSON><PERSON><PERSON>", "Ferrari", "Esposito", "<PERSON>", "Colombo",
    "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON> Luca", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON><PERSON>", "<PERSON><PERSON>", "Font<PERSON>", "<PERSON>ro", "<PERSON>i", "Rinaldi"
  ]
};

// Age-based behavior patterns
export const ageGroupBehavior = {
  young: { // 18-35
    weight: 0.35,
    preferredCategories: ['bar', 'ristorante', 'abbigliamento', 'palestra'],
    bookingFrequency: 'high',
    pricesensitivity: 'high', // More likely to book discounted deals
    bookingAdvanceTime: [1, 7], // Book 1-7 days in advance
    preferredTimes: ['18:00', '19:00', '20:00', '21:00'], // Evening preferences
    weekendPreference: 0.7 // 70% weekend bookings
  },
  adult: { // 36-55  
    weight: 0.45,
    preferredCategories: ['ristorante', 'spa', 'bellezza', 'alimentari'],
    bookingFrequency: 'medium',
    pricesensitivity: 'medium',
    bookingAdvanceTime: [3, 14], // Book 3-14 days in advance
    preferredTimes: ['12:00', '13:00', '19:00', '20:00'], // Lunch and dinner
    weekendPreference: 0.5 // Balanced weekday/weekend
  },
  senior: { // 55+
    weight: 0.2,
    preferredCategories: ['alimentari', 'spa', 'bellezza'],
    bookingFrequency: 'low',
    pricesensitivity: 'low', // Less price sensitive, prefer quality
    bookingAdvanceTime: [7, 30], // Book well in advance
    preferredTimes: ['10:00', '11:00', '14:00', '15:00'], // Avoid peak hours
    weekendPreference: 0.3 // Prefer weekdays
  }
} as const;

// Location-based preferences  
export const locationPreferences = {
  local: {
    weight: 0.7, // 70% prefer local businesses
    maxDistance: 5000, // 5km radius
    loyaltyRate: 0.4 // 40% return to same businesses
  },
  citywide: {
    weight: 0.3, // 30% explore citywide
    maxDistance: 20000, // 20km radius  
    loyaltyRate: 0.2 // 20% return rate
  }
} as const;

// Booking frequency patterns
export const bookingFrequencyPatterns = {
  low: { // Occasional users
    weight: 0.4,
    bookingsPerMonth: [0, 2],
    averageSpend: [15, 40],
    categoryLoyalty: 0.3 // Low category loyalty
  },
  medium: { // Regular users
    weight: 0.45, 
    bookingsPerMonth: [2, 6],
    averageSpend: [25, 60],
    categoryLoyalty: 0.6 // Medium category loyalty
  },
  high: { // Power users
    weight: 0.15,
    bookingsPerMonth: [6, 15], 
    averageSpend: [35, 100],
    categoryLoyalty: 0.8 // High category loyalty
  }
} as const;

// Email provider distribution based on age
export const emailProviders = {
  young: ['gmail.com', 'outlook.com', 'yahoo.it', 'hotmail.it'],
  adult: ['gmail.com', 'libero.it', 'virgilio.it', 'alice.it', 'yahoo.it'],
  senior: ['libero.it', 'virgilio.it', 'tiscali.it', 'alice.it', 'tin.it']
} as const;

// Mobile vs landline phone preferences
export const phonePreferences = {
  young: { mobile: 0.95, landline: 0.05 },
  adult: { mobile: 0.85, landline: 0.15 },
  senior: { mobile: 0.7, landline: 0.3 }
} as const;

// Family/friend network simulation
export const networkPatterns = {
  familyGroups: {
    probability: 0.3, // 30% chance of family booking patterns
    groupSize: [2, 4],
    sharedPreferences: 0.7 // 70% preference overlap
  },
  friendGroups: {
    probability: 0.25, // 25% chance of friend group patterns  
    groupSize: [2, 6],
    sharedPreferences: 0.5 // 50% preference overlap
  }
} as const;

// Seasonal customer behavior
export const seasonalBehavior = {
  winter: { // Dec, Jan, Feb
    categories: {
      spa: 1.3, // 30% increase in spa bookings
      ristorante: 1.2, // 20% increase for comfort food
      bar: 0.9, // 10% decrease
      palestra: 1.4 // 40% increase (New Year resolutions)
    }
  },
  spring: { // Mar, Apr, May  
    categories: {
      bellezza: 1.3, // Prepare for summer
      abbigliamento: 1.2, // Spring collections
      alimentari: 1.1,
      palestra: 1.1
    }
  },
  summer: { // Jun, Jul, Aug
    categories: {
      spa: 1.2, // Pre-vacation treatments
      bellezza: 1.4, // Summer beauty prep
      bar: 1.3, // More social activities
      ristorante: 0.8 // Less heavy meals
    }
  },
  autumn: { // Sep, Oct, Nov
    categories: {
      ristorante: 1.1, // Comfort food returns
      abbigliamento: 1.3, // Fall/winter shopping
      alimentari: 1.2, // Seasonal products
      palestra: 0.9 // Post-summer decline
    }
  }
} as const;