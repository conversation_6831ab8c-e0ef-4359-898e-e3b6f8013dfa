-- Enable pgvector extension for semantic search
CREATE EXTENSION IF NOT EXISTS vector;

-- Create table for deal embeddings
CREATE TABLE public.deal_embeddings (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  deal_id uuid NOT NULL REFERENCES deals(id) ON DELETE CASCADE,
  content text NOT NULL,
  embedding vector(1536) NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now())
);

-- Create index for similarity search
CREATE INDEX deal_embeddings_embedding_idx ON public.deal_embeddings 
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Create table for multi-service plans
CREATE TABLE public.multi_service_plans (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid NOT NULL,
  request_text text NOT NULL,
  services jsonb NOT NULL,
  total_budget numeric,
  optimized_plan jsonb,
  status text NOT NULL DEFAULT 'pending',
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now())
);

-- Create table for service bookings coordination
CREATE TABLE public.coordinated_bookings (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  plan_id uuid NOT NULL REFERENCES multi_service_plans(id) ON DELETE CASCADE,
  deal_id uuid NOT NULL REFERENCES deals(id),
  booking_id uuid REFERENCES bookings(id),
  service_order integer NOT NULL,
  scheduled_time timestamp with time zone,
  travel_time_minutes integer,
  status text NOT NULL DEFAULT 'planned',
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now())
);

-- Enable RLS
ALTER TABLE public.deal_embeddings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.multi_service_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.coordinated_bookings ENABLE ROW LEVEL SECURITY;

-- RLS policies for deal_embeddings
CREATE POLICY "Deal embeddings are viewable by everyone"
  ON public.deal_embeddings
  FOR SELECT
  USING (true);

-- RLS policies for multi_service_plans
CREATE POLICY "Users can view their own plans"
  ON public.multi_service_plans
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own plans"
  ON public.multi_service_plans
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own plans"
  ON public.multi_service_plans
  FOR UPDATE
  USING (auth.uid() = user_id);

-- RLS policies for coordinated_bookings
CREATE POLICY "Users can view coordinated bookings for their plans"
  ON public.coordinated_bookings
  FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM multi_service_plans msp 
    WHERE msp.id = coordinated_bookings.plan_id 
    AND msp.user_id = auth.uid()
  ));

CREATE POLICY "Users can create coordinated bookings for their plans"
  ON public.coordinated_bookings
  FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM multi_service_plans msp 
    WHERE msp.id = coordinated_bookings.plan_id 
    AND msp.user_id = auth.uid()
  ));

-- Function to update deal embeddings when deals change
CREATE OR REPLACE FUNCTION public.update_deal_embedding()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  -- This will be triggered by the edge function
  RETURN NEW;
END;
$$;

-- Trigger to update embeddings when deals are modified
CREATE TRIGGER update_deal_embedding_trigger
  AFTER INSERT OR UPDATE ON public.deals
  FOR EACH ROW
  EXECUTE FUNCTION public.update_deal_embedding();