import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/integrations/supabase/types';

type PricingTier = Database['public']['Tables']['pricing_tiers']['Row'];
type Business = Database['public']['Tables']['businesses']['Row'];

export interface BusinessLimits {
  currentBusinessCount: number;
  maxBusinesses: number;
  canCreateMore: boolean;
  highestTier: string;
  isUnlimited: boolean;
}

/**
 * Hook per ottenere i limiti di creazione business per l'utente corrente
 */
export const useBusinessLimits = () => {
  return useQuery({
    queryKey: ['businessLimits'],
    queryFn: async (): Promise<BusinessLimits> => {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get user's businesses
      const { data: businesses, error: businessesError } = await supabase
        .from('businesses')
        .select('id')
        .eq('owner_id', user.id);

      if (businessesError) throw businessesError;

      const currentBusinessCount = businesses?.length || 0;

      // Get user's highest tier by checking all business subscriptions
      const { data: subscriptions, error: subscriptionsError } = await supabase
        .from('business_subscriptions')
        .select('tier_type')
        .eq('user_id', user.id)
        .eq('status', 'active');

      if (subscriptionsError) throw subscriptionsError;

      // Determine highest tier (enterprise > professional > basic)
      let highestTier: 'basic' | 'professional' | 'enterprise' = 'basic';
      if (subscriptions && subscriptions.length > 0) {
        const tiers = subscriptions.map(sub => sub.tier_type);
        if (tiers.includes('enterprise')) {
          highestTier = 'enterprise';
        } else if (tiers.includes('professional')) {
          highestTier = 'professional';
        }
      }

      // Get tier limits
      const { data: tierData, error: tierError } = await supabase
        .from('pricing_tiers')
        .select('max_businesses')
        .eq('tier_type', highestTier)
        .single();

      if (tierError) throw tierError;

      const maxBusinesses = tierData?.max_businesses || 1;
      const isUnlimited = maxBusinesses === -1;
      const canCreateMore = isUnlimited || currentBusinessCount < maxBusinesses;

      return {
        currentBusinessCount,
        maxBusinesses,
        canCreateMore,
        highestTier,
        isUnlimited
      };
    },
    staleTime: 2 * 60 * 1000, // 2 minuti
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook per validare se l'utente può creare un nuovo business
 */
export const useCanCreateBusiness = () => {
  const { data: limits, isLoading, error } = useBusinessLimits();
  
  return {
    canCreate: limits?.canCreateMore ?? false,
    reason: !limits?.canCreateMore ? 
      `Hai raggiunto il limite di ${limits?.maxBusinesses} business per il piano ${limits?.highestTier}. Aggiorna il piano per creare più business.` : 
      null,
    currentCount: limits?.currentBusinessCount ?? 0,
    maxCount: limits?.maxBusinesses ?? 1,
    isUnlimited: limits?.isUnlimited ?? false,
    isLoading,
    error
  };
}; 