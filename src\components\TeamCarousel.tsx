import React from "react";
import { motion } from "framer-motion";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

interface TeamMember {
  name: string;
  role: string;
  description: string;
  avatar: string;
  videoPlaceholder: string;
  videoId?: string;
}

interface TeamCarouselProps {
  members: TeamMember[];
}

const TeamCarousel: React.FC<TeamCarouselProps> = ({ members }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
      className="w-full mx-auto max-w-5xl"
    >
      <Carousel className="w-full">
        <CarouselContent>
          {members.map((member, index) => {
            // Extract the first name from the format "Name - Role"
            const firstName = member.name.split(" - ")[0];
            const jobTitle = member.name.split(" - ")[1] || member.role;
            
            return (
              <CarouselItem key={index} className="md:basis-1/2 lg:basis-1/3">
                <div className="p-2">
                  <div className="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden h-full flex flex-col">
                    {/* Video/Image section */}
                    <div className="relative aspect-video w-full overflow-hidden bg-gray-100">
                      <img
                        src={member.videoPlaceholder}
                        alt={`${firstName} Video Placeholder`}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                        <div className="relative">
                          <div className="absolute inset-0 bg-primary-600/50 rounded-full animate-ping opacity-70"></div>
                          <div className="relative bg-primary-600 rounded-full p-4 cursor-pointer hover:bg-primary-700 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-8 w-8 text-white">
                              <polygon points="5 3 19 12 5 21 5 3"></polygon>
                            </svg>
                          </div>
                        </div>
                      </div>
                      <div className="absolute bottom-2 right-2 bg-amber-500 text-white text-xs px-2 py-1 rounded-full">
                        Video in arrivo
                      </div>
                    </div>

                    {/* Content section */}
                    <div className="p-5 flex-grow flex flex-col">
                      <div className="flex items-center mb-3">
                        <img
                          src={member.avatar}
                          alt={firstName}
                          className="w-12 h-12 rounded-full object-cover border-2 border-primary-100 mr-3"
                        />
                        <div>
                          <h3 className="text-xl font-bold text-gray-900">Ciao, sono {firstName}!</h3>
                          <p className="text-sm text-gray-600">{jobTitle}</p>
                        </div>
                      </div>
                      <p className="text-gray-700 mb-4 flex-grow">{member.description}</p>
                      <div className="mt-auto">
                        <div className="bg-primary-50 text-primary-700 px-3 py-1 rounded-full text-sm font-medium inline-block">
                          {jobTitle.split(" ")[0]}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CarouselItem>
            );
          })}
        </CarouselContent>
        <div className="flex justify-center mt-4">
          <CarouselPrevious className="static transform-none mx-2" />
          <CarouselNext className="static transform-none mx-2" />
        </div>
      </Carousel>
    </motion.div>
  );
};

export default TeamCarousel;
