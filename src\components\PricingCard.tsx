import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>ircle2, <PERSON><PERSON>, Users, Star, Crown, Check } from "lucide-react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { formatPrice, getAgentTypeLabel } from "@/queries/usePricingTiers";

interface AgentDefinition {
  id: string;
  agent_type: string;
}

interface PricingTier {
  id: string;
  name: string;
  tier_type: 'basic' | 'professional' | 'enterprise';
  price_monthly: number;
  price_yearly: number;
  max_agents: number;
  max_businesses: number;
  features: string[] | any;
  description?: string;
  agentDefinitions?: AgentDefinition[];
  current?: boolean;
}

interface BusinessLimits {
  currentBusinessCount: number;
  highestTier: string;
}

interface PricingCardProps {
  tier: PricingTier;
  isYearly: boolean;
  isPopular?: boolean;
  showCurrentBadge?: boolean;
  businessLimits?: BusinessLimits;
  onSelect?: () => void;
  buttonText?: string;
  showAgentDetails?: boolean;
  showBusinessLimits?: boolean;
  delay?: number;
  className?: string;
}

const getPlanIcon = (planType: 'basic' | 'professional' | 'enterprise') => {
  switch (planType) {
    case "basic":
      return <Users className="w-6 h-6" />;
    case "professional":
      return <Star className="w-6 h-6" />;
    case "enterprise":
      return <Crown className="w-6 h-6" />;
    default:
      return <Users className="w-6 h-6" />;
  }
};

const getYearlySavings = (tier: PricingTier) => {
  const monthlyTotal = tier.price_monthly * 12;
  const yearlyPrice = tier.price_yearly;
  const savings = ((monthlyTotal - yearlyPrice) / monthlyTotal) * 100;
  return Math.round(savings);
};

export const PricingCard = ({
  tier,
  isYearly,
  isPopular = false,
  showCurrentBadge = false,
  businessLimits,
  onSelect,
  buttonText,
  showAgentDetails = false,
  showBusinessLimits = false,
  delay = 0,
  className = ""
}: PricingCardProps) => {
  const currentPrice = isYearly ? tier.price_yearly : tier.price_monthly;
  const features = Array.isArray(tier.features) ? tier.features as string[] : [];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      className={className}
    >
      <Card
        className={`relative flex flex-col h-full ${
          isPopular ? "ring-2 ring-primary border-primary-300" : ""
        } ${tier.current ? "ring-2 ring-primary" : ""}`}
      >
        {/* Badges */}
        {tier.current && showCurrentBadge && (
          <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-primary">
            Piano Attuale
          </Badge>
        )}
        {isPopular && !tier.current && (
          <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <Badge className="bg-blue-500 text-white px-4 py-1">
              Più Popolare
            </Badge>
          </div>
        )}

        <CardHeader>
          {/* Plan Icon */}
          <div className="flex justify-center mb-4">
            <div
              className={`p-3 rounded-full ${
                tier.tier_type === "basic"
                  ? "bg-green-100 text-green-600"
                  : tier.tier_type === "professional"
                  ? "bg-blue-100 text-blue-600"
                  : "bg-purple-100 text-purple-600"
              }`}
            >
              {getPlanIcon(tier.tier_type)}
            </div>
          </div>

          <CardTitle className="text-center">{tier.name}</CardTitle>
          
          {tier.description && (
            <CardDescription className="text-center">
              {tier.description}
            </CardDescription>
          )}

          {/* Pricing */}
          <div className="text-center">
            <div className="flex items-baseline justify-center gap-1">
              <span className="text-3xl font-bold">
                {formatPrice(currentPrice)}
              </span>
              <span className="text-gray-500">
                /{isYearly ? "anno" : "mese"}
              </span>
            </div>

            {isYearly && (
              <div className="flex items-center justify-center gap-2 mt-1">
                <span className="text-sm text-gray-500 line-through">
                  {formatPrice(tier.price_monthly * 12)}/anno
                </span>
                <Badge variant="outline" className="text-xs">
                  Risparmia {getYearlySavings(tier)}%
                </Badge>
              </div>
            )}

            {!isYearly && tier.price_yearly && (
              <p className="text-sm text-green-600 mt-1">
                Risparmia {getYearlySavings(tier)}% con il piano annuale
              </p>
            )}
          </div>
        </CardHeader>

        <CardContent className="flex flex-col flex-1 space-y-4">
          {/* Agent Details */}
          {showAgentDetails && (
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Bot className="h-4 w-4" />
                Agenti AI Inclusi:
              </h4>
              <div className="flex flex-wrap gap-1">
                {tier.agentDefinitions && tier.agentDefinitions.length > 0 ? (
                  tier.agentDefinitions.map((agent) => (
                    <Badge
                      key={agent.id}
                      variant="secondary"
                      className="text-xs"
                    >
                      {getAgentTypeLabel(agent.agent_type)}
                    </Badge>
                  ))
                ) : (
                  <Badge variant="outline" className="text-xs">
                    Nessun agente configurato
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Business Limits */}
          {showBusinessLimits && (
            <div className="space-y-2">
              <h4 className="font-medium">Limiti Business:</h4>
              <div className="bg-gray-50 p-3 rounded-md">
                <p className="text-sm text-gray-700">
                  <strong>Massimo Business:</strong>{" "}
                  {tier.max_businesses === -1 ? "Illimitati" : tier.max_businesses}
                </p>
                {businessLimits && tier.tier_type === businessLimits.highestTier && (
                  <p className="text-xs text-blue-600 mt-1">
                    Attualmente: {businessLimits.currentBusinessCount} utilizzati
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Agent Count (Simple version) */}
          {!showAgentDetails && (
            <div className="mb-4">
              <p className="text-sm text-gray-500">
                <strong>Massimo {tier.max_agents} agenti AI</strong>
              </p>
            </div>
          )}

          {/* Features */}
          <div className="space-y-2 flex-1">
            <h4 className="font-medium">Caratteristiche:</h4>
            <ul className="space-y-1">
              {features.length > 0 ? (
                features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2 text-sm">
                    {showAgentDetails ? (
                      <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    ) : (
                      <CheckCircle2 className="h-4 w-4 text-primary-600 mt-0.5 flex-shrink-0" />
                    )}
                    <span>{feature}</span>
                  </li>
                ))
              ) : (
                <li className="flex items-start gap-2 text-sm">
                  <CheckCircle2 className="h-4 w-4 text-primary-600 mt-0.5 flex-shrink-0" />
                  <span>Caratteristiche non disponibili</span>
                </li>
              )}
            </ul>
          </div>

          {/* Action Button */}
          <Button
            className={`w-full mt-auto ${
              isPopular 
                ? "bg-primary-600 hover:bg-primary-700 text-white" 
                : tier.current 
                ? "" 
                : "border-primary-300 text-primary-700 hover:bg-primary-50"
            }`}
            variant={tier.current ? "outline" : isPopular ? "default" : "outline"}
            disabled={tier.current}
            onClick={onSelect}
            asChild={!onSelect}
          >
            {onSelect ? (
              <span>
                {buttonText || 
                 (tier.current ? "Piano Attuale" : 
                  tier.tier_type === 'enterprise' ? 'Contattaci' : 
                  'Prova Gratis per 90 Giorni')}
              </span>
            ) : (
              <Link to="/auth">
                {buttonText || 
                 (tier.current ? "Piano Attuale" : 
                  tier.tier_type === 'enterprise' ? 'Contattaci' : 
                  'Prova Gratis per 90 Giorni')}
              </Link>
            )}
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
};