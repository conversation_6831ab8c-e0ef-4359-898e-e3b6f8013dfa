-- Create enum for pricing tiers
CREATE TYPE pricing_tier_type AS ENUM ('basic', 'professional', 'enterprise');

-- Create enum for agent types
CREATE TYPE agent_type AS ENUM ('booking', 'customer_support', 'sales', 'marketing', 'data_analysis');

-- Create pricing tiers table
CREATE TABLE public.pricing_tiers (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  tier_type pricing_tier_type NOT NULL UNIQUE,
  price_monthly DECIMAL(10,2) NOT NULL,
  price_yearly DECIMAL(10,2) NOT NULL,
  max_agents INTEGER NOT NULL,
  allowed_agents agent_type[] NOT NULL,
  features JSONB NOT NULL DEFAULT '[]'::jsonb,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create business subscriptions table
CREATE TABLE public.business_subscriptions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  business_id UUID NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
  tier_type pricing_tier_type NOT NULL,
  is_yearly BOOLEAN NOT NULL DEFAULT false,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'suspended')),
  started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create business AI agents table (extends ai_assistant_profile for business-specific configs)
CREATE TABLE public.business_ai_agents (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  business_id UUID NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
  agent_type agent_type NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  voice_id TEXT,
  voice_settings JSONB DEFAULT '{}'::jsonb,
  avatar_url TEXT,
  personality_style TEXT,
  instructions TEXT,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(business_id, agent_type)
);

-- Enable RLS
ALTER TABLE public.pricing_tiers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_ai_agents ENABLE ROW LEVEL SECURITY;

-- RLS Policies for pricing_tiers
CREATE POLICY "Pricing tiers are viewable by everyone" 
ON public.pricing_tiers 
FOR SELECT 
USING (true);

-- RLS Policies for business_subscriptions
CREATE POLICY "Business owners can view their subscriptions" 
ON public.business_subscriptions 
FOR SELECT 
USING (business_id IN (
  SELECT id FROM businesses WHERE owner_id = auth.uid()
));

CREATE POLICY "Business owners can update their subscriptions" 
ON public.business_subscriptions 
FOR UPDATE 
USING (business_id IN (
  SELECT id FROM businesses WHERE owner_id = auth.uid()
));

CREATE POLICY "Business owners can create subscriptions" 
ON public.business_subscriptions 
FOR INSERT 
WITH CHECK (business_id IN (
  SELECT id FROM businesses WHERE owner_id = auth.uid()
));

-- RLS Policies for business_ai_agents
CREATE POLICY "Business owners can manage their AI agents" 
ON public.business_ai_agents 
FOR ALL 
USING (business_id IN (
  SELECT id FROM businesses WHERE owner_id = auth.uid()
));

-- Insert default pricing tiers
INSERT INTO public.pricing_tiers (name, tier_type, price_monthly, price_yearly, max_agents, allowed_agents, features) VALUES
('Basic', 'basic', 29.99, 299.99, 2, ARRAY['booking', 'customer_support']::agent_type[], '[
  "2 AI Voice Agents",
  "Basic voice customization", 
  "Standard support",
  "Basic analytics"
]'::jsonb),
('Professional', 'professional', 79.99, 799.99, 4, ARRAY['booking', 'customer_support', 'sales', 'marketing']::agent_type[], '[
  "4 AI Voice Agents",
  "Advanced voice customization",
  "Priority support", 
  "Advanced analytics",
  "Custom branding"
]'::jsonb),
('Enterprise', 'enterprise', 199.99, 1999.99, 5, ARRAY['booking', 'customer_support', 'sales', 'marketing', 'data_analysis']::agent_type[], '[
  "5 AI Voice Agents",
  "Full voice customization",
  "24/7 dedicated support",
  "Advanced analytics & insights",
  "Custom branding",
  "API access",
  "Custom integrations"
]'::jsonb);

-- Create triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_pricing_tiers_updated_at
  BEFORE UPDATE ON public.pricing_tiers
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_business_subscriptions_updated_at
  BEFORE UPDATE ON public.business_subscriptions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_business_ai_agents_updated_at
  BEFORE UPDATE ON public.business_ai_agents
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();