-- Create email templates table
CREATE TABLE public.email_templates (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  template_type TEXT NOT NULL CHECK (template_type IN ('welcome', 'updates', 'custom')),
  subject_template TEXT NOT NULL,
  html_template TEXT NOT NULL,
  variables JSON DEFAULT '[]'::json,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.email_templates ENABLE ROW LEVEL SECURITY;

-- Create policies for admin access only
CREATE POLICY "<PERSON><PERSON> can manage email templates" 
ON public.email_templates 
FOR ALL 
USING (
  EXISTS (
    SELECT 1 FROM auth.users 
    WHERE auth.users.id = auth.uid() 
    AND auth.users.raw_user_meta_data->>'role' = 'admin'
  )
);

-- Add template_id to campaigns table
ALTER TABLE public.campaigns 
ADD COLUMN template_id UUID REFERENCES public.email_templates(id);

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_email_templates_updated_at
BEFORE UPDATE ON public.email_templates
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Insert default templates
INSERT INTO public.email_templates (name, description, template_type, subject_template, html_template, variables) VALUES
(
  'Benvenuto nella Waitlist',
  'Email di benvenuto inviata dopo l''iscrizione alla waitlist',
  'welcome',
  'Benvenuto {{nome}}! Sei nella nostra waitlist',
  '<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Benvenuto</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
  <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;">
    <h1 style="margin: 0; font-size: 28px;">Benvenuto {{nome}}!</h1>
    <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Grazie per esserti unito alla nostra waitlist</p>
  </div>
  
  <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin-bottom: 25px;">
    <h2 style="color: #333; margin-top: 0;">Cosa succede adesso?</h2>
    <ul style="padding-left: 20px;">
      <li style="margin-bottom: 10px;">Ti terremo aggiornato sui nostri progressi</li>
      <li style="margin-bottom: 10px;">Sarai tra i primi a provare le nuove funzionalità</li>
      <li style="margin-bottom: 10px;">Riceverai offerte esclusive per i membri della waitlist</li>
    </ul>
  </div>
  
  <div style="text-align: center; margin: 30px 0;">
    <a href="{{website_url}}" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">Visita il nostro sito</a>
  </div>
  
  <div style="text-align: center; color: #666; font-size: 14px; border-top: 1px solid #eee; padding-top: 20px;">
    <p>Grazie per il tuo interesse!</p>
    <p>Il team di {{company_name}}</p>
  </div>
</body>
</html>',
  '[{"name": "nome", "description": "Nome dell''utente"}, {"name": "company_name", "description": "Nome dell''azienda"}, {"name": "website_url", "description": "URL del sito web"}]'::json
),
(
  'Aggiornamenti Prodotto',
  'Email per inviare aggiornamenti e notifiche di lancio',
  'updates',
  'Novità da {{company_name}}: {{update_title}}',
  '<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Aggiornamenti</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
  <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;">
    <h1 style="margin: 0; font-size: 28px;">{{update_title}}</h1>
    <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">{{update_date}}</p>
  </div>
  
  <div style="padding: 0 10px;">
    <p style="font-size: 16px; margin-bottom: 20px;">Ciao {{nome}},</p>
    
    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin-bottom: 25px;">
      {{update_content}}
    </div>
    
    <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; border-left: 4px solid #2196f3; margin: 25px 0;">
      <h3 style="color: #1976d2; margin-top: 0;">Cosa c''è di nuovo:</h3>
      {{new_features}}
    </div>
  </div>
  
  <div style="text-align: center; margin: 30px 0;">
    <a href="{{cta_url}}" style="background: #4facfe; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">{{cta_text}}</a>
  </div>
  
  <div style="text-align: center; color: #666; font-size: 14px; border-top: 1px solid #eee; padding-top: 20px;">
    <p>Continua a seguirci per altri aggiornamenti!</p>
    <p>Il team di {{company_name}}</p>
    <p style="font-size: 12px; margin-top: 15px;">
      <a href="{{unsubscribe_url}}" style="color: #999;">Disiscriviti</a>
    </p>
  </div>
</body>
</html>',
  '[{"name": "nome", "description": "Nome dell''utente"}, {"name": "company_name", "description": "Nome dell''azienda"}, {"name": "update_title", "description": "Titolo dell''aggiornamento"}, {"name": "update_date", "description": "Data dell''aggiornamento"}, {"name": "update_content", "description": "Contenuto principale dell''aggiornamento"}, {"name": "new_features", "description": "Lista delle nuove funzionalità"}, {"name": "cta_text", "description": "Testo del bottone call-to-action"}, {"name": "cta_url", "description": "URL del bottone call-to-action"}, {"name": "unsubscribe_url", "description": "URL per la disiscrizione"}]'::json
);