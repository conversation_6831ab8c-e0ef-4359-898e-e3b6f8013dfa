import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface UserData {
  id: string;
  email: string | null;
  role: string;
  first_name?: string | null;
  last_name?: string | null;
  avatar_url?: string | null;
  display_name?: string | null;
  phone_number?: string | null;
}

interface UserStore {
  userData: UserData | null;
  isLoading: boolean;
  setUserData: (userData: UserData | null) => void;
  setLoading: (loading: boolean) => void;
  clearUserData: () => void;
}

export const useUserStore = create<UserStore>()(
  persist(
    (set) => ({
      userData: null,
      isLoading: false,
      
      setUserData: (userData) => {
        set({ userData, isLoading: false });
      },
      
      setLoading: (loading) => {
        set({ isLoading: loading });
      },
      
      clearUserData: () => {
        set({ userData: null, isLoading: false });
      },
    }),
    {
      name: 'user-store',
      partialize: (state) => ({
        userData: state.userData,
      }),
    }
  )
);