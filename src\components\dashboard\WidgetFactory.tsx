
import React from 'react';
import { Widget, WidgetType } from '@/types/widget';
import BookingsWidget from './BookingsWidget';
import DealsWidget from './DealsWidget';
import ClientsWidget from './ClientsWidget';
import TimeSlotUsageWidget from './TimeSlotUsageWidget';
import TotalRevenueWidget from './TotalRevenueWidget';
import EstimatedProfitWidget from './EstimatedProfitWidget';
import AvgBookingPriceWidget from './AvgBookingPriceWidget';
import RevenuePerClientWidget from './RevenuePerClientWidget';
import NewCustomersWidget from './NewCustomersWidget';
import RecentBookingsWidget from './RecentBookingsWidget';
import { useWidgetStore } from '@/store/widgetStore';
import { toast } from 'sonner';

interface WidgetFactoryProps {
  widget: Widget;
  className?: string;
  onRemove?: (widgetId: string) => void;
}

/**
 * Factory component che renderizza il widget appropriato in base al tipo
 */
const WidgetFactory: React.FC<WidgetFactoryProps> = React.memo(({ widget, className, onRemove }) => {
  // Ottieni la funzione removeWidget dallo store
  const { removeWidget } = useWidgetStore();

  // Funzione per gestire la rimozione del widget
  const handleRemove = () => {
    // Rimuovi sempre il widget usando la funzione dello store
    removeWidget(widget.id);
    toast.success(`Widget "${widget.title}" rimosso con successo`);

    // Notifica anche il componente parent se necessario
    if (typeof onRemove === 'function') {
      onRemove(widget.id);
    }
  };
  // Renderizza il widget appropriato in base al tipo
  switch (widget.type) {
    case WidgetType.BOOKINGS:
      return <BookingsWidget widget={widget} className={className} onRemove={handleRemove} />;

    case WidgetType.DEALS:
      return <DealsWidget widget={widget} className={className} onRemove={handleRemove} />;

    case WidgetType.CLIENTS:
      return <ClientsWidget widget={widget} className={className} onRemove={handleRemove} />;

    case WidgetType.TIME_SLOT_USAGE:
      return <TimeSlotUsageWidget widget={widget} className={className} onRemove={handleRemove} />;

    case WidgetType.TOTAL_REVENUE:
      return <TotalRevenueWidget widget={widget} className={className} onRemove={handleRemove} />;

    case WidgetType.ESTIMATED_PROFIT:
      return <EstimatedProfitWidget widget={widget} className={className} onRemove={handleRemove} />;

    case WidgetType.AVG_BOOKING_PRICE:
      return <AvgBookingPriceWidget widget={widget} className={className} onRemove={handleRemove} />;

    case WidgetType.REVENUE_PER_CLIENT:
      return <RevenuePerClientWidget widget={widget} className={className} onRemove={handleRemove} />;
      
    case WidgetType.NEW_CUSTOMERS:
      return <NewCustomersWidget widget={widget} className={className} onRemove={handleRemove} />;
      
    case WidgetType.RECENT_BOOKINGS:
      return <RecentBookingsWidget widget={widget} className={className} onRemove={handleRemove} />;

    // Aggiungi altri casi per i tipi di widget futuri

    default:
      // Widget non supportato
      return (
        <div className="bg-red-100 p-4 rounded-md">
          Widget di tipo {widget.type} non supportato
        </div>
      );
  }
});


export default WidgetFactory;
