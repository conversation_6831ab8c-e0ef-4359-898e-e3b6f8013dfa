import { useState, useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import MainLayout from "@/layouts/MainLayout";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Building2, MapPin, Mail, Phone, Globe, Calendar, Search, Filter, ChevronLeft, ChevronRight, Edit, List, Map } from "lucide-react";
import { Database } from "@/integrations/supabase/types";

type BusinessWithCounts = Database['public']['Views']['businesses_with_counts']['Row'];

const AdminBusinesses = () => {
  const navigate = useNavigate();
  const [businesses, setBusinesses] = useState<BusinessWithCounts[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [cityFilter, setCityFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [categories, setCategories] = useState<{id: string, name: string}[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [viewMode, setViewMode] = useState<'list' | 'map'>('list');

  useEffect(() => {
    fetchBusinesses();
    fetchCategories();
  }, []);

  // Get unique cities for filter
  const uniqueCities = useMemo(() => {
    const cities = businesses
      .map(b => b.city)
      .filter(Boolean)
      .filter((city, index, array) => array.indexOf(city) === index)
      .sort();
    return cities;
  }, [businesses]);

  // Filter and search businesses
  const filteredBusinesses = useMemo(() => {
    return businesses.filter(business => {
      const matchesSearch = !searchTerm || 
        business.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        business.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        business.description?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesCity = !cityFilter || cityFilter === "all" || business.city === cityFilter;
      const matchesCategory = !categoryFilter || categoryFilter === "all" || business.category_id === categoryFilter;
      
      return matchesSearch && matchesCity && matchesCategory;
    });
  }, [businesses, searchTerm, cityFilter, categoryFilter]);

  // Pagination
  const totalPages = Math.ceil(filteredBusinesses.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedBusinesses = filteredBusinesses.slice(startIndex, startIndex + itemsPerPage);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, cityFilter, categoryFilter]);

  const fetchBusinesses = async () => {
    try {
      const { data, error } = await supabase
        .from('businesses_with_counts')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setBusinesses(data || []);
    } catch (error) {
      console.error('Error fetching businesses:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('id, name')
        .order('name');

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const getCategoryName = (categoryId: string | null) => {
    if (!categoryId) return null;
    const category = categories.find(cat => cat.id === categoryId);
    return category?.name || null;
  };

  const getBusinessInitials = (name: string) => {
    if (!name) return 'B';
    const words = name.split(' ');
    if (words.length >= 2) {
      return `${words[0].charAt(0)}${words[1].charAt(0)}`.toUpperCase();
    }
    return name.charAt(0).toUpperCase();
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('it-IT');
  };

  const handlePreviousPage = () => {
    setCurrentPage(prev => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(prev + 1, totalPages));
  };

  const clearFilters = () => {
    setSearchTerm("");
    setCityFilter("all");
    setCategoryFilter("all");
    setCurrentPage(1);
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="space-y-6 p-6">
          <div className="animate-pulse">
            <div className="h-8 w-64 bg-gray-300 rounded mb-2"></div>
            <div className="h-4 w-96 bg-gray-300 rounded mb-8"></div>
            <div className="h-96 bg-gray-300 rounded"></div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6 p-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            <Building2 className="inline-block h-8 w-8 mr-3" />
            Gestione Attività
          </h1>
          <p className="text-gray-600">
            Visualizza e gestisci tutte le attività registrate sulla piattaforma
          </p>
          <div className="flex items-center gap-4 mt-4">
            <Badge variant="outline" className="text-sm">
              Totale: {businesses.length} attività
            </Badge>
            <Badge variant="outline" className="text-sm">
              Filtrate: {filteredBusinesses.length} attività
            </Badge>
            <Badge variant="outline" className="text-sm">
              Con offerte: {businesses.filter(b => (b.deal_count || 0) > 0).length}
            </Badge>
          </div>
        </div>

        {/* View Mode Toggle */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filtri e Ricerca
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="flex items-center gap-2"
                >
                  <List className="h-4 w-4" />
                  Lista
                </Button>
                <Button
                  variant={viewMode === 'map' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('map')}
                  className="flex items-center gap-2"
                >
                  <Map className="h-4 w-4" />
                  Mappa
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Cerca per nome, email o descrizione..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="w-full sm:w-48">
                <Select value={cityFilter} onValueChange={setCityFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filtra per città" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tutte le città</SelectItem>
                    {uniqueCities.map((city) => (
                      <SelectItem key={city} value={city}>
                        {city}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full sm:w-48">
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filtra per categoria" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tutte le categorie</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Button variant="outline" onClick={clearFilters}>
                Pulisci Filtri
              </Button>
            </div>
          </CardContent>
        </Card>

        {viewMode === 'list' ? (
          <Card>
            <CardHeader>
              <CardTitle>Elenco Attività</CardTitle>
              <CardDescription>
                Tutte le attività registrate con i relativi dettagli e statistiche
              </CardDescription>
            </CardHeader>
            <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Attività</TableHead>
                    <TableHead>Categoria</TableHead>
                    <TableHead>Proprietario</TableHead>
                    <TableHead>Contatti</TableHead>
                    <TableHead>Posizione</TableHead>
                    <TableHead>Statistiche</TableHead>
                    <TableHead>Data Creazione</TableHead>
                    <TableHead>Azioni</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedBusinesses.map((business) => (
                    <TableRow key={business.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarFallback className="bg-blue-100 text-blue-600">
                              {getBusinessInitials(business.name || '')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{business.name}</div>
                            {business.description && (
                              <div className="text-sm text-gray-500 max-w-xs truncate">
                                {business.description}
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>

                      <TableCell>
                        {getCategoryName(business.category_id) ? (
                          <Badge variant="outline" className="text-xs">
                            {getCategoryName(business.category_id)}
                          </Badge>
                        ) : (
                          <span className="text-gray-400 text-sm">Nessuna categoria</span>
                        )}
                      </TableCell>

                      <TableCell>
                        <div className="text-sm">
                          <div className="text-gray-600">ID: {business.owner_id?.slice(0, 8)}...</div>
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="space-y-1">
                          {business.email && (
                            <div className="flex items-center gap-2 text-sm">
                              <Mail className="h-3 w-3 text-gray-400" />
                              <span className="truncate max-w-xs">{business.email}</span>
                            </div>
                          )}
                          {business.phone && (
                            <div className="flex items-center gap-2 text-sm">
                              <Phone className="h-3 w-3 text-gray-400" />
                              <span>{business.phone}</span>
                            </div>
                          )}
                          {business.website && (
                            <div className="flex items-center gap-2 text-sm">
                              <Globe className="h-3 w-3 text-gray-400" />
                              <span className="truncate max-w-xs">{business.website}</span>
                            </div>
                          )}
                        </div>
                      </TableCell>

                      <TableCell>
                        {business.city || business.address ? (
                          <div className="flex items-start gap-2 text-sm">
                            <MapPin className="h-3 w-3 text-gray-400 mt-0.5" />
                            <div>
                              {business.city && <div>{business.city}</div>}
                              {business.address && (
                                <div className="text-gray-500 max-w-xs truncate">
                                  {business.address}
                                </div>
                              )}
                            </div>
                          </div>
                        ) : (
                          <span className="text-gray-400">N/A</span>
                        )}
                      </TableCell>

                      <TableCell>
                        <div className="space-y-1">
                          <Badge variant="secondary" className="text-xs">
                            {business.deal_count || 0} offerte
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {business.booking_count || 0} prenotazioni
                          </Badge>
                          {(business.pending_booking_count || 0) > 0 && (
                            <Badge variant="destructive" className="text-xs">
                              {business.pending_booking_count} in attesa
                            </Badge>
                          )}
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Calendar className="h-3 w-3" />
                          {formatDate(business.created_at)}
                        </div>
                      </TableCell>

                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => navigate(`/business/${business.id}/edit`)}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Modifica
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Empty State */}
            {filteredBusinesses.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Building2 className="h-12 w-12 mx-auto mb-4 opacity-20" />
                <p>
                  {searchTerm || cityFilter 
                    ? "Nessuna attività trovata con i filtri applicati" 
                    : "Nessuna attività trovata"
                  }
                </p>
              </div>
            )}

            {/* Pagination */}
            {filteredBusinesses.length > itemsPerPage && (
              <div className="flex items-center justify-between px-2 py-4">
                <div className="text-sm text-gray-500">
                  Mostrando {startIndex + 1}-{Math.min(startIndex + itemsPerPage, filteredBusinesses.length)} di {filteredBusinesses.length} attività
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePreviousPage}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Precedente
                  </Button>
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: totalPages }, (_, i) => i + 1)
                      .filter(page => 
                        page === 1 || 
                        page === totalPages || 
                        Math.abs(page - currentPage) <= 1
                      )
                      .map((page, index, array) => (
                        <div key={page} className="flex items-center">
                          {index > 0 && array[index - 1] !== page - 1 && (
                            <span className="px-2 text-gray-400">...</span>
                          )}
                          <Button
                            variant={currentPage === page ? "default" : "outline"}
                            size="sm"
                            onClick={() => setCurrentPage(page)}
                            className="min-w-8"
                          >
                            {page}
                          </Button>
                        </div>
                      ))
                    }
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleNextPage}
                    disabled={currentPage === totalPages}
                  >
                    Successivo
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Mappa Attività</CardTitle>
              <CardDescription>
                Visualizza le attività sulla mappa geografica
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[600px] rounded-lg bg-muted flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <Map className="h-12 w-12 mx-auto mb-4" />
                  <p className="text-lg font-medium">Vista Mappa</p>
                  <p className="text-sm">
                    La vista mappa sarà implementata qui per visualizzare le {filteredBusinesses.length} attività filtrate
                  </p>
                  <p className="text-xs mt-2">
                    Attività con coordinate: {filteredBusinesses.filter(b => b.latitude && b.longitude).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  );
};

export default AdminBusinesses;