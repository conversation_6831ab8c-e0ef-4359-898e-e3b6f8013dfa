import React from 'react';
import {
  B<PERSON><PERSON><PERSON>b,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { usePageInfo } from '@/hooks/usePageInfo';
import { Store } from 'lucide-react';

/**
 * CustomBreadcrumb component that displays the current business and page
 */
const CustomBreadcrumb: React.FC = () => {
  const { businessName, pageName, pageUrl, subPageName, subPageUrl } = usePageInfo();

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {/* Business name */}
        {businessName && (
          <>
            <BreadcrumbItem className="flex items-center">
              <BreadcrumbLink
                href="/dashboard"
                className="flex items-center text-primary-600 font-medium hover:text-primary-700"
              >
                <Store className="h-4 w-4 mr-1" />
                <span>{businessName}</span>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
          </>
        )}

        {/* Main page */}
        <BreadcrumbItem>
          {subPageName ? (
            <BreadcrumbLink
              href={pageUrl}
              className="text-gray-600 hover:text-gray-900"
            >
              {pageName}
            </BreadcrumbLink>
          ) : (
            <BreadcrumbPage className="font-semibold">{pageName}</BreadcrumbPage>
          )}
        </BreadcrumbItem>

        {/* Sub page (detail page) */}
        {subPageName && (
          <>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="font-semibold">
                {subPageName}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </>
        )}
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default CustomBreadcrumb;
