import React, { useState } from 'react';
import Widget from './Widget';
import { TimeSlotUsageWidget as TimeSlotUsageWidgetType } from '@/types/widget';
import { useBusinessStore } from '@/store/businessStore';
import { useWidgetStore } from '@/store/widgetStore';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Legend } from 'recharts';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { Clock, BarChart3, Clock3, CalendarDays, ArrowUpDown, RefreshCw } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Toggle } from '@/components/ui/toggle';
import { cn } from '@/lib/utils';
import { useTimeSlotUsage, TimeSlotFilters, defaultFilters, TimeSlotData } from '@/queries/useTimeSlotUsage';

interface TimeSlotUsageWidgetProps {
  widget: TimeSlotUsageWidgetType;
  className?: string;
  onRemove?: () => void;
}

/**
 * Widget che mostra le fasce orarie più popolari per le prenotazioni
 */
const TimeSlotUsageWidget: React.FC<TimeSlotUsageWidgetProps> = React.memo(({ widget, className, onRemove }) => {
  const { selectedBusiness } = useBusinessStore();
  const { setWidgetLoading } = useWidgetStore();
  
  // Stato locale per i filtri - inizializzato dai filtri del widget ma non aggiorna il widget
  const [filters, setFilters] = useState<TimeSlotFilters>(
    widget.data.filters || defaultFilters
  );

  // Stato locale per i dati - non aggiorna il widget store
  const [localTimeSlotData, setLocalTimeSlotData] = useState<TimeSlotData[]>(
    widget.data.timeSlotData || []
  );

  // Usa il query hook per ottenere i dati delle fasce orarie
  const { data: timeSlotData, isLoading, error, refetch } = useTimeSlotUsage(
    selectedBusiness?.id,
    filters
  );

  // Aggiorna lo stato locale quando cambiano i dati della query
  React.useEffect(() => {
    if (timeSlotData) {
      setLocalTimeSlotData(timeSlotData);
      
      // Log per debug
      console.log('Dati aggiornati:', timeSlotData);
    }
  }, [timeSlotData]);

  // Aggiorna lo stato di caricamento del widget
  React.useEffect(() => {
    setWidgetLoading(widget.id, isLoading);
  }, [isLoading, widget.id, setWidgetLoading]);

  // Gestisci il cambio di filtri (solo stato locale)
  const handleDateRangeChange = (value: string) => {
    setFilters(prev => ({
      ...prev,
      dateRange: value as 'last7days' | 'last30days' | 'last90days' | 'all'
    }));
    
    // Log per debug
    console.log('Filtro data cambiato:', value);
  };

  const handleSortByChange = (value: 'popularity' | 'time') => {
    setFilters(prev => ({
      ...prev,
      sortBy: value
    }));
    
    // Log per debug
    console.log('Filtro ordinamento cambiato:', value);
  };

  const toggleDayFilter = (day: number) => {
    setFilters(prev => {
      const currentSelectedDays = [...prev.selectedDays];
      const dayIndex = currentSelectedDays.indexOf(day);
      
      if (dayIndex >= 0) {
        // Rimuovi il giorno se già selezionato
        currentSelectedDays.splice(dayIndex, 1);
      } else {
        // Aggiungi il giorno se non selezionato
        currentSelectedDays.push(day);
      }
      
      const newFilters = {
        ...prev,
        selectedDays: currentSelectedDays
      };
      
      // Log per debug
      console.log('Filtro giorni cambiato:', newFilters.selectedDays);
      
      return newFilters;
    });
  };

  // Ottieni i giorni della settimana in italiano
  const getDayName = (dayOfWeek?: number): string => {
    if (dayOfWeek === undefined) return '';
    
    const days = [
      'Domenica', 'Lunedì', 'Martedì', 'Mercoledì', 
      'Giovedì', 'Venerdì', 'Sabato', 'Domenica'
    ];
    
    // Gestisci sia 0-6 che 1-7 come indici per i giorni della settimana
    return days[dayOfWeek % 7];
  };

  return (
    <Widget
      widget={widget}
      className={className}
      actionLabel="Gestisci Fasce Orarie"
      actionLink="/deals"
      onRemove={onRemove}
    >
      <div className="w-full h-full py-2">
        {/* Filtri */}
        <div className="mb-4 space-y-2">
          <div className="flex flex-wrap items-center justify-between gap-2">
            <div className="flex flex-wrap items-center gap-2">
              <div className="flex items-center gap-1.5">
                <CalendarDays className="h-4 w-4 text-muted-foreground" />
                <Select
                  value={filters.dateRange}
                  onValueChange={handleDateRangeChange}
                >
                  <SelectTrigger className="h-8 w-[130px]">
                    <SelectValue placeholder="Periodo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="last7days">Ultimi 7 giorni</SelectItem>
                    <SelectItem value="last30days">Ultimi 30 giorni</SelectItem>
                    <SelectItem value="last90days">Ultimi 90 giorni</SelectItem>
                    <SelectItem value="all">Tutti</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center gap-1.5">
                <ArrowUpDown className="h-4 w-4 text-muted-foreground" />
                <div className="flex items-center rounded-md border">
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "h-8 rounded-r-none border-r",
                      filters.sortBy === 'popularity' ? "bg-muted" : ""
                    )}
                    onClick={() => handleSortByChange('popularity')}
                  >
                    <BarChart3 className="h-4 w-4 mr-1" />
                    Popolarità
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "h-8 rounded-l-none",
                      filters.sortBy === 'time' ? "bg-muted" : ""
                    )}
                    onClick={() => handleSortByChange('time')}
                  >
                    <Clock3 className="h-4 w-4 mr-1" />
                    Orario
                  </Button>
                </div>
              </div>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              className="h-8 px-2"
              onClick={() => {
                console.log('Aggiornamento manuale dei dati');
                refetch();
              }}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span className="sr-only">Aggiorna</span>
            </Button>
          </div>

          <div className="flex flex-wrap gap-1">
            {[0, 1, 2, 3, 4, 5, 6].map((day) => {
              const dayNames = ['Dom', 'Lun', 'Mar', 'Mer', 'Gio', 'Ven', 'Sab'];
              const isSelected = filters.selectedDays.includes(day);
              
              return (
                <Toggle
                  key={day}
                  pressed={isSelected}
                  onPressedChange={() => toggleDayFilter(day)}
                  className="h-7 px-2 text-xs"
                  variant="outline"
                >
                  {dayNames[day]}
                </Toggle>
              );
            })}
          </div>
        </div>
        
        {localTimeSlotData && localTimeSlotData.length > 0 ? (
          <div className="w-full h-[160px]">
            <ChartContainer
              config={{
                timeSlot: { label: "Fascia Oraria" },
                count: { label: "Prenotazioni", color: "#4f46e5" }
              }}
            >
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={localTimeSlotData}
                  margin={{ top: 10, right: 10, left: 5, bottom: 30 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis 
                    dataKey="timeSlot" 
                    angle={-45} 
                    textAnchor="end" 
                    height={60}
                    tick={{ fontSize: 12 }}
                    tickFormatter={(value) => value.replace('-', ' - ')}
                  />
                  <YAxis 
                    label={{ value: 'Prenotazioni', angle: -90, position: 'insideLeft', style: { textAnchor: 'middle' } }}
                  />
                  <ChartTooltip
                    content={
                      <ChartTooltipContent
                        formatter={(value, _, entry) => {
                          const item = entry.payload;
                          return (
                            <div>
                              <div className="font-semibold">{item.timeSlot.replace('-', ' - ')}</div>
                              <div>{getDayName(item.dayOfWeek)}</div>
                              <div>{value} prenotazioni</div>
                            </div>
                          );
                        }}
                      />
                    }
                  />
                  <Legend />
                  <defs>
                    <linearGradient id="colorCount" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#4f46e5" stopOpacity={0.9}/>
                      <stop offset="95%" stopColor="#818cf8" stopOpacity={0.7}/>
                    </linearGradient>
                  </defs>
                  <Bar 
                    dataKey="count" 
                    name="Prenotazioni" 
                    fill="url(#colorCount)" 
                    radius={[4, 4, 0, 0]}
                    fillOpacity={0.8}
                  />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-[200px] text-gray-500">
            <Clock className="h-12 w-12 text-gray-300 mb-2" />
            <p className="text-sm text-center">
              {isLoading ? 'Caricamento dati...' : error ? 'Errore nel caricamento dei dati' : 'Nessun dato disponibile sulle fasce orarie'}
            </p>
          </div>
        )}
      </div>
    </Widget>
  );
});

export default TimeSlotUsageWidget;
