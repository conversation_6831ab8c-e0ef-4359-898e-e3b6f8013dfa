
import { supabase } from '@/integrations/supabase/client';

/**
 * <PERSON><PERSON><PERSON> le metriche di debug dirette per il business
 * @param businessId ID del business
 * @returns Promise con i dati di debug
 */
export const debugDashboardMetrics = async (businessId: string) => {
  try {
    // Ottieni le informazioni sulle offerte
    const { data: deals, error: dealsError } = await supabase
      .from('deals')
      .select('id, start_date, end_date')
      .eq('business_id', businessId);
    
    if (dealsError) throw dealsError;
    
    // Ottieni le prenotazioni per ogni offerta
    const dealIds = deals?.map(deal => deal.id) || [];
    
    let bookings = [];
    
    if (dealIds.length > 0) {
      const { data: bookingsData, error: bookingsError } = await supabase
        .from('bookings')
        .select('*')
        .in('deal_id', dealIds);
      
      if (bookingsError) throw bookingsError;
      
      bookings = bookingsData || [];
    }
    
    // Calcola le metriche
    const now = new Date();
    const activeDealsCount = deals?.filter(deal => new Date(deal.end_date) >= now).length || 0;
    const expiredDealsCount = deals?.filter(deal => new Date(deal.end_date) < now).length || 0;
    
    // Calcola il numero di clienti unici
    const uniqueClientIds = new Set();
    bookings.forEach((booking: any) => {
      if (booking.user_id) {
        uniqueClientIds.add(booking.user_id);
      }
    });
    
    return {
      dealsCount: deals?.length || 0,
      activeDealsCount,
      expiredDealsCount,
      bookingsCount: bookings.length,
      uniqueClientsCount: uniqueClientIds.size
    };
  } catch (error) {
    console.error('Errore nel recupero delle metriche di debug:', error);
    throw error;
  }
};
