# Deal Sub-Categorization Feature

## Overview

The Deal Sub-Categorization feature extends the existing category system by allowing deals to be tagged with multiple sub-categories. This provides more granular classification while maintaining backward compatibility with the existing macro-category system.

## Business Rules

1. **Inheritance Rule**: A deal can only be linked to deal-categories whose `category_id` matches the `business.category_id` that owns the deal.
2. **Flexibility**: Deals may have zero or many deal-categories (backward compatibility).
3. **Uniqueness**: Sub-category names must be unique within each macro-category.

## Database Schema

### New Tables

#### `deal_categories`
Catalog of sub-categories that belong to a macro-category.

| Column | Type | Description |
|--------|------|-------------|
| `id` | UUID (PK) | Primary key |
| `category_id` | UUID (FK) | References `categories(id)` |
| `name` | TEXT | Sub-category name |
| `created_at` | TIMESTAMPTZ | Creation timestamp |

**Constraints:**
- Unique constraint on `(category_id, name)`
- Foreign key to `categories(id)` with CASCADE delete

#### `deals_deal_categories`
Many-to-many join table between deals and deal-categories.

| Column | Type | Description |
|--------|------|-------------|
| `deal_id` | UUID (FK) | References `deals(id)` |
| `deal_category_id` | UUID (FK) | References `deal_categories(id)` |

**Constraints:**
- Composite primary key `(deal_id, deal_category_id)`
- Foreign keys with CASCADE delete
- Validation trigger enforces business inheritance rule

### Indexes

```sql
-- Performance optimization for category lookups
CREATE INDEX idx_deal_categories_category_id ON deal_categories(category_id);

-- Performance optimization for deal-category joins
CREATE INDEX idx_deals_deal_categories_deal_category_id ON deals_deal_categories(deal_category_id);
```

## Validation Logic

A database trigger `validate_deal_category_assignment_trigger` enforces the inheritance rule:

```sql
-- Validates that deal_category.category_id = business.category_id
CREATE OR REPLACE FUNCTION validate_deal_category_assignment()
RETURNS TRIGGER AS $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM deals d
        JOIN businesses b ON d.business_id = b.id
        JOIN deal_categories dc ON dc.id = NEW.deal_category_id
        WHERE d.id = NEW.deal_id 
        AND b.category_id = dc.category_id
    ) THEN
        RAISE EXCEPTION 'Deal category assignment violates business rule';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

## Row Level Security (RLS)

Both new tables implement RLS policies that mirror the existing `deals` table permissions:

### `deal_categories` Policies
- **Read**: Everyone can view deal categories
- **Create/Update/Delete**: Only business owners within their macro-category

### `deals_deal_categories` Policies  
- **Read**: Everyone can view associations
- **Create/Update/Delete**: Only business owners for their own deals

## API Usage Examples

### 1. Fetch all deal-categories for a specific deal

```sql
SELECT dc.id, dc.name, dc.category_id
FROM deal_categories dc
JOIN deals_deal_categories ddc ON dc.id = ddc.deal_category_id
WHERE ddc.deal_id = 'your-deal-id-here';
```

### 2. Fetch all deals under a specific category via sub-categories

```sql
SELECT DISTINCT d.id, d.title, d.description, d.original_price, d.discounted_price
FROM deals d
JOIN deals_deal_categories ddc ON d.id = ddc.deal_id
JOIN deal_categories dc ON ddc.deal_category_id = dc.id
WHERE dc.category_id = 'your-category-id-here';
```

### 3. Create a new deal-category (business owner only)

```sql
INSERT INTO deal_categories (category_id, name)
VALUES ('macro-category-uuid', 'Sub-category Name');
```

### 4. Associate a deal with multiple sub-categories

```sql
INSERT INTO deals_deal_categories (deal_id, deal_category_id)
VALUES 
    ('deal-uuid', 'subcategory-1-uuid'),
    ('deal-uuid', 'subcategory-2-uuid');
```

## Implementation Notes

### Backward Compatibility
- Existing `deals.category_id` column remains unchanged
- Deals without sub-categories continue to work normally
- No breaking changes to existing APIs

### Performance Considerations
- Indexed foreign key relationships for fast joins
- Composite primary key prevents duplicate associations
- Trigger validation runs only on insert/update operations

### Future Enhancements
- Could add `description` field to `deal_categories`
- Could add `display_order` for UI sorting
- Could implement category hierarchy (sub-sub-categories)

## Migration Details

The feature was implemented via a single migration that:
1. Creates both new tables with proper constraints
2. Adds performance indexes
3. Implements validation trigger
4. Sets up RLS policies
5. Maintains full backward compatibility

**Migration File**: `supabase/migrations/[timestamp]-add-deal-subcategorization.sql`

## Testing Scenarios

1. **Valid Assignment**: Deal from restaurant business → restaurant sub-category ✅
2. **Invalid Assignment**: Deal from restaurant business → hotel sub-category ❌ (trigger blocks)
3. **Multiple Categories**: One deal → multiple valid sub-categories ✅
4. **No Categories**: Deal with no sub-categories ✅ (backward compatibility)
5. **Cascade Deletion**: Delete macro-category → sub-categories deleted ✅
6. **RLS Enforcement**: Business owner can only manage their categories ✅

## Error Handling

Common errors and their meanings:

- `Deal category assignment violates business rule`: Attempting to link deal to sub-category from different macro-category
- `duplicate key value violates unique constraint`: Sub-category name already exists in that macro-category
- `permission denied`: User doesn't have RLS permissions for the operation