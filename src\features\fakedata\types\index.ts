import { Database } from "@/integrations/supabase/types";

export type Booking = Database['public']['Tables']['bookings']['Row'] & {
  deals: {
    title: string;
    images: string[] | null;
    businesses: {
      name: string;
      address: string;
    } | null;
  } | null;
};

export type BusinessCategory = 'bar' | 'ristorante' | 'alimentari' | 'bellezza' | 'abbigliamento' | 'palestra' | 'spa' | 'cinema' | 'fioraio' | 'hotel' | 'teatro' | 'aperitivo' | 'default';

export interface CityCoordinates {
  name: string;
  latitude: { min: number; max: number };
  longitude: { min: number; max: number };
}

export interface BusinessLifecycle {
  establishedYear: number;
  businessAge: 'new' | 'established' | 'mature';
  successLevel: 'low' | 'medium' | 'high';
}

export interface SeasonalPattern {
  month: number;
  dealMultiplier: number;
  priceMultiplier: number;
}

export interface UserPersona {
  ageGroup: 'young' | 'adult' | 'senior';
  bookingFrequency: 'low' | 'medium' | 'high';
  preferredCategories: BusinessCategory[];
  locationPreference: 'local' | 'citywide';
}

export interface GenerationOptions {
  useSeasonality: boolean;
  useBusinessLifecycle: boolean;
  useRealisticClustering: boolean;
  useCustomerBehavior: boolean;
}

export interface FakeDataResult {
  created: number;
  errors: string[];
  warnings: string[];
}