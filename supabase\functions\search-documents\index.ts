
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.48.1'
import { Configuration, OpenAIApi } from 'https://esm.sh/openai@3.2.1'
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface RequestBody {
  query: string
  businessId: string
  searchType: 'text' | 'semantic' | 'both'
  limit?: number
}

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL') as string
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') as string
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY') as string
    
    if (!supabaseUrl || !supabaseServiceKey || !openaiApiKey) {
      throw new Error('Required environment variables are not set')
    }

    // Initialize Supabase client with service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    // Initialize OpenAI
    const configuration = new Configuration({ apiKey: openaiApiKey })
    const openai = new OpenAIApi(configuration)

    // Parse request body
    const { query, businessId, searchType = 'both', limit = 20 } = await req.json() as RequestBody
    
    if (!query || !businessId) {
      throw new Error('Query and business ID are required')
    }
    
    console.log(`Ricerca: "${query}" nell'attività: ${businessId}, tipo: ${searchType}`)
    
    let results = []
    
    // Perform text search
    if (searchType === 'text' || searchType === 'both') {
      console.log('Esecuzione ricerca testuale...')
      
      // Cerchiamo nella tabella document_embeddings per il testo usando ILIKE
      const { data: textResults, error: textError } = await supabase
        .from('document_embeddings')
        .select(`
          id,
          text_content,
          document_id,
          documents:document_id (
            id,
            name,
            file_url,
            file_type,
            file_size,
            created_at,
            business_id
          )
        `)
        .eq('documents.business_id', businessId)
        .ilike('text_content', `%${query}%`)
        .limit(limit);
      
      if (textError) {
        console.error("Errore ricerca testuale:", textError)
      } else if (textResults && textResults.length > 0) {
        console.log(`Ricerca testuale: trovati ${textResults.length} risultati`)
        
        const formattedResults = textResults.map(item => ({
          id: item.documents.id,
          name: item.documents.name,
          file_url: item.documents.file_url,
          file_type: item.documents.file_type,
          file_size: item.documents.file_size,
          created_at: item.documents.created_at,
          business_id: item.documents.business_id,
          text_content: item.text_content,
          match_type: 'text'
        }))
        
        results = [...formattedResults]
      }
    }
    
    // Perform semantic search with embeddings
    if ((searchType === 'semantic' || searchType === 'both') && results.length < limit) {
      try {
        console.log('Generazione embedding per la query...')
        // Generate embedding for query
        const embeddingResponse = await openai.createEmbedding({
          model: 'text-embedding-ada-002',
          input: query,
        })
        
        const [{ embedding }] = embeddingResponse.data.data
        
        console.log('Esecuzione ricerca semantica...')
        // Utilizziamo la funzione di somiglianza coseno (<=> operatore in pgvector)
        const { data: vectorResults, error: vectorError } = await supabase
          .from('document_embeddings')
          .select(`
            id,
            text_content,
            document_id,
            documents:document_id (
              id,
              name, 
              file_url,
              file_type,
              file_size,
              created_at,
              business_id
            ),
            (1 - (embedding <=> ${JSON.stringify(embedding)})) as similarity
          `)
          .eq('documents.business_id', businessId)
          .gt('1 - (embedding <=> ' + JSON.stringify(embedding) + ')', 0.6)
          .order('similarity', { ascending: false })
          .limit(limit)
        
        if (vectorError) {
          console.error("Errore ricerca semantica:", vectorError)
        } else if (vectorResults && vectorResults.length > 0) {
          console.log(`Ricerca semantica: trovati ${vectorResults.length} risultati`)
          
          // Se abbiamo sia risultati testuali che semantici, li fondiamo e rimuoviamo i duplicati
          if (results.length > 0) {
            const existingIds = new Set(results.map(doc => doc.id))
            
            const formattedVectorResults = vectorResults
              .filter(item => !existingIds.has(item.documents.id))
              .map(item => ({
                id: item.documents.id,
                name: item.documents.name,
                file_url: item.documents.file_url,
                file_type: item.documents.file_type,
                file_size: item.documents.file_size,
                created_at: item.documents.created_at,
                business_id: item.documents.business_id,
                text_content: item.text_content,
                similarity: item.similarity,
                match_type: 'semantic'
              }))
            
            results = [...results, ...formattedVectorResults]
          } else {
            results = vectorResults.map(item => ({
              id: item.documents.id,
              name: item.documents.name,
              file_url: item.documents.file_url,
              file_type: item.documents.file_type,
              file_size: item.documents.file_size,
              created_at: item.documents.created_at,
              business_id: item.documents.business_id,
              text_content: item.text_content,
              similarity: item.similarity,
              match_type: 'semantic'
            }))
          }
        }
      } catch (error) {
        console.error("Errore nella generazione dell'embedding:", error)
      }
    }
    
    // If we couldn't perform either search or got no results, fall back to a simple filter
    if (results.length === 0) {
      console.log('Nessun risultato trovato, utilizzo ricerca basica su nome file...')
      const { data: fallbackResults, error: fallbackError } = await supabase
        .from('documents')
        .select('*')
        .eq('business_id', businessId)
        .ilike('name', `%${query}%`)
        .limit(limit)
      
      if (!fallbackError && fallbackResults && fallbackResults.length > 0) {
        console.log(`Ricerca basica: trovati ${fallbackResults.length} risultati`)
        results = [...fallbackResults.map(doc => ({ ...doc, match_type: 'fallback' }))]
      }
    }
    
    return new Response(
      JSON.stringify({ 
        success: true, 
        results,
        count: results.length,
        query: query,
        searchType: searchType
      }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Errore nella ricerca dei documenti:', error)
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Si è verificato un errore sconosciuto',
      }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
        status: 500,
      }
    )
  }
})
