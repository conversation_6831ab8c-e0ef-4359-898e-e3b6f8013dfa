
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { supabase } from "@/integrations/supabase/client"
import type { Database } from "@/integrations/supabase/types"
import { toast } from "sonner"

// Type for business data from the businesses_with_counts view
type BusinessWithCounts = Database['public']['Views']['businesses_with_counts']['Row']

// Business type for the application
export interface Business {
  id: string
  name: string
  description: string | null
  deal_count: number
  booking_count: number
  pending_booking_count: number
  created_at?: string
  owner_id: string
}

interface BusinessState {
  businesses: Business[]
  selectedBusiness: Business | null
  isLoading: boolean
  fetchBusinesses: (userId: string) => Promise<void>
  selectBusiness: (business: Business) => void
  setDefaultBusiness: (userId: string, business: Business) => Promise<boolean>
}

export const useBusinessStore = create<BusinessState>()(
  persist(
    (set, get) => ({
      businesses: [],
      selectedBusiness: null,
      isLoading: false,
      
      fetchBusinesses: async (userId: string) => {
        set({ isLoading: true })
        try {
          const { data, error } = await supabase
            .from('businesses_with_counts')
            .select('*')
            .eq('owner_id', userId)
            .order('name', { ascending: true })
          
          if (error) {
            console.error('Error fetching businesses:', error)
            throw error
          }
          
          const typedBusinesses: Business[] = (data || []).map((item: BusinessWithCounts) => ({
            id: item.id,
            name: item.name,
            description: item.description,
            deal_count: item.deal_count || 0,
            booking_count: item.booking_count || 0,
            pending_booking_count: item.pending_booking_count || 0,
            created_at: item.created_at,
            owner_id: item.owner_id
          }))
         
          set({ businesses: typedBusinesses })
          
          // Handle selectedBusiness based on available businesses
          const { selectedBusiness } = get()
          
          if (typedBusinesses.length === 0) {
            // Clear selectedBusiness if user has no businesses
            set({ selectedBusiness: null })
            localStorage.removeItem('selectedBusinessId')
          } else if (!selectedBusiness || !typedBusinesses.find(b => b.id === selectedBusiness.id)) {
            // Set business if none selected or current selection is not in the list
            const savedBusinessId = localStorage.getItem('selectedBusinessId')
            
            // Try to find the saved business in the list
            const savedBusiness = savedBusinessId 
              ? typedBusinesses.find(b => b.id === savedBusinessId)
              : typedBusinesses[0]
              
            set({ selectedBusiness: savedBusiness || typedBusinesses[0] })
          }
          
        } catch (error) {
          console.error('Error in fetchBusinesses:', error)
          set({ businesses: [] })
        } finally {
          set({ isLoading: false })
        }
      },
      
      selectBusiness: (business: Business) => {
        set({ selectedBusiness: business })
        localStorage.setItem('selectedBusinessId', business.id)
      },
      
      setDefaultBusiness: async (userId: string, business: Business) => {
        try {
          console.log(`Setting default business for user ${userId} to ${business.id} (${business.name})`)
          
          const { error } = await supabase
            .from('user_details')
            .update({ default_business_id: business.id })
            .eq('id', userId)
          
          if (error) {
            console.error('Error setting default business:', error)
            toast.error("Errore nell'impostazione dell'attività predefinita")
            return false
          }
          
          // Also update localStorage to ensure consistency
          localStorage.setItem('selectedBusinessId', business.id)
          
          // Update selected business
          set({ selectedBusiness: business })
          
          toast.success(`${business.name} impostata come attività predefinita`)
          return true
        } catch (error) {
          console.error('Error in setDefaultBusiness:', error)
          toast.error("Errore nell'impostazione dell'attività predefinita")
          return false
        }
      }
    }),
    {
      name: 'business-storage',
      partialize: (state) => ({ 
        selectedBusiness: state.selectedBusiness 
      })
    }
  )
)
