import { WeeklySchedule } from "@/types/deals";

export const daysNames = {
  1: "luned<PERSON>", 
  2: "martedì",
  3: "mercoledì",
  4: "gioved<PERSON>",
  5: "venerd<PERSON>",
  6: "sabato",
  7: "domenica"
};

export const DAYS_OF_WEEK = [
  { value: 1, label: "Lunedì" },
  { value: 2, label: "Martedì" },
  { value: 3, label: "Mercoledì" },
  { value: 4, label: "Giovedì" },
  { value: 5, label: "Venerdì" },
  { value: 6, label: "Sabato" },
  { value: 7, label: "Domenica" }
];

export const INITIAL_SCHEDULE: WeeklySchedule = {
  schedule: [
    { day: 1, day_name: "Lunedì", time_slots: [] },
    { day: 2, day_name: "Marted<PERSON>", time_slots: [] },
    { day: 3, day_name: "Mercoledì", time_slots: [] },
    { day: 4, day_name: "<PERSON><PERSON><PERSON>ì", time_slots: [] },
    { day: 5, day_name: "Venerdì", time_slots: [] },
    { day: 6, day_name: "Sabato", time_slots: [] },
    { day: 7, day_name: "Domenica", time_slots: [] }
  ],
  exceptions: []
};

// Category-specific operating hours
export const categoryOperatingHours = {
  bar: {
    weekdays: { open: "06:00", close: "22:00" },
    weekends: { open: "07:00", close: "00:00" },
    closedDays: []
  },
  ristorante: {
    weekdays: { open: "12:00", close: "23:00" },
    weekends: { open: "12:00", close: "00:00" },
    closedDays: [1] // Lunedì
  },
  alimentari: {
    weekdays: { open: "08:00", close: "19:30" },
    weekends: { open: "08:30", close: "13:00" },
    closedDays: [7] // Domenica
  },
  bellezza: {
    weekdays: { open: "09:00", close: "19:00" },
    weekends: { open: "09:00", close: "18:00" },
    closedDays: [1] // Lunedì
  },
  abbigliamento: {
    weekdays: { open: "10:00", close: "19:30" },
    weekends: { open: "10:00", close: "20:00" },
    closedDays: [1] // Lunedì mattina
  },
  palestra: {
    weekdays: { open: "06:00", close: "23:00" },
    weekends: { open: "08:00", close: "21:00" },
    closedDays: []
  },
  spa: {
    weekdays: { open: "09:00", close: "20:00" },
    weekends: { open: "09:00", close: "21:00" },
    closedDays: [1] // Lunedì
  }
} as const;