import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Search, Users, Building2, Mail, Phone, Calendar, ExternalLink, Send, SendHorizontal } from "lucide-react";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import { useEmailTemplates } from "@/hooks/useEmailTemplates";
import MainLayout from "@/layouts/MainLayout";

interface WaitlistEntry {
  id: string;
  campaign_id: string;
  email: string;
  first_name: string;
  last_name: string;
  company: string | null;
  phone: string | null;
  role: string | null;
  interests: string | null;
  isbusiness: boolean | null;
  marketing_consent: boolean | null;
  linkedin: string | null;
  telegram: string | null;
  whatsapp: string | null;
  created_at: string | null;
  updated_at: string | null;
  email_sent: boolean | null;
  email_sent_at: string | null;
}

const AdminWaitlist = () => {
  const [waitlistEntries, setWaitlistEntries] = useState<WaitlistEntry[]>([]);
  const [filteredEntries, setFilteredEntries] = useState<WaitlistEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filter, setFilter] = useState<"all" | "business" | "individual">("all");
  const { toast } = useToast();
  const { sendEmail } = useEmailTemplates();

  useEffect(() => {
    fetchWaitlistEntries();
  }, []);

  useEffect(() => {
    filterEntries();
  }, [waitlistEntries, searchTerm, filter]);

  const fetchWaitlistEntries = async () => {
    try {
      const { data, error } = await supabase
        .from('campaign_waitlist')
        .select(`
          *,
          campaigns!inner(
            template_id,
            email_templates(*)
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setWaitlistEntries(data || []);
    } catch (error) {
      console.error('Error fetching waitlist:', error);
      toast({
        title: "Errore",
        description: "Impossibile caricare la lista d'attesa",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filterEntries = () => {
    let filtered = waitlistEntries;

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(entry =>
        entry.email.toLowerCase().includes(term) ||
        entry.first_name.toLowerCase().includes(term) ||
        entry.last_name.toLowerCase().includes(term) ||
        (entry.company && entry.company.toLowerCase().includes(term)) ||
        (entry.role && entry.role.toLowerCase().includes(term))
      );
    }

    // Filter by type
    if (filter === "business") {
      filtered = filtered.filter(entry => entry.isbusiness === true);
    } else if (filter === "individual") {
      filtered = filtered.filter(entry => entry.isbusiness === false);
    }

    setFilteredEntries(filtered);
  };

  const sendIndividualEmail = async (entry: WaitlistEntry) => {
    try {
      setIsSending(entry.id);
      
      // Get campaign and template info
      const { data: campaign, error: campaignError } = await supabase
        .from('campaigns')
        .select('template_id, email_templates(*)')
        .eq('id', entry.campaign_id)
        .single();

      if (campaignError || !campaign) {
        throw new Error('Campagna o template non trovato');
      }

      if (!campaign.template_id || !campaign.email_templates) {
        throw new Error('Template email non configurato per questa campagna');
      }

      // Send email
      const variables = {
        first_name: entry.first_name,
        last_name: entry.last_name,
        email: entry.email,
        company: entry.company || '',
      };

      const result = await sendEmail({
        templateId: campaign.template_id,
        toEmail: entry.email,
        variables,
      });

      if (result.success) {
        // Update waitlist entry
        const { error: updateError } = await supabase
          .from('campaign_waitlist')
          .update({
            email_sent: true,
            email_sent_at: new Date().toISOString(),
          })
          .eq('id', entry.id);

        if (updateError) {
          console.error('Error updating waitlist entry:', updateError);
        }

        // Refresh the list
        fetchWaitlistEntries();
        
        toast({
          title: "Email inviata",
          description: `Email inviata con successo a ${entry.email}`,
        });
      }
    } catch (error: any) {
      console.error('Error sending individual email:', error);
      toast({
        title: "Errore",
        description: error.message || "Errore nell'invio dell'email",
        variant: "destructive",
      });
    } finally {
      setIsSending(null);
    }
  };

  const sendBulkEmails = async () => {
    const unsentEntries = filteredEntries.filter(entry => !entry.email_sent);
    
    if (unsentEntries.length === 0) {
      toast({
        title: "Nessuna email da inviare",
        description: "Tutti gli utenti hanno già ricevuto l'email",
      });
      return;
    }

    try {
      setIsSending('bulk');
      
      let successCount = 0;
      let errorCount = 0;

      for (const entry of unsentEntries) {
        try {
          // Get campaign and template info
          const { data: campaign, error: campaignError } = await supabase
            .from('campaigns')
            .select('template_id, email_templates(*)')
            .eq('id', entry.campaign_id)
            .single();

          if (campaignError || !campaign || !campaign.template_id) {
            console.error(`Template not found for entry ${entry.id}`);
            errorCount++;
            continue;
          }

          // Send email
          const variables = {
            first_name: entry.first_name,
            last_name: entry.last_name,
            email: entry.email,
            company: entry.company || '',
          };

          const result = await sendEmail({
            templateId: campaign.template_id,
            toEmail: entry.email,
            variables,
          });

          if (result.success) {
            // Update waitlist entry
            await supabase
              .from('campaign_waitlist')
              .update({
                email_sent: true,
                email_sent_at: new Date().toISOString(),
              })
              .eq('id', entry.id);

            successCount++;
          } else {
            errorCount++;
          }

          // Small delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          console.error(`Error sending email to ${entry.email}:`, error);
          errorCount++;
        }
      }

      // Refresh the list
      fetchWaitlistEntries();
      
      toast({
        title: "Invio completato",
        description: `${successCount} email inviate con successo, ${errorCount} errori`,
      });
    } catch (error: any) {
      console.error('Error in bulk email send:', error);
      toast({
        title: "Errore",
        description: "Errore nell'invio delle email",
        variant: "destructive",
      });
    } finally {
      setIsSending(null);
    }
  };

  const exportToCSV = () => {
    const headers = [
      'Email', 'Nome', 'Cognome', 'Azienda', 'Ruolo', 'Telefono', 
      'Tipo', 'Interessi', 'Marketing Consent', 'Data Registrazione'
    ];
    
    const csvData = filteredEntries.map(entry => [
      entry.email,
      entry.first_name,
      entry.last_name,
      entry.company || '',
      entry.role || '',
      entry.phone || '',
      entry.isbusiness ? 'Business' : 'Individuale',
      entry.interests || '',
      entry.marketing_consent ? 'Sì' : 'No',
      entry.created_at ? format(new Date(entry.created_at), 'dd/MM/yyyy HH:mm') : ''
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `waitlist-${format(new Date(), 'yyyy-MM-dd')}.csv`;
    link.click();
  };

  const stats = {
    total: waitlistEntries.length,
    business: waitlistEntries.filter(e => e.isbusiness === true).length,
    individual: waitlistEntries.filter(e => e.isbusiness === false).length,
    withConsent: waitlistEntries.filter(e => e.marketing_consent === true).length,
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto p-6">
          <div className="flex items-center justify-center min-h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Lista d'Attesa</h1>
            <p className="text-muted-foreground">Monitora e gestisci gli utenti in lista d'attesa</p>
          </div>
          <div className="flex gap-2">
            <Button 
              onClick={sendBulkEmails} 
              variant="default"
              disabled={isSending === 'bulk' || filteredEntries.filter(e => !e.email_sent).length === 0}
            >
              {isSending === 'bulk' ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground mr-2"></div>
                  Invio in corso...
                </>
              ) : (
                <>
                  <SendHorizontal className="h-4 w-4 mr-2" />
                  Invia a tutti ({filteredEntries.filter(e => !e.email_sent).length})
                </>
              )}
            </Button>
            <Button onClick={exportToCSV} variant="outline">
              Esporta CSV
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Totale Iscritti</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Business</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.business}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Individuali</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.individual}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Con Consenso Marketing</CardTitle>
              <Mail className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.withConsent}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Filtri</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cerca per email, nome, azienda..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={filter === "all" ? "default" : "outline"}
                onClick={() => setFilter("all")}
                size="sm"
              >
                Tutti ({stats.total})
              </Button>
              <Button
                variant={filter === "business" ? "default" : "outline"}
                onClick={() => setFilter("business")}
                size="sm"
              >
                Business ({stats.business})
              </Button>
              <Button
                variant={filter === "individual" ? "default" : "outline"}
                onClick={() => setFilter("individual")}
                size="sm"
              >
                Individuali ({stats.individual})
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Waitlist Table */}
        <Card>
          <CardHeader>
            <CardTitle>Lista d'Attesa</CardTitle>
            <CardDescription>
              {filteredEntries.length} di {waitlistEntries.length} iscritti
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nome</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Azienda</TableHead>
                    <TableHead>Tipo</TableHead>
                    <TableHead>Contatti</TableHead>
                    <TableHead>Marketing</TableHead>
                    <TableHead>Data Registrazione</TableHead>
                    <TableHead>Stato</TableHead>
                    <TableHead>Azioni</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredEntries.map((entry) => (
                    <TableRow key={entry.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{entry.first_name} {entry.last_name}</div>
                          {entry.role && (
                            <div className="text-sm text-muted-foreground">{entry.role}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <a 
                            href={`mailto:${entry.email}`}
                            className="text-sm hover:underline"
                          >
                            {entry.email}
                          </a>
                        </div>
                      </TableCell>
                      <TableCell>
                        {entry.company ? (
                          <div className="flex items-center gap-2">
                            <Building2 className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">{entry.company}</span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant={entry.isbusiness ? "default" : "secondary"}>
                          {entry.isbusiness ? "Business" : "Individuale"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          {entry.phone && (
                            <div className="flex items-center gap-1 text-sm">
                              <Phone className="h-3 w-3" />
                              <a href={`tel:${entry.phone}`} className="hover:underline">
                                {entry.phone}
                              </a>
                            </div>
                          )}
                          {entry.linkedin && (
                            <div className="flex items-center gap-1 text-sm">
                              <ExternalLink className="h-3 w-3" />
                              <a 
                                href={entry.linkedin} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="hover:underline"
                              >
                                LinkedIn
                              </a>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={entry.marketing_consent ? "default" : "secondary"}>
                          {entry.marketing_consent ? "Sì" : "No"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {entry.created_at && (
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">
                              {format(new Date(entry.created_at), 'dd/MM/yyyy HH:mm')}
                            </span>
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          <Badge variant={entry.email_sent ? "default" : "secondary"}>
                            {entry.email_sent ? "Inviata" : "In attesa"}
                          </Badge>
                          {entry.email_sent && entry.email_sent_at && (
                            <span className="text-xs text-muted-foreground">
                              {format(new Date(entry.email_sent_at), 'dd/MM/yyyy HH:mm')}
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {!entry.email_sent && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => sendIndividualEmail(entry)}
                            disabled={isSending === entry.id}
                          >
                            {isSending === entry.id ? (
                              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary"></div>
                            ) : (
                              <Send className="h-3 w-3" />
                            )}
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              
              {filteredEntries.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  Nessun iscritto trovato con i filtri selezionati
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default AdminWaitlist;