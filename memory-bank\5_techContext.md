# Technical Context

## Technology Stack

### Frontend
- **Framework**: React with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **UI Component Library**: shadcn/ui (based on Radix UI)
- **Form Management**: React Hook Form with Zod validation
- **State Management**: 
  - React Query for server state
  - Zustand for persistent application state
  - Context API for transient application state
- **Routing**: React Router v6

### Backend
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: Supabase Auth
- **Storage**: Supabase Storage
- **API**: Supabase Client with RLS policies
- **Functions**: Supabase Edge Functions (optional)

### DevOps
- **Version Control**: Git
- **Package Manager**: npm/pnpm
- **Deployment**: Lovable platform with automatic deployments
- **CI/CD**: GitHub Actions

## Development Setup

### Prerequisites
- Node.js (v16+)
- npm or pnpm
- Git

### Local Development
1. Clone the repository
2. Install dependencies with `npm install` or `pnpm install`
3. Start the development server with `npm run dev` or `pnpm dev`
4. Access the application at `http://localhost:5173`

### Environment Configuration
Key environment variables are stored in `.env` files and loaded through Vite:
- `VITE_SUPABASE_URL`: Supabase project URL
- `VITE_SUPABASE_ANON_KEY`: Supabase anonymous key for client-side operations

## Technical Constraints

### Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- No explicit support for IE11 or older browsers

### Performance Targets
- Initial load time < 2 seconds
- Time to interactive < 3 seconds
- Smooth transitions and animations (60fps)

### Security Requirements
- HTTPS only
- JWT-based authentication
- Strict CSP policies
- Row-level security for data access
- Input validation on all forms

### Accessibility Standards
- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- Proper focus management

## Dependencies

### Core Dependencies
- React & React DOM: UI rendering
- TypeScript: Type safety
- Vite: Build tool and development server
- Tailwind CSS: Utility-first CSS framework
- shadcn/ui: Component library
- React Router: Navigation and routing
- React Hook Form & Zod: Form handling and validation
- React Query: Data fetching and caching
- Zustand: State management with persistence
- Supabase JS Client: Database and auth integration

### Development Dependencies
- ESLint: Code linting
- TypeScript ESLint: TypeScript-specific linting
- Vite Plugin React SWC: Fast React compilation
- Tailwind CSS Plugins: Typography and animations

## Database Schema

```mermaid
erDiagram
    users {
        uuid id PK
        timestamp created_at
        string email
    }
    
    user_details {
        uuid id PK
        uuid user_id FK
        string first_name
        string last_name
        string display_name
        string phone_number
        string avatar_url
        timestamp registered_at
    }
    
    businesses {
        uuid id PK
        uuid owner_id FK
        string name
        string description
        string logo_url
        string contact_email
        string phone_number
        timestamp created_at
    }
    
    deals {
        uuid id PK
        uuid business_id FK
        string title
        string description
        float original_price
        float discount_percentage
        float discounted_price
        timestamp start_date
        timestamp end_date
        json time_slots
        timestamp created_at
    }
    
    bookings {
        uuid id PK
        uuid user_id FK
        uuid deal_id FK
        timestamp booking_date
        string status
        timestamp created_at
    }
    
    users ||--o{ user_details : "has"
    users ||--o{ businesses : "owns"
    businesses ||--o{ deals : "offers"
    deals ||--o{ bookings : "receives"
    users ||--o{ bookings : "makes"
```

## Integration Points

### Supabase Integration
- Authentication via Supabase Auth
- Database access via Supabase Client
- File storage for avatars and business images
- Real-time subscriptions for updates

### State Management Implementation
- **Zustand Stores**:
  - `businessStore`: Manages business selection and business data
  - Future plans for additional stores (user preferences, app settings)
- **Persistence Layer**:
  - Zustand/middleware persist for local storage integration
  - Selective persistence of critical state
- **Performance Considerations**:
  - Selective state subscriptions to minimize re-renders
  - Shallow equality checks for state comparisons
  - Devtools integration for state debugging

### Future Integrations
- Payment processing (Stripe/PayPal)
- Email notifications (SendGrid/Mailchimp)
- Calendar integration (Google Calendar/Apple Calendar)
- Analytics (Google Analytics/Mixpanel)

## Technical Debt

### Current Technical Debt
- Complete migration from localStorage to Zustand stores
- Improve error handling for API calls
- Enhance form validation for complex scenarios
- Optimize bundle size with code splitting
- Implement comprehensive testing
- Add complete TypeScript types for all components

### Technical Improvement Opportunities
- Expand Zustand usage for other application state
- Server-side rendering for improved SEO
- Progressive Web App capabilities
- Advanced caching strategies
- GraphQL implementation for optimized data fetching
- Automated end-to-end testing
