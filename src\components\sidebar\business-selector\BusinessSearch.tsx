
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

interface BusinessSearchProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}

export const BusinessSearch = ({ searchTerm, setSearchTerm }: BusinessSearchProps) => {
  // Stoppa la propagazione dell'evento per evitare la chiusura del dropdown quando si clicca nella casella di ricerca
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.stopPropagation();
    setSearchTerm(e.target.value);
  };

  return (
    <div className="px-2 py-1.5">
      <div 
        className="flex items-center px-2 py-1 border rounded-md focus-within:ring-1 focus-within:ring-blue-500 bg-background"
        onClick={handleClick}
      >
        <Search className="h-4 w-4 mr-2 text-muted-foreground" />
        <Input
          type="text"
          placeholder="Cerca attività..."
          value={searchTerm}
          onChange={handleChange}
          onClick={handleClick}
          className="h-8 p-0 border-0 focus-visible:ring-0 focus-visible:ring-offset-0 text-sm"
        />
      </div>
    </div>
  );
};
