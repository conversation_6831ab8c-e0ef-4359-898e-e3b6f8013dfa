import { useState } from "react";
import FloatingMenuBar from "../FloatingMenuBar";
import { Home, Info, Settings, Mail, User, Book, Phone } from "lucide-react";

const FloatingMenuBarExample = () => {
  const [activeSection, setActiveSection] = useState("home");

  // Esempio di callback personalizzata
  const handleMenuItemClick = (itemId: string) => {
    console.log(`Clicked on ${itemId}`);
    setActiveSection(itemId);
    
    // Esempio: navigazione programmatica
    const element = document.getElementById(itemId);
    if (element) {
      window.scrollTo({
        top: element.offsetTop - 80,
        behavior: "smooth",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Esempio base */}
      <div className="mb-20 pt-20">
        <h2 className="text-2xl font-bold mb-4 text-center">Esempio Base</h2>
        <FloatingMenuBar
          items={[
            { id: "home", label: "Home" },
            { id: "about", label: "Chi Siamo" },
            { id: "services", label: "Servizi" },
            { id: "contact", label: "Contatti" },
          ]}
          activeItemId={activeSection}
          topOffset={80}
        />
      </div>

      {/* Esempio con icone */}
      <div className="mb-20 pt-20">
        <h2 className="text-2xl font-bold mb-4 text-center">Esempio con Icone</h2>
        <FloatingMenuBar
          items={[
            { id: "home", label: "Home", icon: <Home className="w-4 h-4" /> },
            { id: "about", label: "Chi Siamo", icon: <Info className="w-4 h-4" /> },
            { id: "services", label: "Servizi", icon: <Settings className="w-4 h-4" /> },
            { id: "contact", label: "Contatti", icon: <Mail className="w-4 h-4" /> },
            { id: "profile", label: "Profilo", icon: <User className="w-4 h-4" /> },
            { id: "blog", label: "Blog", icon: <Book className="w-4 h-4" /> },
            { id: "support", label: "Supporto", icon: <Phone className="w-4 h-4" /> },
          ]}
          activeItemId={activeSection}
          onItemClick={handleMenuItemClick}
          className="top-40" // Posizionamento personalizzato
        />
      </div>

      {/* Esempio con colori personalizzati */}
      <div className="mb-20 pt-20">
        <h2 className="text-2xl font-bold mb-4 text-center">Esempio con Colori Personalizzati</h2>
        <FloatingMenuBar
          items={[
            { id: "home", label: "Home" },
            { id: "about", label: "Chi Siamo" },
            { id: "services", label: "Servizi" },
            { id: "contact", label: "Contatti" },
          ]}
          activeItemId={activeSection}
          primaryColor="bg-blue-100"
          primaryDarkColor="text-blue-800"
          primaryLightColor="border-blue-100"
          textColor="text-gray-700"
          hoverTextColor="hover:bg-gray-200"
          className="top-60" // Posizionamento personalizzato
        />
      </div>

      {/* Sezioni di esempio per la navigazione */}
      <section id="home" className="h-screen flex items-center justify-center bg-gray-100">
        <h1 className="text-4xl font-bold">Home Section</h1>
      </section>
      
      <section id="about" className="h-screen flex items-center justify-center bg-gray-200">
        <h1 className="text-4xl font-bold">About Section</h1>
      </section>
      
      <section id="services" className="h-screen flex items-center justify-center bg-gray-100">
        <h1 className="text-4xl font-bold">Services Section</h1>
      </section>
      
      <section id="contact" className="h-screen flex items-center justify-center bg-gray-200">
        <h1 className="text-4xl font-bold">Contact Section</h1>
      </section>
    </div>
  );
};

export default FloatingMenuBarExample;
