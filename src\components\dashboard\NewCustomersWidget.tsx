
import React from 'react';
import { Widget as WidgetType, WidgetType as WidgetTypes } from '@/types/widget';
import WidgetComponent from '@/components/dashboard/Widget';
import { Users, TrendingUp, TrendingDown } from 'lucide-react';
import { formatNumberWithCommas } from '@/utils/formatters';
import { cn } from '@/lib/utils';

interface NewCustomersWidgetProps {
  widget: WidgetType;
  className?: string;
  onRemove?: () => void;
}

const NewCustomersWidget: React.FC<NewCustomersWidgetProps> = ({ widget, className, onRemove }) => {
  if (widget.type !== WidgetTypes.NEW_CUSTOMERS) {
    console.error('Tipo di widget errato passato a NewCustomersWidget');
    return null;
  }

  const { count, percentChange, period } = widget.data;
  const isPositiveChange = percentChange === undefined ? undefined : percentChange >= 0;

  return (
    <WidgetComponent
      widget={widget}
      className={className}
      onRemove={onRemove}
      actionLabel="Visualizza dettagli"
      actionLink="/users"
    >
      <div className="h-full flex flex-col justify-between">
        <div className="mt-2">
          <div className="text-3xl font-bold">
            {widget.isLoading ? (
              <div className="animate-pulse h-8 w-24 bg-gray-200 rounded"></div>
            ) : (
              formatNumberWithCommas(count)
            )}
          </div>
          <div className="text-sm text-muted-foreground mt-1">
            {widget.isLoading ? (
              <div className="animate-pulse h-3 w-32 bg-gray-200 rounded"></div>
            ) : (
              `Nuovi clienti negli ultimi ${period || '30 giorni'}`
            )}
          </div>
        </div>

        {percentChange !== undefined && (
          <div className={cn(
            "flex items-center gap-1 mt-4 text-sm font-medium",
            isPositiveChange ? "text-green-600" : "text-red-600"
          )}>
            {isPositiveChange ? (
              <TrendingUp className="h-4 w-4" />
            ) : (
              <TrendingDown className="h-4 w-4" />
            )}
            <span>
              {isPositiveChange ? '+' : ''}{percentChange.toFixed(1)}% rispetto al periodo precedente
            </span>
          </div>
        )}
      </div>
    </WidgetComponent>
  );
};

export default NewCustomersWidget;
