import ControlPanel from "@/components/google-maps/control-panel";
import { PlaceDetailsMarker } from "@/components/google-maps/place-details-marker";
import { PlaceListWebComponent } from "@/components/google-maps/place-list-webcomponent";
import { SearchBar } from "@/components/google-maps/search-bar";
import { supabase } from "@/integrations/supabase/client";
import MainLayout from "@/layouts/MainLayout";
import { DetailsSize, PlaceType } from "@/types/google-maps";
import { APIProvider, Map } from "@vis.gl/react-google-maps";
import { MapPin } from "lucide-react";
import { useEffect, useMemo, useState } from "react";


const AdminSearchMap = () => {
  const [places, setPlaces] = useState<google.maps.places.Place[]>([]);
  const [selectedPlaceId, setSelectedPlaceId] = useState<string | null>(null);
  const [locationId, setLocationId] = useState<string | null>(null);
  const [placeType, setPlaceType] = useState<PlaceType>("restaurant");
  const [detailsSize, setDetailsSize] = useState<DetailsSize>("FULL");

  const [API_KEY, setAPI_KEY] = useState<string>("");
  const [center, setCenter] = useState<{
    lat: number;
    lng: number;
  }>({
    lat: 45.4671,
    lng: 9.1526,
  });

  // Check for Google Maps version and reload if needed
  useEffect(() => {
    const nonAlphaVersionLoaded = Boolean(
      globalThis &&
        globalThis.google?.maps?.version &&
        !globalThis.google?.maps?.version.endsWith("-alpha")
    );
    if (nonAlphaVersionLoaded) {
      location.reload();
      return;
    }
  }, []);

  useEffect(() => {
    const getGoogleMapsKey = async () => {
      const {
        data: { GOOGLE_MAPS_API_KEY },
        error,
      } = await supabase.functions.invoke("get-google-maps-key");
      if (error) {
        console.error("Errore nel recupero della chiave API:", error);
        return;
      }
      setAPI_KEY(GOOGLE_MAPS_API_KEY);
    };
    getGoogleMapsKey();
  }, []);

  // Memoize the place markers to prevent unnecessary re-renders
  // Only recreate when places, selection, or details size changes
  const placeMarkers = useMemo(() => {
    return places.map((place, index) => (
      <PlaceDetailsMarker
        detailsSize={detailsSize}
        key={place.id || index}
        selected={place.id === selectedPlaceId}
        place={place}
        onClick={() => setSelectedPlaceId(place.id)}
      />
    ));
  }, [places, selectedPlaceId, detailsSize]);

  const MAP_CONFIG = {
    defaultZoom: 15,
    defaultCenter: { lat: 53.55, lng: 9.99 },
    mapId: "49ae42fed52588c3",
    gestureHandling: "greedy" as const,
    disableDefaultUI: true,
    clickableIcons: false,
  };

  // Conditional rendering after all hooks
  if (!API_KEY) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex items-center justify-center h-[calc(100vh-120px)]">
          <div className="animate-spin">
            <MapPin className="h-8 w-8 text-brand-primary" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <MainLayout>
      <div className="flex flex-col h-[calc(100vh-200px)]">
        <div className="flex items-center justify-between p-4 border-b bg-white">
          <h1 className="text-2xl font-semibold">Admin Search Map</h1>
          <div className="text-xs text-gray-500">API Key: {API_KEY.substring(0, 10)}...</div>
        </div>
        
        <APIProvider apiKey={API_KEY} version="alpha">
          <div className="places-ui-kit flex-1">
            {/* Place List Sidebar */}
            <div className="place-list-wrapper">
              {/*
              PlaceListWebComponent displays a list of places based on:
              - The selected place type (restaurant, cafe, etc.)
              - The current map location and bounds
            */}
              <PlaceListWebComponent
                placeType={placeType}
                locationId={locationId}
                setPlaces={setPlaces}
                onPlaceSelect={(place) => setSelectedPlaceId(place?.id ?? null)}
              />
            </div>

            {/* Map Container */}
            <div className="map-container">
              {/*
              The Map component renders the Google Map
              Clicking on the map background will deselect any selected place
            */}
              <Map
                style={{ width: "100%", height: "100%" }}
                defaultCenter={center}
                defaultZoom={14}
                gestureHandling={"greedy"}
                disableDefaultUI={true}
                mapTypeControl={false}
                streetViewControl={false}
                fullscreenControl={false}
                mapId="49ae42fed52588c3"
                onClick={() => setSelectedPlaceId(null)}
              >
                {/* Render the place markers inside the Map */}
                {placeMarkers}
              </Map>

              {/*
              SearchBar allows users to:
              - Select the type of place they want to find
              - Search for a specific location to center the map on
            */}
              <SearchBar
                placeType={placeType}
                setPlaceType={setPlaceType}
                setLocationId={setLocationId}
              />

              {/*
              ControlPanel provides UI controls for adjusting the size of place details
              displayed in the InfoWindow
            */}
              <ControlPanel
                detailsSize={detailsSize}
                onDetailSizeChange={setDetailsSize}
              />
            </div>
          </div>
        </APIProvider>
      </div>
    </MainLayout>
  );
};

export default AdminSearchMap;
